# Rest of your ignore patterns
.git/
.github/
.gitignore
docs/
**/logs/
**/runs/
**/output/*
**/outputs/*
**/videos/*
*.tmp
docker/cluster/exports/
docker/.container.cfg
recordings/
**/__pycache__/
**/*.egg-info/
_isaac_sim?
wandb/
# ignore isaac lab heavy folders
_isaaclab/_isaac_sim
_isaaclab/source
_isaaclab/.git
_isaaclab/docs
/source/moleworks_ext/moleworks_ext/ros

# Specify the models you are not using to optimize synchronization with the cluster
# and reduce data usage. For example:
# source/moleworks_ext/moleworks_ext/rsc/sim/model/*
# !source/moleworks_ext/moleworks_ext/rsc/sim/model/m545_arm_spread
source/moleworks_ext/moleworks_ext/rsc/sim/model/m545_arm_spread
source/moleworks_ext/moleworks_ext/rsc/sim/model/m545_arm_spread_w_cabin
source/moleworks_ext/moleworks_ext/rsc/sim/model/m545
source/moleworks_ext/moleworks_ext/rsc/sim/model/m545_arm_spread_w_shovel
source/moleworks_ext/moleworks_ext/rsc/driving/terrains
