#!/bin/bash
export SCRIPT_DIR=/workspace/isaaclab/_isaac_sim
export CARB_APP_PATH=$SCRIPT_DIR/kit
export ISAAC_PATH=$SCRIPT_DIR
export EXP_PATH=$SCRIPT_DIR/apps

export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$SCRIPT_DIR/../../../$LD_LIBRARY_PATH:$SCRIPT_DIR/.:$SCRIPT_DIR/source/omni.usd.schema.isaac/plugins/IsaacSensorSchema/lib:$SCRIPT_DIR/source/omni.usd.schema.isaac/plugins/RangeSensorSchema/lib:$SCRIPT_DIR/source/omni.isaac.lula/pip_prebundle:$SCRIPT_DIR/source/omni.exporter.urdf/pip_prebundle:$SCRIPT_DIR/kit:$SCRIPT_DIR/kit/kernel/plugins:$SCRIPT_DIR/kit/libs/iray:$SCRIPT_DIR/kit/plugins:$SCRIPT_DIR/kit/plugins/bindings-python:$SCRIPT_DIR/kit/plugins/carb_gfx:$SCRIPT_DIR/kit/plugins/rtx:$SCRIPT_DIR/kit/plugins/gpu.foundation
export PYTHONPATH=$PYTHONPATH:$SCRIPT_DIR/../../../$PYTHONPATH:$SCRIPT_DIR/kit/python/lib/python3.10/site-packages:$SCRIPT_DIR/python_packages:$SCRIPT_DIR/source/omni.isaac.kit:$SCRIPT_DIR/kit/kernel/py:$SCRIPT_DIR/kit/plugins/bindings-python:$SCRIPT_DIR/source/omni.isaac.lula/pip_prebundle:$SCRIPT_DIR/source/omni.exporter.urdf/pip_prebundle:$SCRIPT_DIR/extscache/omni.kit.pip_archive-0.0.0+10a4b5c0.lx64.cp310/pip_prebundle:$SCRIPT_DIR/source/omni.isaac.core_archive/pip_prebundle:$SCRIPT_DIR/source/omni.isaac.ml_archive/pip_prebundle:$SCRIPT_DIR/source/omni.pip.compute/pip_prebundle:$SCRIPT_DIR/source/omni.pip.cloud/pip_prebundle


export RESOURCE_NAME=IsaacSim
