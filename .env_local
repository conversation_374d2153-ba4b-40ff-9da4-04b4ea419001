ISAACLAB_PATH="/workspace/isaaclab"
CARB_APP_PATH="/workspace/isaaclab/_isaac_sim/kit"
ISAAC_PATH="/workspace/isaaclab/_isaac_sim"
EXP_PATH="/workspace/isaaclab/_isaac_sim/apps"
PYTHONPATH="/workspace/isaaclab/_isaac_sim/kit/python/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages::/workspace/isaaclab/_isaac_sim/../../..//workspace/isaaclab/_isaac_sim/kit/python/lib/python3.10/site-packages:/opt/openrobots/lib/python3.10/site-packages::/workspace/isaaclab/_isaac_sim/kit/python/lib/python3.10:/workspace/isaaclab/_isaac_sim/kit/python/lib/python3.10/site-packages:/workspace/isaaclab/_isaac_sim/python_packages:/workspace/isaaclab/_isaac_sim/exts/isaacsim.simulation_app:/workspace/isaaclab/_isaac_sim/extsDeprecated/omni.isaac.kit:/workspace/isaaclab/_isaac_sim/kit/kernel/py:/workspace/isaaclab/_isaac_sim/kit/plugins/bindings-python:/workspace/isaaclab/_isaac_sim/exts/isaacsim.robot_motion.lula/pip_prebundle:/workspace/isaaclab/_isaac_sim/exts/isaacsim.asset.exporter.urdf/pip_prebundle:/workspace/isaaclab/_isaac_sim/extscache/omni.kit.pip_archive-0.0.0+d02c707b.lx64.cp310/pip_prebundle:/workspace/isaaclab/_isaac_sim/exts/omni.isaac.core_archive/pip_prebundle:/workspace/isaaclab/_isaac_sim/exts/omni.isaac.ml_archive/pip_prebundle:/workspace/isaaclab/_isaac_sim/exts/omni.pip.compute/pip_prebundle:/workspace/isaaclab/_isaac_sim/exts/omni.pip.cloud/pip_prebundle"
LD_LIBRARY_PATH="/opt/openrobots/lib:/usr/local/cuda/lib64:/workspace/isaaclab/_isaac_sim/../../..//opt/openrobots/lib:/usr/local/cuda/lib64:/workspace/isaaclab/_isaac_sim/.:/workspace/isaaclab/_isaac_sim/exts/omni.usd.schema.isaac/plugins/IsaacSensorSchema/lib:/workspace/isaaclab/_isaac_sim/exts/omni.usd.schema.isaac/plugins/RangeSensorSchema/lib:/workspace/isaaclab/_isaac_sim/exts/isaacsim.robot_motion.lula/pip_prebundle:/workspace/isaaclab/_isaac_sim/exts/isaacsim.asset.exporter.urdf/pip_prebundle:/workspace/isaaclab/_isaac_sim/kit:/workspace/isaaclab/_isaac_sim/kit/kernel/plugins:/workspace/isaaclab/_isaac_sim/kit/libs/iray:/workspace/isaaclab/_isaac_sim/kit/plugins:/workspace/isaaclab/_isaac_sim/kit/plugins/bindings-python:/workspace/isaaclab/_isaac_sim/kit/plugins/carb_gfx:/workspace/isaaclab/_isaac_sim/kit/plugins/rtx:/workspace/isaaclab/_isaac_sim/kit/plugins/gpu.foundation"


