import numpy as np
import weakref
from collections.abc import Callable
import carb
import omni

from isaaclab.devices.device_base import DeviceBase


class NavGamepad(DeviceBase):
    """
    A gamepad controller for navigation teleoperation.

    Controls:
      - Left Stick Up/Down: Forward/Reverse wheel velocity (applied equally to all wheels)
      - Left Stick Left/Right: Steering command.
          The output steering is applied as: [steer, -steer, steer, -steer] to the four steering joints.

    Additional callbacks can be registered to gamepad buttons.
    """
    def __init__(self, v_sensitivity: float = 1.0, s_sensitivity: float = 1.0, dead_zone: float = 0.01):
        # Turn off simulator gamepad camera control
        carb_settings_iface = carb.settings.get_settings()
        carb_settings_iface.set_bool("/persistent/app/omniverse/gamepadCameraControl", False)

        self.v_sensitivity = v_sensitivity
        self.s_sensitivity = s_sensitivity
        self.dead_zone = dead_zone

        # Acquire Omniverse input interfaces
        self._appwindow = omni.appwindow.get_default_app_window()
        self._input = carb.input.acquire_input_interface()
        self._gamepad = self._appwindow.get_gamepad(0)

        # Subscribe to gamepad events using a weak reference
        self._gamepad_sub = self._input.subscribe_to_gamepad_events(
            self._gamepad,
            lambda event, *args, obj=weakref.proxy(self): obj._on_gamepad_event(event, *args),
        )

        # Command buffer for 2 axes (velocity, steering), each with two directions (0: positive, 1: negative)
        self._cmd_buffer = np.zeros([2, 2])
        self._additional_callbacks = dict()

        self._create_key_bindings()

    def __del__(self):
        """Unsubscribe from gamepad events."""
        if hasattr(self, '_input') and hasattr(self, '_gamepad') and self._gamepad_sub is not None:
            self._input.unsubscribe_from_gamepad_events(self._gamepad, self._gamepad_sub)
            self._gamepad_sub = None

    def __str__(self) -> str:
        msg = f"Navigation Gamepad Controller: {self.__class__.__name__}\n"
        msg += f"Device name: {self._input.get_gamepad_name(self._gamepad)}\n"
        msg += "----------------------------------------------\n"
        msg += "Controls:\n"
        msg += "  Left Stick Up/Down: Forward/Reverse wheel velocity\n"
        msg += "  Left Stick Left/Right: Steering command\n"
        msg += "    (Output steering: [steer, -steer, steer, -steer] for steering joints)\n"
        return msg

    def reset(self):
        """Reset internal command states."""
        self._cmd_buffer.fill(0.0)

    def add_callback(self, key: carb.input.GamepadInput, func: Callable):
        """Add an additional function to be called when a gamepad key is pressed."""
        self._additional_callbacks[key] = func

    def advance(self) -> tuple[np.ndarray, np.ndarray]:
        """
        Returns:
            wheel_velocity (scalar): Command applied equally to all wheel joints.
            steering_vector (np.ndarray): Steering commands for the four joints,
              following the pattern [steer, -steer, steer, -steer].
        """
        # Resolve the velocity command (axis 0)
        vel = self._resolve_command_buffer(self._cmd_buffer[:, 0:1])[0]
        # Resolve the steering command (axis 1)
        steer = self._resolve_command_buffer(self._cmd_buffer[:, 1:2])[0]

        # Create steering vector for the 4 steering joints:
        # left front, right front, left rear, right rear.
        steering_vector = np.array([steer, -steer, steer, -steer], dtype=np.float32)

        return (vel, steering_vector)

    def _on_gamepad_event(self, event, *args, **kwargs):
        """Handle raw events from the gamepad."""
        cur_val = event.value
        if abs(cur_val) < self.dead_zone:
            cur_val = 0.0

        if event.input in self._INPUT_STICK_VALUE_MAPPING:
            direction, axis, scale = self._INPUT_STICK_VALUE_MAPPING[event.input]
            self._cmd_buffer[direction, axis] = scale * cur_val

        # Call any additional callbacks if registered
        if event.input in self._additional_callbacks:
            self._additional_callbacks[event.input]()

        return True

    def _create_key_bindings(self):
        """
        Create key mapping for navigation teleoperation:
          - LEFT_STICK_UP/DOWN control the velocity (axis 0)
          - LEFT_STICK_LEFT/RIGHT control the steering (axis 1)
        """
        self._INPUT_STICK_VALUE_MAPPING = {
            carb.input.GamepadInput.LEFT_STICK_UP:    (0, 0, self.v_sensitivity),
            carb.input.GamepadInput.LEFT_STICK_DOWN:  (1, 0, self.v_sensitivity),
            carb.input.GamepadInput.LEFT_STICK_RIGHT: (0, 1, self.s_sensitivity),
            carb.input.GamepadInput.LEFT_STICK_LEFT:  (1, 1, self.s_sensitivity),
        }

    def _resolve_command_buffer(self, raw_command: np.ndarray) -> np.ndarray:
        """
        Combine the positive and negative entries for each axis.
        Returns a vector of resolved commands.
        """
        command_sign = raw_command[1, :] > raw_command[0, :]
        command = raw_command.max(axis=0)
        command[command_sign] *= -1
        return command
