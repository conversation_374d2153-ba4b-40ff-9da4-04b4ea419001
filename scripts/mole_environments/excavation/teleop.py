#!/usr/bin/env python3

"""<PERSON><PERSON><PERSON> to run a keyboard/gamepad/SpaceMouse teleoperation for a 4-DOF robot 
using Isaac Lab environments.

Launch Isaac Sim Simulator before running this script.

Example usage:
    python teleop.py --task My4DofEnv --teleop_device gamepad
"""

import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Keyboard teleoperation for Isaac Lab with 4-DOF action space.")
parser.add_argument(
    "--disable_fabric", action="store_true", default=False, help="Disable fabric and use USD I/O operations."
)
parser.add_argument("--num_envs", type=int, default=1, help="Number of environments to simulate.")
parser.add_argument("--teleop_device", type=str, default="keyboard", help="Device for interacting with environment")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--sensitivity", type=float, default=1.0, help="Sensitivity factor.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(headless=args_cli.headless)
simulation_app = app_launcher.app

import gymnasium as gym
import torch
import carb

from isaaclab.devices import Se3Keyboard, Se3SpaceMouse
from gamepad import ArmGamepad
from isaaclab.managers import TerminationTermCfg as DoneTerm

from isaaclab_tasks.utils import parse_env_cfg
import moleworks_ext.tasks  # noqa: F401


def pre_process_actions(delta_pose: torch.Tensor) -> torch.Tensor:
    """
    Convert a (6D) delta pose from the teleop device into a 4D action vector.

    The full 6D delta pose is: [dX, dY, dZ, dRoll, dPitch, dYaw].
    For a 4-DOF robot, we only want: [dX, dY, dRoll, dPitch].
    We ignore z (delta_pose[:,2]) and yaw (delta_pose[:,5]).
    """
    # Indices: 0->dX, 1->dY, 3->dRoll, 4->dPitch
    return torch.stack([
        delta_pose[:, 0],  # dX
        delta_pose[:, 1],  # dY
        delta_pose[:, 3],  # dRoll
        delta_pose[:, 4],  # dPitch
    ], dim=1)


def main():
    """Run teleoperation for a 4-DOF robot environment."""
    # parse configuration
    env_cfg = parse_env_cfg(
        args_cli.task,
        device=args_cli.device,
        num_envs=args_cli.num_envs,
        use_fabric=not args_cli.disable_fabric
    )
    # remove default time-out, so the task does not auto-reset on time
    env_cfg.send_timeouts = False
    env_cfg.terminations_excavation.negative_terminations.bucket_height = False
    env_cfg.reset.sample_soil = False
    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)

    # create teleoperation device
    if args_cli.teleop_device.lower() == "keyboard":
        teleop_interface = Se3Keyboard(
            pos_sensitivity=0.05 * args_cli.sensitivity, 
            rot_sensitivity=0.05 * args_cli.sensitivity
        )
    elif args_cli.teleop_device.lower() == "spacemouse":
        teleop_interface = Se3SpaceMouse(
            pos_sensitivity=0.05 * args_cli.sensitivity, 
            rot_sensitivity=0.005 * args_cli.sensitivity
        )
    elif args_cli.teleop_device.lower() == "gamepad":
        teleop_interface = ArmGamepad(
            pos_sensitivity=0.1 * args_cli.sensitivity, 
            rot_sensitivity=0.1 * args_cli.sensitivity
        )
    else:
        raise ValueError(
            f"Invalid device interface '{args_cli.teleop_device}'. "
            "Supported: 'keyboard', 'spacemouse', 'gamepad'."
        )

    # add teleoperation key for env reset (press 'L' to reset).
    # For the gamepad, you can also map a button to env.reset in se3_gamepad.py
    teleop_interface.add_callback("L", env.reset)

    # print controls
    print(teleop_interface)

    # reset environment
    env.reset()
    teleop_interface.reset()

    # main simulation loop
    while simulation_app.is_running():
        with torch.inference_mode():
            # read the device deltas
            delta_pose, _ = teleop_interface.advance()  # ignoring any "gripper command" here
            delta_pose = delta_pose.astype("float32")

            # replicate across all envs in the batch
            delta_pose_tensor = torch.tensor(delta_pose, device=env.unwrapped.device)
            delta_pose_tensor = delta_pose_tensor.repeat(env.unwrapped.num_envs, 1)
            
            # convert the 6D pose into a 4D action
            actions = pre_process_actions(delta_pose_tensor)
            
            # step the environment
            obs, rewards, resets, timeouts, extras = env.step(actions)

            # Print termination cases if environment resets
            if resets.any():
                if "episode_neg_term_counts" in extras:
                    print("\nNegative terminations:")
                    for key, value in extras["episode_neg_term_counts"].items():
                        if sum(value) > 0:
                            print(f"  {key}: {sum(value)}")
                if "episode_pos_term_counts" in extras:
                    print("\nPositive terminations:")
                    for key, value in extras["episode_pos_term_counts"].items():
                        if sum(value) > 0:
                            print(f"  {key}: {sum(value)}")

    env.close()


if __name__ == "__main__":
    main()
    simulation_app.close()
