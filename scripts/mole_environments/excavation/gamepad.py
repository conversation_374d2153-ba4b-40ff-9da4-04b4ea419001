import numpy as np
import weakref
from collections.abc import Callable
from scipy.spatial.transform import Rotation
import carb
import omni

from isaaclab.devices.device_base import DeviceBase


class ArmGamepad(DeviceBase):
    """A gamepad controller for sending SE(3) commands as delta poses and a binary gripper command.

    Modified for a 4-DOF scenario using the D-Pad for (x, y) and the right stick for (roll, pitch).
    - D-Pad → (x, y)
    - Right stick → (roll, pitch)
    - No z / yaw mapping
    - No left stick usage
    """

    def __init__(self, pos_sensitivity: float = 1.0, rot_sensitivity: float = 1.6, dead_zone: float = 0.01):
        """Initialize the gamepad layer.

        Args:
            pos_sensitivity: Magnitude of input position command scaling.
            rot_sensitivity: Magnitude of input rotation command scaling.
            dead_zone: Dead zone for gamepad stick input.
        """
        # Turn off simulator camera gamepad control
        carb_settings_iface = carb.settings.get_settings()
        carb_settings_iface.set_bool("/persistent/app/omniverse/gamepadCameraControl", False)

        self.pos_sensitivity = pos_sensitivity
        self.rot_sensitivity = rot_sensitivity
        self.dead_zone = dead_zone

        # Acquire Omniverse input interfaces
        self._appwindow = omni.appwindow.get_default_app_window()
        self._input = carb.input.acquire_input_interface()
        self._gamepad = self._appwindow.get_gamepad(0)

        # Subscribe to gamepad events; use weakref
        self._gamepad_sub = self._input.subscribe_to_gamepad_events(
            self._gamepad,
            lambda event, *args, obj=weakref.proxy(self): obj._on_gamepad_event(event, *args),
        )

        # Command buffers
        self._close_gripper = False
        # For each axis we store positive and negative input separately:
        # shape = (2, 6) → 2 directions (pos, neg) x 6 possible DOF
        self._delta_pose_raw = np.zeros([2, 6])

        self._additional_callbacks = dict()

        # Create our custom mapping for 4-DOF
        self._create_key_bindings()

    def __del__(self):
        """Unsubscribe from gamepad events."""
        self._input.unsubscribe_from_gamepad_events(self._gamepad, self._gamepad_sub)
        self._gamepad_sub = None

    def __str__(self) -> str:
        """Print out usage for the 4-DOF gamepad interface (D-Pad for x,y and Right Stick for roll, pitch)."""
        msg = f"Gamepad Controller for SE(3): {self.__class__.__name__}\n"
        msg += f"Device name: {self._input.get_gamepad_name(self._gamepad)}\n"
        msg += "----------------------------------------------\n"
        msg += "Toggle gripper (open/close): X\n\n"
        msg += "D-Pad:\n"
        msg += "  Up/Down -> +x / -x\n"
        msg += "  Right/Left -> +y / -y\n\n"
        msg += "Right Stick:\n"
        msg += "  Up/Down -> +pitch / -pitch\n"
        msg += "  Left/Right -> -roll / +roll\n"
        return msg

    def reset(self):
        """Reset internal command states."""
        self._close_gripper = False
        self._delta_pose_raw.fill(0.0)

    def add_callback(self, key: carb.input.GamepadInput, func: Callable):
        """Add additional function to be called when a gamepad key is pressed."""
        self._additional_callbacks[key] = func

    def advance(self) -> tuple[np.ndarray, bool]:
        """
        Returns:
            (delta_pose, gripper_flag)

            Where delta_pose is [dX, dY, dZ, rX, rY, rZ].
            In this 4-DOF usage, we'll fill x, y, roll, pitch (others = 0).
        """
        # Resolve the x,y portion from self._delta_pose_raw[:, :3]
        # but we won't actually use z in this example, so it stays zero.
        delta_pos = self._resolve_command_buffer(self._delta_pose_raw[:, :3])  # shape (3,)

        # For rotations, we only need roll and pitch → stored at indices 3,4.
        # (We keep the yaw index 5 in the array, but it stays zero.)
        delta_rot = self._resolve_command_buffer(self._delta_pose_raw[:, 3:6])  # shape (3,)

        # Convert Euler angles [roll, pitch, yaw] to a rotation vector
        rot_vec = Rotation.from_euler("XYZ", delta_rot).as_rotvec()

        delta_pose = np.concatenate([delta_pos, rot_vec])
        return (delta_pose, self._close_gripper)

    """
    Internal Helpers
    """

    def _on_gamepad_event(self, event, *args, **kwargs):
        """Handle raw events from the gamepad."""
        cur_val = event.value
        if abs(cur_val) < self.dead_zone:
            cur_val = 0.0

        # Toggle gripper
        if event.input == carb.input.GamepadInput.X:
            # On press
            if cur_val > 0.5:
                self._close_gripper = not self._close_gripper

        # Check if the event maps to a known axis in _INPUT_STICK_VALUE_MAPPING
        if event.input in self._INPUT_STICK_VALUE_MAPPING:
            direction, axis, scale = self._INPUT_STICK_VALUE_MAPPING[event.input]
            self._delta_pose_raw[direction, axis] = scale * cur_val

        # Additional callbacks (e.g., user-specified resets)
        if event.input in self._additional_callbacks:
            self._additional_callbacks[event.input]()

        return True

    def _create_key_bindings(self):
        """
        Create a simplified key mapping for 4-DOF:
          - D-Pad => (x, y)
          - Right stick => (roll, pitch)
        """
        # Indices in self._delta_pose_raw are:
        #  0: x, 1: y, 2: z, 3: roll, 4: pitch, 5: yaw
        # We won't use z or yaw, so we never fill index 2 or 5.
        # direction = 0 => positive axis
        # direction = 1 => negative axis

        self._INPUT_STICK_VALUE_MAPPING = {
            # D-Pad => (x, y)
            carb.input.GamepadInput.DPAD_UP:    (1, 0, self.pos_sensitivity * 0.3),  # +X
            carb.input.GamepadInput.DPAD_DOWN:  (0, 0, self.pos_sensitivity * 0.3),  # -X
            carb.input.GamepadInput.DPAD_RIGHT: (0, 1, self.pos_sensitivity * 0.3),  # +Y
            carb.input.GamepadInput.DPAD_LEFT:  (1, 1, self.pos_sensitivity * 0.3),  # -Y

            # Right Stick => (roll, pitch)
            carb.input.GamepadInput.RIGHT_STICK_LEFT:  (1, 3, self.rot_sensitivity),  # -roll
            carb.input.GamepadInput.RIGHT_STICK_RIGHT: (0, 3, self.rot_sensitivity),  # +roll
            carb.input.GamepadInput.RIGHT_STICK_UP:    (0, 4, self.rot_sensitivity),  # +pitch
            carb.input.GamepadInput.RIGHT_STICK_DOWN:  (1, 4, self.rot_sensitivity),  # -pitch
        }

        # We won't use the left stick or any other D-Pad functionality beyond these.
        self._INPUT_DPAD_VALUE_MAPPING = {}

    def _resolve_command_buffer(self, raw_command: np.ndarray) -> np.ndarray:
        """
        Given a sub-slice of self._delta_pose_raw (shape (2, N)), we want to combine
        the positive and negative entries. We pick the maximum magnitude among them
        and apply the appropriate sign.
        """
        # shape (2, N) => (pos_dir, neg_dir)
        # direction: (0: positive, 1: negative)

        # Which side is bigger in absolute magnitude?
        delta_command_sign = raw_command[1, :] > raw_command[0, :]

        # Get the maximum magnitude from each column
        delta_command = raw_command.max(axis=0)

        # Flip sign if negative side is bigger
        delta_command[delta_command_sign] *= -1

        return delta_command
