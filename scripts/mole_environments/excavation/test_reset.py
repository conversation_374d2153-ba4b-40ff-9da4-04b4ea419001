# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

"""Launch Isaac Sim Simulator fir
st."""
import argparse
import json
import os
import sys
import traceback
from datetime import datetime

import carb
import gymnasium as gym
import matplotlib.pyplot as plt
import torch

from isaaclab.app import AppLauncher
# import cli_args  # isort: skip

# add argparse arguments
parser = argparse.ArgumentParser(description="Test environment resets.")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
parser.add_argument("--tags", nargs="+", type=str, default=[], help="Tags used for the run.")
# cli_args.add_rsl_rl_args(parser)
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()

# Override needed arguments
args_cli.task = "Isaac-m545-digging"
args_cli.num_envs = 1500 * 20
args_cli.headless = True

# Launch the Omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

# Import tasks (so they are registered)
import moleworks_ext.tasks
from moleworks_ext.tasks.excavation.env_cfg.m545_env_cfg import M545EnvCfg
from moleworks_ext.tasks.excavation.excavation_env import ExcavationEnv


def main():
    """Test environment resets."""
    print("Creating environment...")
    env_cfg = M545EnvCfg()

    # Additional environment config overrides
    env_cfg.scene.num_envs = 1000
    env_cfg.decimation = 4
    env_cfg.sim.dt = 0.0375
    env_cfg.max_depth_height.theta = 0.5
    env_cfg.reset.sample_soil = True
    env_cfg.send_timeouts = False
    env_cfg.terminations_excavation.disable_negative_termination = False
    env_cfg.reset.only_above_soil = True
    env_cfg.reset.sample_max_depth = True
    env_cfg.reset.sample_pullup_dist = True
    env_cfg.reset.sample_obstacles = False
    env_cfg.reset.fixed_config = False
    env_cfg.sim.device = "cuda"

    # enable fabric
    env_cfg.sim.use_fabric = True

    # ensures enhanced determinism without much loss in performance (only available for cpu)
    env_cfg.sim.physx.enable_enhanced_determinism = True
    # Create the environment

    env = ExcavationEnv(cfg=env_cfg)

    # Number of resets
    num_resets = 20
    warmup_resets = 1
    num_steps = 1
    done_count = torch.zeros(1, device=env.device)

    # Create a directory for logs
    log_root_path = os.path.join("logs", "rsl_rl", args_cli.task)
    log_root_path = os.path.abspath(log_root_path)
    reset_log_path = os.path.join(log_root_path, datetime.now().strftime("%Y-%m-%d_%H-%M-%S") + "_reset")
    os.makedirs(reset_log_path, exist_ok=True)

    # Reset environment
    actions = torch.zeros((env.num_envs, 4), device=env.device)
    # env.step(actions)
    # env.reset()

    depths = torch.zeros(num_resets - warmup_resets, env.num_envs, device=env.device)
    depths_after_step = torch.zeros(num_resets - warmup_resets, env.num_envs, device=env.device)
    
    print("Stepping...")
    with torch.inference_mode():
        for i in range(num_resets):
            print("*" * 50)
            if i >= warmup_resets:
                depths[i - warmup_resets] = env.unwrapped.soil.get_bucket_depth().squeeze()
            for _ in range(num_steps):
                obs, rewards, dones, timeouts, infos = env.step(actions)
                if i >= warmup_resets:
                    depths_after_step[i - warmup_resets] = env.unwrapped.soil.get_bucket_depth().squeeze()
                    in_soil = env.unwrapped.soil.get_bucket_depth() > 0.0
                    print("Percentage of in_soil: ", torch.mean(in_soil.to(torch.float32)))
                if i > warmup_resets:
                    if dones.any():
                        done_count += torch.sum(dones)
            env.reset()

    total_resets = (num_resets - warmup_resets) * env.num_envs
    in_soil = torch.sum(depths > 0.0)
    in_soil_after_step = torch.sum(depths_after_step > 0.0)

    print(
        "resets in soil: {} / {} ({} %)".format(
            int(in_soil.item()), int(total_resets), 100 * float(in_soil) / total_resets
        )
    )
    print(
        "resets in soil after step: {} / {} ({} %)".format(
            int(in_soil_after_step.item()), int(total_resets), 100 * float(in_soil_after_step) / total_resets
        )
    )
    print(
        "done/resets: {} / {} ({} %)".format(
            int(done_count), int(total_resets), 100.0 * float(done_count) / total_resets
        )
    )

    # Negative and positive termination stats
    for key, value in infos["episode_neg_term_counts"].items():
        print("neg term: " + key + ": " + str(sum(value)))
    for key, value in infos["episode_pos_term_counts"].items():
        print("pos term: " + key + ": " + str(sum(value)))

    # Plotting depths
    fig, axs = plt.subplots(1, 2, figsize=(20, 6))
    axs[0].hist(depths.cpu().numpy().flatten(), bins=50, color="blue", alpha=0.7)
    axs[0].set_title("Distribution of Depths")
    axs[0].set_xlabel("Depth")
    axs[0].set_ylabel("Frequency")
    axs[0].grid(True)

    axs[1].hist(depths_after_step.cpu().numpy().flatten(), bins=50, color="blue", alpha=0.7)
    axs[1].set_title("Distribution of Depths after Step")
    axs[1].set_xlabel("Depth")
    axs[1].set_xlim(-0.5, 0.5)
    axs[1].set_ylabel("Frequency")
    axs[1].grid(True)

    plt.show()
    input("hit [ENTER] to exit")
    env.close()


if __name__ == "__main__":
    try:
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        simulation_app.close()
