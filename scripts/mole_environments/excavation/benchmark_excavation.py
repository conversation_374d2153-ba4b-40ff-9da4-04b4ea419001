"""
<PERSON>ript to play a checkpoint if an RL agent from RSL-RL and benchmark its performance, including
logging of terminations (positive, negative, or timeouts) but ignoring per-asset statistics.

Launch Isaac Sim Simulator first.
"""
import json
import os
import time
from collections import defaultdict, deque
import statistics
import matplotlib
import matplotlib.pyplot as plt
from matplotlib.backends.backend_pdf import PdfPages
from datetime import datetime
import torch
import numpy as np

print("CARB_APP_PATH:", os.environ.get("CARB_APP_PATH"))
print("ISAAC_PATH:", os.environ.get("ISAAC_PATH"))
print("EXP_PATH:", os.environ.get("EXP_PATH"))
print("RESOURCE_NAME:", os.environ.get("RESOURCE_NAME"))
print("LD_PRELOAD:", os.environ.get("LD_PRELOAD"))

import argparse

from isaaclab.app import AppLauncher

# local imports
import cli_args  # isort: skip

# add argparse arguments
parser = argparse.ArgumentParser(description="Play an RL agent with RSL-RL and benchmark its performance.")
parser.add_argument("--video", action="store_true", default=False, help="Record videos during training.")
parser.add_argument("--video_length", type=int, default=200, help="Length of the recorded video (in steps).")
parser.add_argument(
    "--disable_fabric", action="store_true", default=False, help="Disable fabric and use USD I/O operations."
)
parser.add_argument("--num_envs", type=int, default=10000, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
# Add benchmark arguments
parser.add_argument("--benchmark", action="store_true", default=True, help="Run benchmarking mode.")
parser.add_argument("--benchmark_steps", type=int, default=200, help="Number of steps to run for benchmarking.")
# append RSL-RL cli arguments
cli_args.add_rsl_rl_args(parser)
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()
args_cli.headless = True
# always enable cameras to record video
if args_cli.video:
    args_cli.enable_cameras = True
args_cli.task = "Isaac-m545-digging"
# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

# RSL-RL imports
from rsl_rl.runners import OnPolicyRunner

# Isaac-Sim-lab imports
import gymnasium as gym
from isaaclab.envs import DirectMARLEnv, multi_agent_to_single_agent
from isaaclab.utils.dict import print_dict
from isaaclab_tasks.utils import get_checkpoint_path, parse_env_cfg
from isaaclab_rl.rsl_rl import (
    RslRlOnPolicyRunnerCfg,
    RslRlVecEnvWrapper,
    export_policy_as_jit,
    export_policy_as_onnx,
)
import moleworks_ext.tasks  # noqa: F401


def main():
    """Play with RSL-RL agent, and benchmark with termination-cause logging (ignoring per-asset stats)."""
    # parse configuration
    env_cfg = parse_env_cfg(
        args_cli.task, device=args_cli.device, num_envs=args_cli.num_envs, use_fabric=not args_cli.disable_fabric
    )
    agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(args_cli.task, args_cli)

    # specify directory for logging experiments
    log_root_path = os.path.join("logs", "rsl_rl", agent_cfg.experiment_name)
    log_root_path = os.path.abspath(log_root_path)
    print(f"[INFO] Loading experiment from directory: {log_root_path}")
    resume_path = get_checkpoint_path(log_root_path, agent_cfg.load_run, agent_cfg.load_checkpoint)
    log_dir = os.path.dirname(resume_path)

    # create isaac environment
    env = gym.make(args_cli.task, cfg=env_cfg, render_mode="rgb_array" if args_cli.video else None)
    # Possibly wrap for video
    if args_cli.video:
        video_kwargs = {
            "video_folder": os.path.join(log_dir, "videos", "play"),
            "step_trigger": lambda step: step == 0,
            "video_length": args_cli.video_length,
            "disable_logger": True,
        }
        print("[INFO] Recording videos during run.")
        print_dict(video_kwargs, nesting=4)
        env = gym.wrappers.RecordVideo(env, **video_kwargs)

    # If multi-agent environment, convert to single-agent
    if isinstance(env.unwrapped, DirectMARLEnv):
        env = multi_agent_to_single_agent(env)

    # Wrap with the RslRlVecEnvWrapper
    env = RslRlVecEnvWrapper(env)
    env.unwrapped.curriculum_excavation.set_level_and_update(torch.tensor(2000, device=env.device))
    env.unwrapped.curriculum_excavation.theta = args.rbf_theta
    env.unwrapped.soil.soil_height.compute_norm_transform(theta=env.unwrapped.curriculum_excavation.theta)
    env.unwrapped.soil.max_depth_height.compute_norm_transform(theta=env.unwrapped.curriculum_excavation.theta)

    print(f"[INFO] Loading model checkpoint from: {resume_path}")
    # load previously trained model
    ppo_runner = OnPolicyRunner(env, agent_cfg.to_dict(), log_dir=None, device=agent_cfg.device)
    ppo_runner.load(resume_path)

    # get inference policy
    policy = ppo_runner.get_inference_policy(device=env.unwrapped.device)

    # export policy to onnx/jit
    export_model_dir = os.path.join(os.path.dirname(resume_path), "exported")
    export_policy_as_jit(
        ppo_runner.alg.actor_critic, ppo_runner.obs_normalizer, path=export_model_dir, filename="policy.pt"
    )
    export_policy_as_onnx(
        ppo_runner.alg.actor_critic, normalizer=ppo_runner.obs_normalizer, path=export_model_dir, filename="policy.onnx"
    )

    # ----------------------------------------------------------------
    # Optionally: Run main simulation loop with or without benchmarking
    # ----------------------------------------------------------------
    obs, _ = env.get_observations()
    timestep = 0

    # ------------------------------------------------------------
    # Benchmarking with termination cause logging
    # ------------------------------------------------------------
    num_envs = env.num_envs
    num_steps = args_cli.benchmark_steps
    num_data = num_envs * num_steps
    print(f"[INFO] Running {num_steps} steps for benchmarking over {num_envs} environment(s).")

    # Track how many episodes complete and the lengths for each environment
    episode_count = 0
    ep_len = torch.zeros(num_envs, device=env.unwrapped.device)

    # Need to log those for termination conditions
    bucket_aoa = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    bucket_vel = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    base_vel = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    max_depth = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    pullup_dist = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    in_soil = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    bucket_x = torch.zeros(args_cli.num_envs, num_steps, device=env.device)
    bucket_z = torch.zeros(args_cli.num_envs, num_steps, device=env.device)

    # need to keep track of it manually, because step() resets if done but we only log after step()
    ep_lens = torch.zeros(args_cli.num_envs, device=env.device)
    ep_len_counts = {}
    ep_len_counts["timeout"] = deque()
    for name in env.unwrapped.termination_excavation.neg_term_names:
        ep_len_counts["neg_" + name] = deque()
    ep_len_counts["close"] = deque()
    ep_len_counts["full"] = deque()
    ep_len_counts["desired_partial"] = deque()

    # Also keep aggregator for each cause
    ep_len_counts = defaultdict(deque)

    for step_i in range(num_steps):
        if step_i % 50 == 0:
            print(f"Benchmark step {step_i} / {num_steps}")

        with torch.inference_mode():
            actions = policy(obs)
            obs, rewards, dones, infos = env.step(actions)

        ep_len += 1
        # Log terminations
        if dones.any():
            # Indices of done environments
            done_ids = dones.nonzero()

            # Update dones information
            episode_count += len(done_ids)
            # print at step i added n episodes done 
            ep_len_counts["timeout"].extend(ep_lens[env.unwrapped.termination_excavation.time_out_buf].tolist())
            for name in env.unwrapped.termination_excavation.neg_term_names:
                ep_len_counts["neg_" + name].extend(ep_lens[env.unwrapped.termination_excavation.episode_neg_term_buf[name]].tolist())
            ep_len_counts["close"].extend(ep_lens[env.unwrapped.termination_excavation.close_pos_term_buf].tolist())
            ep_len_counts["full"].extend(ep_lens[env.unwrapped.termination_excavation.full_pos_term_buf].tolist())
            ep_len_counts["desired_partial"].extend(ep_lens[env.unwrapped.termination_excavation.partial_pos_term_buf].tolist())

            # reset ep lens
            ep_lens[done_ids] = 0
    
    pdf_filename = os.path.join(log_dir, "stats.pdf")
    with PdfPages(pdf_filename) as pdf:
        #--------- Save termination statistics per excavator type
        all_data_to_serialize = []
        # Define consistent label order
        label_order = ['desired_full', 'desired_close', 'desired_partial', 'timeout']
        # Add negative termination names
        neg_term_names = list(env.unwrapped.termination_excavation.neg_term_names)
        label_order.extend(neg_term_names)

        # Save all data to a single JSON file
        all_data_filepath = os.path.join(log_dir, "terminations_all_assets.json")
        with open(all_data_filepath, 'w') as file:
            json.dump(all_data_to_serialize, file)

        #--------- Plotting Overall Failure Distribution
        # Terminations, percentage [0,1]
        values = []
        labels = []
        label_order = ['desired_full', 'desired_close', 'desired_partial', 'timeout']
        neg_term_names = list(env.unwrapped.termination_excavation.neg_term_names)
        label_order.extend(neg_term_names)

        for label in label_order:
            if label in env.unwrapped.termination_excavation.episode_neg_term_counts:
                value = torch.sum(env.unwrapped.termination_excavation.episode_neg_term_counts[label]).item() / episode_count
                values.append(value)
                labels.append(label)
            elif label in env.unwrapped.termination_excavation.episode_pos_term_counts:
                value = torch.sum(env.unwrapped.termination_excavation.episode_pos_term_counts[label]).item() / episode_count
                values.append(value)
                labels.append(label)
            elif label == "timeout":
                value = torch.sum(env.unwrapped.termination_excavation.time_out_count).item() / episode_count
                values.append(value)
                labels.append(label)

        # Calculate percentages for positive terminations
        sum_pos_term = sum([torch.sum(env.unwrapped.termination_excavation.episode_pos_term_counts[key]).item()
                            for key in env.unwrapped.termination_excavation.episode_pos_term_counts])
        full_term = torch.sum(env.unwrapped.termination_excavation.episode_pos_term_counts.get("desired_full", torch.tensor(0))).item() / episode_count
        close_term = torch.sum(env.unwrapped.termination_excavation.episode_pos_term_counts.get("desired_close", torch.tensor(0))).item() / episode_count
        partial_term = torch.sum(env.unwrapped.termination_excavation.episode_pos_term_counts.get("desired_partial", torch.tensor(0))).item() / episode_count

        # Print out the failure statistics
        print("\nFailure Statistics:")
        for label, value in zip(labels, values):
            print(f"{label}: {value:.3f}")

        fig, ax = plt.subplots(figsize=(12, 8))
        ax.bar(np.arange(len(values)), values, tick_label=labels, color='salmon')
        plt.xticks(rotation=45, ha='right', fontsize=12)
        ax.set_title(
            "Overall Failure Distribution\nclose ({:.2f}), full ({:.2f}), partial ({:.2f}) term/tot term: {} / {} [{:.2f}%]".format(
                close_term, full_term, partial_term, sum_pos_term, episode_count, 100.0 * sum_pos_term / episode_count
            ), fontsize=14
        )
        ax.grid(axis='y', linestyle='--', alpha=0.7)

        plt.tight_layout()
        pdf.savefig(fig)  # Save the overall failure distribution figure
        plt.close(fig)

        print("Number of data samples:", num_data)
        error_dict = {}

        # Stats violating negative termination conditions
        def log_and_print_stats(name, errs, num_data, error_dict):
            if not errs:
                print(f"{name}: No errors recorded.")
                return
            error_dict[name] = errs
            print(
                "{:<25} num/num_data: {:<10.2e} mean: {:<7.2f} std: {:<7.2f} min: {:<7.2f} max: {:<7.2f}".format(
                    name,
                    len(errs) / num_data,
                    statistics.mean(errs) if len(errs) > 1 else (errs[0] if len(errs) == 1 else np.nan),
                    statistics.stdev(errs) if len(errs) > 1 else np.nan,
                    min(errs) if len(errs) > 0 else np.nan,
                    max(errs) if len(errs) > 0 else np.nan,
                )
            )

        # bucket aoa
        bad_aoa = bucket_aoa < 0.0
        fast_enough = bucket_vel > env.cfg.terminations_excavation.bucket_vel_aoa_threshold
        ids = torch.where(torch.logical_and(in_soil, torch.logical_and(bad_aoa, fast_enough)))
        errs = (bucket_aoa[ids] - 0.0).tolist()
        log_and_print_stats("bucket_aoa", errs, num_data, error_dict)
        # bucket vel
        ids = torch.where(env.unwrapped.m545_measurements.bucket_vel_norm > env.cfg.terminations_excavation.max_bucket_vel)
        errs = (env.unwrapped.m545_measurements.bucket_vel_norm[ids] - env.cfg.terminations_excavation.max_bucket_vel).tolist()
        log_and_print_stats("bucket_vel", errs, num_data, error_dict)

        # base vel
        ids = torch.where(base_vel > env.cfg.terminations_excavation.max_base_vel)
        errs = (base_vel[ids] - env.cfg.terminations_excavation.max_base_vel).tolist()
        log_and_print_stats("base_vel", errs, num_data, error_dict)

        # max depth
        ids = torch.where(bucket_z < (max_depth - env.cfg.terminations_excavation.max_depth_overshoot))
        errs = (bucket_z[ids] - (max_depth[ids] - env.cfg.terminations_excavation.max_depth_overshoot)).tolist()
        log_and_print_stats("max_depth", errs, num_data, error_dict)

        # pullup
        ids = torch.where(bucket_x < pullup_dist)
        errs = (bucket_x[ids] - pullup_dist[ids]).tolist()
        log_and_print_stats("pullup", errs, num_data, error_dict)

        # episode lengths
        log_and_print_stats("len timeout", ep_len_counts.get("timeout", []), num_data, error_dict)
        log_and_print_stats("len close", ep_len_counts.get("close", []), num_data, error_dict)
        log_and_print_stats("len full", ep_len_counts.get("full", []), num_data, error_dict)
        log_and_print_stats("len desired_partial", ep_len_counts.get("desired_partial", []), num_data, error_dict)

        for name in env.unwrapped.termination_excavation.neg_term_names:
            log_and_print_stats(f"len neg_{name}", ep_len_counts.get(f"neg_{name}", []), num_data, error_dict)

        # Plot boxplots for errors
        if error_dict:
            for key, value in error_dict.items():
                if not value:
                    print(f"No data to plot for {key}. Skipping boxplot.")
                    continue

                fig, ax = plt.subplots(figsize=(12, 8))
                ax.boxplot(value, vert=True)
                ax.set_xticklabels([key], fontsize=12)
                if len(value) > 0:
                    quantiles = np.quantile(value, [0.25, 0.5, 0.75])
                    ax.set_yticks(quantiles)
                    ax.set_title(
                        "{}: q1-q3: {}, steps/total_steps: {} / {} [{:.2f}%]".format(
                            key,
                            " | ".join([str(np.round(x, 2)) for x in quantiles]),
                            len(value), num_data, 100.0 * len(value) / num_data
                        ), fontsize=14
                    )
                    # Adjust y-axis limits with a buffer
                    buffer = (max(value) - min(value)) * 0.1 if max(value) != min(value) else 1
                    ax.set_ylim([min(value) - buffer, max(value) + buffer])
                else:
                    ax.set_title(
                        "{}: No data available".format(key), fontsize=14
                    )
                    ax.set_ylim([0, 1])  # Default limits

                ax.grid(axis='y', linestyle='--', alpha=0.7)

                plt.tight_layout()
                pdf.savefig(fig)  # Save the boxplot to the multipage PDF
                plt.close(fig)
        else:
            print("No errors recorded. No boxplots to generate.")

        # Optionally, add all plots to the multipage PDF as they are created above

    # No need to open the PDF automatically since it's a multipage PDF now
    print(f"Multipage PDF saved at: {pdf_filename}")

    # close the simulator
    env.close()



if __name__ == "__main__":
    main()
