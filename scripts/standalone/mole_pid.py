#!/usr/bin/env python3
# Copyright...

"""Test Termination Condition for Excavation"""

from __future__ import annotations

import argparse
import os

import matplotlib
# If running headless, force an off-screen backend
# (e.g., Agg) so that plt.show() won't complain.
if os.getenv("DISPLAY", "") == "":
    matplotlib.use('Agg') 

import matplotlib.pyplot as plt

from isaaclab.app import AppLauncher

parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
parser.add_argument("--num_steps", type=int, default=None, help="Number of steps after which to stop the simulation. If not provided, the simulation continues indefinitely.")

AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()
args_cli.task = "mole_sim"
args_cli.num_envs = 1

app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

import gymnasium as gym
import torch
import traceback
import carb
import numpy as np

from rsl_rl.runners import OnPolicyRunner

import isaaclab_tasks  # noqa: F401
import moleworks_ext.tasks

from moleworks_ext.tasks.sim.sim_env import MoleSimEnv
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg
from moleworks_ext.tasks.sim.sim_arm_cfg import MoleSimEnvCfg

from isaaclab.terrains import TerrainImporterCfg, TerrainImporter
from isaaclab.sensors.frame_transformer import FrameTransformer
from moleworks_ext.common.actions.actions_cfg import InverseDynamicsActionCfg

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")

INV_DYN_CFG = InverseDynamicsControllerCfg(
    command_type="vel",
    k_p=[0, 0, 0, 0],
    k_d=[25, 30, 20, 20],
    dof_limits=[
        [-0.5, 0.5],
        [-0.6, 0.6],
        [-0.4, 0.4],
        [-0.8, 0.8]
    ],
    dof_efforts_limits=[
        [-2e6, 2e6],
        [-1e6, 1e6],
        [-1e6, 1e6],
        [-1e6, 1e6]
    ]
)

EXTERNAL_FORCE_APPLIED_AT = 4.0
EXTERNAL_FORCE = np.array([2.0e4, 0.0, 7.1892e3])
EXTERNAL_MOMENT = np.array([0.0, -16739.1543, 0.0])

EXCITE_DURATION_PER_JOINT = 3.0

OSCILLATION_CFG = {
    "frequency": 0.5,
    "amplitude": 0.1
}


def generate_sinusoidal_force(excitation_time: float, frequency: float, amplitude: float) -> torch.Tensor:
    return amplitude * torch.sin(torch.tensor(2 * np.pi * frequency * excitation_time))


def main():
    env_cfg = MoleSimEnvCfg()
    env_cfg.num_envs = 1
    env = MoleSimEnv(cfg=env_cfg)

    env.reset()
    time_elapsed = 0
    step_count = 0
    ARM_JOINTS_IDS = env.scene['robot'].find_joints(['J_BOOM', 'J_STICK', 'J_TELE', 'J_EE_PITCH'])[0]
    JOINT_SEQUENCE = ARM_JOINTS_IDS
    TOTAL_EXCITATION_TIME = EXCITE_DURATION_PER_JOINT * len(ARM_JOINTS_IDS)

    max_joint_velocities = torch.tensor([
        min(abs(INV_DYN_CFG.dof_limits[i][0]), abs(INV_DYN_CFG.dof_limits[i][1]))
        for i in range(len(INV_DYN_CFG.dof_limits))
    ], device=env.device)

    amplitude_scales = OSCILLATION_CFG["amplitude"] / max_joint_velocities

    log_data = {
        "joint_positions": [],
        "joint_velocities": [],
        "commanded_velocities": [],
        "external_force": [],
        "external_moment": [],
        "time": [],
        "root_position": []
    }

    actions = torch.zeros((env.num_envs, len(ARM_JOINTS_IDS)), device=env.device, dtype=torch.float32)

    with torch.inference_mode():
        while simulation_app.is_running():
            current_joint_index = int(time_elapsed // EXCITE_DURATION_PER_JOINT) % len(JOINT_SEQUENCE)
            active_joint_id = JOINT_SEQUENCE[current_joint_index]

            excitation_time = time_elapsed % EXCITE_DURATION_PER_JOINT
            frequency = OSCILLATION_CFG["frequency"]
            amplitude = min(max_joint_velocities[current_joint_index], OSCILLATION_CFG["amplitude"])
            sinusoid = generate_sinusoidal_force(excitation_time, frequency, amplitude)

            actions = torch.zeros((env.num_envs, len(ARM_JOINTS_IDS)), device=env.device, dtype=torch.float32)
            arm_joint_idx = ARM_JOINTS_IDS.index(active_joint_id)
            actions[:, arm_joint_idx] = sinusoid

            force = torch.tensor(EXTERNAL_FORCE, device=env.device, dtype=torch.float32).repeat(env.num_envs, 1)
            moment = torch.tensor(EXTERNAL_MOMENT, device=env.device, dtype=torch.float32).repeat(env.num_envs, 1)
        
            if time_elapsed >= EXTERNAL_FORCE_APPLIED_AT:
                env.bucket_force_com = force
                env.bucket_moment_com = moment
                body_ids = torch.tensor([env.m545_measurements.bucket_body_idx[0]], device=env.device)
                env.m545_asset.set_external_force_and_torque(force, moment, body_ids=body_ids)
            else:
                env.bucket_force_com = torch.zeros_like(force)
                env.bucket_moment_com = torch.zeros_like(moment)
                body_ids = torch.tensor([env.m545_measurements.bucket_body_idx[0]], device=env.device)
                env.m545_asset.set_external_force_and_torque(torch.zeros_like(force), torch.zeros_like(moment), body_ids=body_ids)
            print("actions", actions)
            env.step(actions)
            log_data["joint_positions"].append(env.scene['robot'].data.joint_pos.cpu().numpy())
            log_data["joint_velocities"].append(env.scene['robot'].data.joint_vel.cpu().numpy())
            log_data["commanded_velocities"].append(actions.cpu().numpy())
            log_data["external_force"].append(EXTERNAL_FORCE)
            log_data["external_moment"].append(EXTERNAL_MOMENT)
            log_data["time"].append(time_elapsed)
            log_data["root_position"].append(env.m545_measurements.root_pos_w.cpu().numpy())

            time_elapsed += env.step_dt
            step_count += 1
            if time_elapsed >= TOTAL_EXCITATION_TIME + 5.0:
                print("[INFO]: Total excitation time reached. Stopping simulation.")
                break
            
            # Check if we should stop the simulation based on num_steps
            if args_cli.num_steps is not None and step_count >= args_cli.num_steps:
                print(f"Reached specified {args_cli.num_steps} steps. Stopping simulation.")
                break

    env.close()

    # Delete references to help free the stage before app closes
    del env

    plot_results(log_data, ARM_JOINTS_IDS, args_cli.headless)


def plot_results(log_data, ARM_JOINTS_IDS, headless=False):
    """
    Saves the logged joint data plots to files.
    """
    time = log_data["time"]
    joint_positions = np.array(log_data["joint_positions"])
    joint_velocities = np.array(log_data["joint_velocities"])
    commanded_velocities = np.array(log_data["commanded_velocities"])
    root_positions = np.array(log_data["root_position"])

    # Create output directory if it doesn't exist
    output_dir = os.path.join(DESKTOP_PATH, "m545_pid_plots")
    os.makedirs(output_dir, exist_ok=True)

    # Plot Root Position
    plt.figure(figsize=(12, 6))
    labels = ['X', 'Y', 'Z']
    for i in range(3):
        plt.plot(time, root_positions[:, 0, i], label=f'Root Position {labels[i]}')
    plt.axvline(x=EXTERNAL_FORCE_APPLIED_AT, color='r', linestyle='--', label='External Force Applied')
    plt.title('Root Position Over Time')
    plt.xlabel('Time (s)')
    plt.ylabel('Position (m)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, "root_position.png"))
    plt.close()

    # Plot Joint Positions
    plt.figure(figsize=(12, 6))
    for i, joint_id in enumerate(ARM_JOINTS_IDS):
        plt.plot(time, joint_positions[:, 0, i], label=f'Joint {joint_id} Position')
    plt.axvline(x=EXTERNAL_FORCE_APPLIED_AT, color='r', linestyle='--', label='External Force Applied')
    plt.title('Arm Joint Positions Over Time')
    plt.xlabel('Time (s)')
    plt.ylabel('Position (rad)')
    plt.legend()
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, "joint_positions.png"))
    plt.close()

    # Plot Joint Velocities vs Commanded Velocities
    for i, joint_id in enumerate(ARM_JOINTS_IDS):
        plt.figure(figsize=(12, 6))
        plt.plot(time, joint_velocities[:, 0, i], label=f'Joint {joint_id} Velocity (Actual)')
        plt.plot(time, commanded_velocities[:, 0, i], label=f'Joint {joint_id} Velocity (Commanded)', linestyle='--')
        plt.axvline(x=EXTERNAL_FORCE_APPLIED_AT, color='r', linestyle='--', label='External Force Applied')
        plt.title(f'Joint {joint_id} Velocities Over Time')
        plt.xlabel('Time (s)')
        plt.ylabel('Velocity (rad/s)')
        plt.ylim(-0.1, 0.1)
        plt.legend()
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, f"joint_{joint_id}_velocities.png"))
        plt.close()


if __name__ == "__main__":
    try:
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        simulation_app.close()