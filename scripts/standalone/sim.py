# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os
from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
parser.add_argument("--num_steps", type=int, default=None, help="Number of steps after which to stop the simulation. If not provided, the simulation continues indefinitely.")

# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "m545_sim"
args_cli.num_envs = 10

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import torch
import traceback

import carb

# from isaaclab_tasks.manager_based.m545.devices.gamepad.gamepad_control_driving_and_arm import m545Gamepad_driving_and_arm

# --- Integration of Inverse Dynamics Controller ---
from moleworks_ext.tasks.sim.sim_env import MoleSimEnv
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsController, InverseDynamicsControllerCfg
from isaaclab.sim import SimulationContext
import isaacsim.core.utils.torch as torch_utils
import numpy as np
from moleworks_ext.tasks.sim.sim_cfg import MoleSimEnvCfg
from isaaclab.envs.manager_based_env import ManagerBasedEnv


def main():
    """Zero actions agent with Orbit environment."""

    # teleop_interface = m545Gamepad_driving_and_arm(v_x_sensitivity=5, v_y_sensitivity=0.5, omega_z_sensitivity=1)
    # parse env configuration
    env_cfg = MoleSimEnvCfg()
    env_cfg.num_envs = args_cli.num_envs
    env = MoleSimEnv(cfg=env_cfg)

    env.reset()
    time = 0
    step_count = 0
    # run everything in inference mode
    with torch.inference_mode():


        # compute zero actions
        while simulation_app.is_running():
            # velocity, steering, cabin_input, arm_input = teleop_interface.advance()
            actions = torch.zeros((env.num_envs, len(env.m545_asset.joint_names)), device=env.device, dtype=torch.float32)
            # actions[:, WHEEL_JOINT_IDS] = wheel_vel
            # actions[:, FLEX_JOINT_IDS] = flex_vel
            # actions[:, ARM_JOINT_IDS] = arm_vel
            obs, infos = env.step(actions)
            time += env.step_dt
            step_count += 1
            
            # Check if we should stop the simulation based on num_steps
            if args_cli.num_steps is not None and step_count >= args_cli.num_steps:
                print(f"Reached specified {args_cli.num_steps} steps. Stopping simulation.")
                break

    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()