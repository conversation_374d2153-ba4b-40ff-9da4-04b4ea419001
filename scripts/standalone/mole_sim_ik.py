# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

"""Launch Isaac Sim Simulator first."""

import traceback
import argparse
import time as time_module  # Renamed to avoid confusion with simulation time

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
parser.add_argument("--num_steps", type=int, default=None, help="Number of steps after which to stop the simulation. If not provided, the simulation continues indefinitely.")

# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "m545_sim"
args_cli.num_envs = 1

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import torch
import carb
# --- Integration of Inverse Dynamics Controller ---
from moleworks_ext.tasks.sim.sim_env import MoleSimEnv
from moleworks_ext.tasks.sim.sim_ik_cfg import MoleSimEnvCfg


def main():
    """Run the simulation with the combined Differential IK and Inverse Dynamics action term."""

    # Parse environment configuration
    env_cfg = MoleSimEnvCfg()
    env_cfg.num_envs = args_cli.num_envs
    env = MoleSimEnv(cfg=env_cfg)

    # Reset the environment
    env.reset()
    sim_time = 0.0
    dt = env.step_dt  # Time  step from the environment
    step_count = 0
 
    # Initialize desired EE pose based on current pose
    robot = env.scene['robot']
    # Ensure that the robot's data is updated before starting
    robot.update(dt=dt)

    while simulation_app.is_running():
        # Update robot data
        robot.update(dt=dt)

        # Define a desired end-effector velocity (e.g., move forward)
        desired_velocity = torch.tensor([
            [0.4, 0.0, 0.0, 0.0, 0.0, 0.0]  # Action for each environment
        ], device=env.device).repeat(env.num_envs, 1)  # Shape: (num_envs, 3)
        obs, infos = env.step(desired_velocity)
        # Increment simulation time
        sim_time += dt
        step_count += 1
        
        # Check if we should stop the simulation based on num_steps
        if args_cli.num_steps is not None and step_count >= args_cli.num_steps:
            print(f"Reached specified {args_cli.num_steps} steps. Stopping simulation.")
            break

    # Close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()