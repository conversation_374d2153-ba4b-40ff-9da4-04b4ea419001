#!/usr/bin/env python3
# -----------------------------------------------------------------------------
# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
# -----------------------------------------------------------------------------

"""
Navigation Teleoperation Environment Interface

Launches the navigation simulation environment and uses a gamepad to control the robot.
Wheel velocity (applied equally to all wheels) and steering (applied to steering joints)
are teleoperated via the gamepad.

Usage:
    python nav_teleop.py --num_envs 10 --num_steps 100
"""

import argparse
from isaaclab.app import AppLauncher


parser = argparse.ArgumentParser(description="Navigation Teleoperation Environment Interface")
parser.add_argument("--num_envs", type=int, default=10, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default="m545_sim", help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment.")
parser.add_argument("--num_steps", type=int, default=None, help="Number of simulation steps.")
AppLauncher.add_app_launcher_args(parser)
args = parser.parse_args()
args.enable_cameras = True

# Launch Omniverse app
app_launcher = AppLauncher(args)
simulation_app = app_launcher.app

import carb
import traceback
import torch


# Environment imports
from moleworks_ext.tasks.sim.sim_env import MoleSimEnv
from moleworks_ext.tasks.driving.env_cfg.mole_nav_env_cfg import NavEnvCfg


def main():

    # Configure and create the environment
    env_cfg = NavEnvCfg()
    env_cfg.num_envs = args.num_envs
    env = MoleSimEnv(cfg=env_cfg)
    env.reset()

    # Create navigation teleoperation interface
    # teleop_interface = NavGamepad(v_sensitivity=1.0, s_sensitivity=0.5)
    # Optional: add callback for resetting the environment (e.g. gamepad button X)
    # teleop_interface.add_callback(carb.input.GamepadInput.X, env.reset)

    step_count = 0

    try:
        with torch.inference_mode():
            while simulation_app.is_running():
                # Get teleop commands: wheel velocity (scalar) and steering (4-element vector)
                # Instead of using teleop_interface, set default values
                wheel_velocity = 0.0
                steering = [0.0, 0.0, 0.0, 0.0]
                
                actions = torch.zeros((env.num_envs, len(env.m545_asset.joint_names)), device=env.device, dtype=torch.float32)

                # Prepare action tensor:
                # First 4 joints: steering commands, Last 4 joints: wheel velocities.
                steering_tensor = (
                    torch.tensor(steering, device=env.device, dtype=torch.float32)
                    .unsqueeze(0)
                    .repeat(env.num_envs, 1)
                )
                wheel_tensor = torch.full(
                    (env.num_envs, 4),
                    float(wheel_velocity),
                    device=env.device,
                    dtype=torch.float32,
                )
                actions = torch.cat([steering_tensor, wheel_tensor], dim=1)

                # Step the environment
                obs, infos = env.step(actions)
                step_count += 1

                if args.num_steps is not None and step_count >= args.num_steps:
                    print(f"Reached {args.num_steps} steps. Stopping simulation.")
                    break
    except Exception as e:
        print(f"Error during simulation: {e}")
    finally:
        env.close()
    
    return simulation_app


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()