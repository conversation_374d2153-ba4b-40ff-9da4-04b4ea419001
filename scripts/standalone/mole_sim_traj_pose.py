# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

# Launch Isaac Sim Simulator first.

import traceback
import argparse
import time as time_module  # Renamed to avoid confusion with simulation time
import torch
import carb


def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
    
    # Environment configuration
    parser.add_argument("--num_envs", type=int, default=2, help="Number of environments to simulate.")
    parser.add_argument("--task", type=str, default="m545_sim", help="Name of the task.")
    parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
    parser.add_argument("--num_steps", type=int, default=None, help="Number of steps after which to stop the simulation. If not provided, the simulation continues indefinitely.")
    
    # Trajectory configuration
    parser.add_argument("--trajectory_type", type=str, choices=["circular", "cylindrical", "dumping"], 
                        default="dumping", help="Type of trajectory to generate.")
    parser.add_argument("--target_r", type=float, default=6.0, 
                        help="Radial distance for target pose in cylindrical coordinates.")
    parser.add_argument("--target_theta", type=float, default=0.75 * torch.pi, 
                        help="Angular coordinate (theta in radians) for target pose in cylindrical coordinates.")
    parser.add_argument("--target_z", type=float, default=1.0, help="Height (z) for target pose.")
    
    # AppLauncher arguments
    AppLauncher.add_app_launcher_args(parser)
    
    args = parser.parse_args()
    
    # Set additional default arguments
    return args


from isaaclab.app import AppLauncher
args_cli = parse_arguments()

# Launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app# --- Integration of Inverse Dynamics Controller ---
from isaaclab.markers import VisualizationMarkers, VisualizationMarkersCfg
import isaaclab.sim as sim_utils
import isaaclab.utils.math as math_utils
from isaaclab.utils.math import quat_from_euler_xyz

from moleworks_ext.tasks.sim.sim_env import MoleSimEnv
from moleworks_ext.tasks.sim.sim_ik_cfg import MoleSimEnvCfg
from moleworks_ext.common.controllers.traj_pose import (
    CircularPoseTrajectoryGenerator,
    CylindricalPoseTrajectoryGenerator,
    DumpingTrajectoryGenerator
)



def cylindrical_to_cartesian(r, theta, z):
    """
    Convert cylindrical coordinates to Cartesian coordinates.

    Args:
        r (float or torch.Tensor): Radial distance.
        theta (float or torch.Tensor): Angle in radians.
        z (float or torch.Tensor): Height.

    Returns:
        torch.Tensor: Cartesian coordinates as [x, y, z].
    """
    r = torch.tensor(r) if not isinstance(r, torch.Tensor) else r
    theta = torch.tensor(theta) if not isinstance(theta, torch.Tensor) else theta
    z = torch.tensor(z) if not isinstance(z, torch.Tensor) else z

    x = r * torch.cos(theta)
    y = r * torch.sin(theta)
    return torch.stack((x, y, z), dim=-1)


def transform_to_world_frame(local_pos, base_pos, base_quat):
    """
    Transform a position from the base frame to the world frame.

    Args:
        local_pos (torch.Tensor): Position in the base frame.
        base_pos (torch.Tensor): Base position in the world frame.
        base_quat (torch.Tensor): Base orientation in the world frame.

    Returns:
        torch.Tensor: Position in the world frame.
    """
    rotated_pos = math_utils.quat_rotate(base_quat, local_pos)
    world_pos = rotated_pos + base_pos
    return world_pos


def create_trajectory_generator(trajectory_type, env, args_cli):
    """
    Factory function to create the appropriate trajectory generator.

    Args:
        trajectory_type (str): Type of trajectory to generate.
        env (MoleSimEnv): The simulation environment.
        args_cli (argparse.Namespace): Parsed command-line arguments.

    Returns:
        TrajectoryGenerator: An instance of the selected trajectory generator.
    """
    num_envs = env.num_envs
    device = env.device

    # Update measurements
    env.m545_measurements.update_measurements()
    initial_pos = env.m545_measurements.bucket_pos_w
    initial_quat = env.m545_measurements.quat_bucket_w
    print("initial_pos: ", initial_pos)
    print("initial_quat: ", initial_quat)
    print("initial_quat euler: ", math_utils.euler_xyz_from_quat(initial_quat))

    # Define target orientation
    target_euler = torch.zeros((num_envs, 3), device=device)
    # this blade perpendicular to the floor 
    target_euler[:, 1] = -torch.pi/2
    target_quat = quat_from_euler_xyz(target_euler[:, 0], target_euler[:, 1], target_euler[:, 2])

    if trajectory_type == "circular":
        traj_gen = CircularPoseTrajectoryGenerator(device, debug_vis=True)

        center = initial_pos + torch.tensor([1.0, 0.0, 0.0], device=device).repeat(num_envs, 1)
        radius = torch.full((num_envs, 1), 1.0, device=device)
        height = initial_pos[..., 2:3] + 1

        # Define start_pos and start_quat if available
        # For example, assuming initial_quat is defined
        start_pos = initial_pos.clone()
        start_quat = initial_quat.clone()
        circular_target_euler = torch.zeros((num_envs, 3), device=device)
        circular_target_quat = quat_from_euler_xyz(circular_target_euler[:, 0], circular_target_euler[:, 1], circular_target_euler[:, 2])

        traj_gen.initialize(
            center=center,
            radius=radius,
            height=height,
            target_orientation=circular_target_quat,
            period=10.0,
            start_pos=start_pos,      # Added start_pos
            start_quat=start_quat     # Added start_quat
        )

    elif trajectory_type == "cylindrical":
        traj_gen = CylindricalPoseTrajectoryGenerator(device, debug_vis=True)

        target_cartesian = cylindrical_to_cartesian(
            r=args_cli.target_r,
            theta=args_cli.target_theta,
            z=args_cli.target_z
        ).to(device).repeat(num_envs, 1)

        target_pos_world = transform_to_world_frame(
            target_cartesian,
            base_pos=env.m545_measurements.root_pos_w,
            base_quat=env.m545_measurements.root_quat_w
        )

        traj_gen.initialize(
            start_pos=initial_pos,
            start_quat=initial_quat,
            target_pos=target_pos_world,
            target_quat=target_quat,
            duration=5.0
        )

    elif trajectory_type == "dumping":
        traj_gen = DumpingTrajectoryGenerator(device, debug_vis=True)

        target_cartesian = cylindrical_to_cartesian(
            r=args_cli.target_r,
            theta=args_cli.target_theta,
            z=args_cli.target_z
        ).to(device).repeat(num_envs, 1)

        target_pos_world = transform_to_world_frame(
            target_cartesian,
            base_pos=env.m545_measurements.root_pos_w,
            base_quat=env.m545_measurements.root_quat_w
        )

        traj_gen.initialize(
            start_pos=initial_pos,
            start_quat=initial_quat,
            target_pos=target_pos_world,
            target_quat=target_quat,
            duration=5.0,
            dump_start_time=0.8  # Start opening bucket at 80% of trajectory
        )

    else:
        raise ValueError(f"Unsupported trajectory type: {trajectory_type}")

    # Setup visualization
    traj_gen.setup_visualization()

    return traj_gen


def main():
    """Main function to run the simulation."""


    try:
        # Parse environment configuration
        env_cfg = MoleSimEnvCfg()
        env_cfg.num_envs = args_cli.num_envs
        env = MoleSimEnv(cfg=env_cfg)

        # Reset the environment
        env.reset()
        sim_time = 0.0
        dt = env.step_dt
        step_count = 0

        # Create trajectory generator
        traj_gen = create_trajectory_generator(args_cli.trajectory_type, env, args_cli)

        robot = env.scene['robot']

        while simulation_app.is_running():
            # Update robot data
            robot.update(dt=dt)

            # Get current pose and base orientation
            current_pos = env.m545_measurements.bucket_pos_w
            current_quat = env.m545_measurements.quat_bucket_w
            base_pos = env.m545_measurements.root_pos_w
            base_quat = env.m545_measurements.root_quat_w  # Get base frame orientation
            print("current base pos : ", base_pos)
            # Generate velocity command with base orientation
            desired_velocity = traj_gen.get_velocity_command(
                current_pos,
                current_quat,
                dt=dt,
                base_quat=base_quat,
                base_pos=base_pos
            )

            # Update visualization
            traj_gen.update_visualization(env.scene.env_origins)

            # Take environment step (desired_velocity is already batched)
            obs, infos = env.step(desired_velocity)

            # Print debug info (showing first environment only)
            if sim_time % 1.0 < dt:  # Print every second
                euler_angles = math_utils.euler_xyz_from_quat(current_quat[0].unsqueeze(0))[0]
                print(f"Current Position: {current_pos[0]}")
                print(f"Current Orientation (Euler XYZ): {euler_angles}")
                print(f"Velocity Command: {desired_velocity[0]}")
                print(f"absolute vel command: {torch.norm(desired_velocity[:, :3], dim=-1)}")

            # Increment simulation time
            sim_time += dt
            step_count += 1
            
            # Check if we should stop the simulation based on num_steps
            if args_cli.num_steps is not None and step_count >= args_cli.num_steps:
                print(f"Reached specified {args_cli.num_steps} steps. Stopping simulation.")
                break

    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # Close the environment and simulation app
        env.close()
        simulation_app.close()


if __name__ == "__main__":
    main()
