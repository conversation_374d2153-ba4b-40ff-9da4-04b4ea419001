{"version": "2.0.0", "tasks": [{"label": "prepare environment", "type": "shell", "command": "bash", "args": ["-c", "source /opt/ros/humble/setup.bash && source ${workspaceFolder}/exts/moleworks_ext/moleworks_ext/ros/ros2_ws/install/setup.bash && set -a && source ${workspaceFolder}/docker/.isaac_env && set +a && env > ${workspaceFolder}/.env_combined"], "problemMatcher": [], "options": {"shell": {"executable": "/bin/bash", "args": ["-c"]}}}]}