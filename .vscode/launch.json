{"version": "0.2.0", "configurations": [{"name": "Python: Debug with ROS", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "envFile": "${workspaceFolder}/.env_combined", "preLaunchTask": "prepare environment", "cwd": "${workspaceFolder}", "justMyCode": false}, {"name": "Python: Debug", "type": "debugpy", "request": "launch", "program": "${file}", "console": "integratedTerminal", "envFile": "${workspaceFolder}/.env_local", "cwd": "${workspaceFolder}", "justMyCode": false}]}