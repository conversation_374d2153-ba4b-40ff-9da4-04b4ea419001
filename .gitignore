# Omniverse
**/*.dmp
**/.thumbs
**/.cache
**/.local
**/.nvidia-omniverse

# Python
.DS_Store
**/*.egg-info/
**/__pycache__/
**/.pytest_cache/
**/*.pyc
**/*.pb

# IDE
**/.idea/
# Don't ignore the top-level .vscode directory as it is
# used to configure VS Code settings
!.vscode

# Outputs
**/runs/*
**/logs/* 
**/recordings/*
**/output/*
**/outputs/*
**/videos/*
**/wandb/*
**/.neptune/*
docker/artifacts/
*.tmp

# Docker/Singularity
**/*.sif
docker/cluster/exports/
docker/.container.cfg

# Isaac-Sim packman
_isaac_sim*
_repo
_build
.lastformat

# Singularity 
docker/cluster/exports/*

# docker
docker/.env.moleworks_ext
docker/.env.moleworks_ext-dev
docker/.env.moleworks_ext-ros2
docker/cluster/.env.cluster
# Allow template files
!docker/.env.moleworks_ext.template
!docker/.env.moleworks_ext-dev.template
!docker/.env.moleworks_ext-ros2.template
!docker/cluster/.env.cluster.template
# play and train scripts
scripts/rl/*
# isaaclab symlink
_isaaclab




# vscode env
.env
.env_ros
.env_combined
# ros 
**/ros2_ws/install/
**/ros2_ws/build/
**/ros2_ws/log/

# scripts/rl/*

# cursor
.cursorrules
.cursorignore