# Use the basic isaaclab image as the base
FROM isaac-lab-base AS base

# =========================
# Build Arguments and ENV
# =========================
ARG EXTENSION_NAME_ARG
ARG EXT_PATH_ARG
ARG DOCKER_EXT_PATH_ARG
ARG DOCKER_USER_NAME_ARG
ARG DOCKER_USER_HOME_ARG

ENV EXT_PATH=${EXT_PATH_ARG} \
    EXTENSION_NAME=${EXTENSION_NAME_ARG} \
    DOCKER_EXT_PATH=${DOCKER_EXT_PATH_ARG} \
    DOCKER_USER_NAME=${DOCKER_USER_NAME_ARG} \
    DOCKER_USER_HOME=${DOCKER_USER_HOME_ARG} \
    HOME=${DOCKER_USER_HOME}

# =========================
# Create User Early
# =========================
RUN useradd -d ${DOCKER_USER_HOME} -s /bin/bash ${DOCKER_USER_NAME}

# =========================
# Install System Dependencies
# =========================
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    figlet \
    gosu \
    apt-utils \
    libeigen3-dev \
    locate \
    wget \
    pkg-config \
    dialog \
    tasksel \
    curl \
    python3-pip \
    git \
    git-lfs \
    rsync \
    software-properties-common

# =========================
# Configure Git LFS
# =========================
RUN git lfs install

# =========================
# Setup Extension Directories
# =========================
RUN mkdir -p ${DOCKER_EXT_PATH}/data ${DOCKER_EXT_PATH}/logs && \
    chmod -R 777 ${DOCKER_EXT_PATH}

# =========================
# Copy and Install moleworks_ext
# =========================
COPY --chown=root:root source/moleworks_ext ${DOCKER_EXT_PATH}/source/moleworks_ext
RUN --mount=type=cache,target=/root/.cache/pip \
    cd ${DOCKER_EXT_PATH} && \
    ${ISAACLAB_PATH}/isaaclab.sh -p -m pip install -e source/moleworks_ext

# =========================
# Clean Up moleworks_ext Directory
# =========================
RUN rm -rf ${DOCKER_EXT_PATH}/source/moleworks_ext

# =========================
# Create Symlinks for Sensors
# =========================
RUN mkdir -p /workspace/isaaclab/source/exts/ && \
    ln -s /workspace/isaaclab/_isaac_sim/exts/isaacsim.sensors.rtx /workspace/isaaclab/source/exts/isaacsim.sensors.rtx

# =========================
# Clone and Install rsl_rl
# =========================
RUN git clone https://github.com/leggedrobotics/rsl_rl.git /tmp/rsl_rl && \
    cd /tmp/rsl_rl && \
    ${ISAACLAB_PATH}/isaaclab.sh -p -m pip install .

# =========================
# Python packages installation
# =========================
RUN ${ISAACLAB_PATH}/_isaac_sim/python.sh -m pip install warp-lang ruamel.yaml

# =========================
# Set Ownership and Permissions
# =========================
RUN chown -R ${DOCKER_USER_NAME}:${DOCKER_USER_NAME} /isaac-sim/kit && \
    chmod -R 777 /isaac-sim/kit

# =========================
# Environment
# =========================
COPY docker/bashrc /home/<USER>
RUN chmod a+rwx /home/<USER>

COPY docker/entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh

# =========================
# Set Up Python Aliases
# =========================
RUN echo "alias python3='${ISAACLAB_PATH}/_isaac_sim/python.sh'" >> /home/<USER>
    echo "alias python='${ISAACLAB_PATH}/_isaac_sim/python.sh'" >> /home/<USER>

# =========================
# Final Configuration
# =========================
WORKDIR ${DOCKER_USER_HOME}
ENTRYPOINT ["/entrypoint.sh"] 
