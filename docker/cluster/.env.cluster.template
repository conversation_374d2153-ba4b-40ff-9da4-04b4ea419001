###
# Cluster specific settings
###

# Job scheduler used by cluster.
# Currently supports PBS and SLURM
CLUSTER_USER=your_username
EXTENSION_NAME=moleworks_ext
   
CLUSTER_JOB_SCHEDULER=SLURM
# Docker cache dir for <PERSON> Sim (has to end on docker-isaac-sim)
# e.g. /cluster/scratch/$USER/docker-isaac-sim
CLUSTER_ISAAC_SIM_CACHE_DIR=/cluster/scratch/$CLUSTER_USER/docker-isaac-sim
# overload the isaaclab directory to the extension dir 
# so we can use the cluster scripts of isaaclab out of the box 
CLUSTER_ISAACLAB_DIR=/cluster/home/<USER>/$EXTENSION_NAME
# Cluster login
CLUSTER_LOGIN=$<EMAIL>
# Cluster scratch directory to store the SIF file
# e.g. /cluster/scratch/$USER
CLUSTER_SIF_PATH=/cluster/scratch/$CLUSTER_USER
# Remove the temporary isaaclab code copy after the job is done
REMOVE_CODE_COPY_AFTER_JOB=false
# Python executable within Isaac Lab directory to run with the submitted job
CLUSTER_PYTHON_EXECUTABLE=scripts/rl/rsl_rl/train.py

# WANDB (optional)
#WANDB_API_KEY="your_wandb_api_key"
#WANDB_USERNAME="your_wandb_username" 