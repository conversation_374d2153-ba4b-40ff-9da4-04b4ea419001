#!/bin/bash
# sync_logs.sh: Synchronize logs from remote cluster experiments.
# Usage: ./sync_logs.sh [--remove] [local_log_folder]
#   --remove         Remove remote experiment directories after sync.
#   local_log_folder Destination folder for logs (default: ./logs)

set -e

usage() {
    echo "Usage: $0 [--remove] [local_log_folder]"
    echo "  --remove         Remove remote experiment directories after sync."
    echo "  local_log_folder Destination folder for logs (default: ./logs)"
    exit 1
}

# Process optional flags
REMOVE_REMOTE=false
while [[ $# -gt 0 ]]; do
    case "$1" in
        --remove)
            REMOVE_REMOTE=true
            shift
            ;;
        -h|--help)
            usage
            ;;
        *)
            break
            ;;
    esac
done

# Local destination folder (default to ./logs if not provided)
LOCAL_DEST=${1:-./logs}
mkdir -p "$LOCAL_DEST"

# Load .env.cluster (assumed to be in the same directory as this script)
SCRIPT_DIR=$(dirname "$0")
if [ -f "$SCRIPT_DIR/.env.cluster" ]; then
    source "$SCRIPT_DIR/.env.cluster"
else
    echo "Error: .env.cluster not found in $SCRIPT_DIR"
    exit 1
fi

# Check required environment variables are set
if [ -z "$CLUSTER_ISAACLAB_DIR" ] || [ -z "$EXTENSION_NAME" ] || [ -z "$CLUSTER_LOGIN" ]; then
    echo "Error: Missing required environment variables in .env.cluster."
    exit 1
fi

# Determine the remote base directory.
# We assume that your timestamped experiment folders (e.g. moleworks_ext_20250214_1500)
# are created in the parent directory of CLUSTER_ISAACLAB_DIR.
REMOTE_BASE=$(dirname "$CLUSTER_ISAACLAB_DIR")

# Build the pattern to match all experiment directories
REMOTE_PATTERN="${EXTENSION_NAME}_*"
echo "Searching for remote experiment directories in ${REMOTE_BASE} matching ${REMOTE_PATTERN} ..."

# List matching directories on the remote cluster
REMOTE_DIRS=$(ssh "$CLUSTER_LOGIN" "ls -d ${REMOTE_BASE}/${REMOTE_PATTERN} 2>/dev/null" || true)
if [ -z "$REMOTE_DIRS" ]; then
    echo "No remote experiment directories found matching pattern ${REMOTE_PATTERN} in ${REMOTE_BASE}"
    exit 0
fi

# Loop over each found remote directory
for remote_dir in $REMOTE_DIRS; do
    BASENAME=$(basename "$remote_dir")
    echo "Syncing logs from remote experiment: $remote_dir"
    
    # Assume that the log folder is inside each experiment folder as 'log'
    REMOTE_LOG_DIR="${remote_dir}/log/"
    
    # Create a corresponding local subfolder (to avoid duplicate logs)
    LOCAL_SUBDIR="${LOCAL_DEST}/${BASENAME}"
    mkdir -p "$LOCAL_SUBDIR"
    
    # Use rsync to synchronize the log folder from remote to local
    rsync -avz --progress "$CLUSTER_LOGIN:${REMOTE_LOG_DIR}" "$LOCAL_SUBDIR/"
    
    # If the sync was successful and the flag was provided, remove the remote folder
    if [ $? -eq 0 ]; then
         echo "Successfully synced logs for ${BASENAME}"
         if [ "$REMOVE_REMOTE" = true ]; then
             echo "Removing remote directory: $remote_dir"
             ssh "$CLUSTER_LOGIN" "rm -rf ${remote_dir}"
         fi
    else
         echo "Error syncing logs for ${BASENAME}"
    fi
done

echo "All experiments have been synchronized."
