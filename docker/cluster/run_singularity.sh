#!/usr/bin/env bash
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"
# Define custom paths for environment files
CUSTOM_ENV_CLUSTER="$SCRIPT_DIR/.env.cluster"
CUSTOM_ENV_BASE="$SCRIPT_DIR/../.env.moleworks_ext"

# Source the environment files to load your variables
source "$CUSTOM_ENV_CLUSTER"
source "$CUSTOM_ENV_BASE"

# Raise warnings if the WandB API credentials are missing
if [ -z "${WANDB_USERNAME}" ]; then
  echo "Warning: WANDB_USERNAME is not set. WandB API credentials may be incomplete." >&2
fi

if [ -z "${WANDB_API_KEY}" ]; then
  echo "Warning: WANDB_API_KEY is not set. WandB API credentials may be incomplete." >&2
fi

# Export variables with the SINGULARITYENV_ prefix to pass them into the container
export APPTAINERENV_WANDB_API_KEY="${WANDB_API_KEY}"
export APPTAINERENV_WANDB_USERNAME="${WANDB_USERNAME}"

# If you have more variables to pass, do the same:

# Path to the original run_singularity.sh (from the other repo)
ORIGINAL_SCRIPT="$SCRIPT_DIR/../../_isaaclab/docker/cluster/run_singularity.sh"

# Call the original script with overridden environment paths
"$ORIGINAL_SCRIPT" "$1" "$2" "$CUSTOM_ENV_CLUSTER" "$CUSTOM_ENV_BASE" "${@:3}"