# Cluster Usage Guide

## Overview
This guide provides instructions on how to submit jobs, manage Docker containers, and synchronize logs for Isaac Lab experiments on the cluster.

For Docker-related setup, refer to the [Isaac Lab Docker Setup Guide](docker_setup.md).

## Table of Contents

- [Prerequisites](#prerequisites)
- [Setting Up Environment Variables](#setting-up-environment-variables)
- [Wrapping with Singularity](#wrapping-with-singularity)
- [Submitting a Job to the Cluster](#submitting-a-job-to-the-cluster)
- [Synchronizing Experiment Logs](#synchronizing-experiment-logs)
- [Troubleshooting](#troubleshooting)
- [Debugging Configurations](#debugging-configurations)

## Prerequisites

Before using the cluster, ensure you have:

- SSH access to the cluster.
- The `.env.cluster` file configured with appropriate values (see below).

## Setting Up Environment Variables

1. **Create your configuration file**:

   Copy the template file to create your own configuration:
   ```bash
   cp docker/cluster/.env.cluster.template docker/cluster/.env.cluster
   ```

2. **Edit the file**:

   Update the values in `.env.cluster` with your specific settings:
   - `CLUSTER_USER`: Your cluster username
   - `EXTENSION_NAME`: Name of the repository (usually `moleworks_ext`)
   - `CLUSTER_LOGIN`: SSH login string (e.g., `<EMAIL>`)
   - `CLUSTER_ISAACLAB_DIR`: Base directory for experiments
   - `CLUSTER_SIF_PATH`: Directory to store Singularity images
   - `CLUSTER_PYTHON_EXECUTABLE`: Path to the Python script to run

3. **Configuration details**:

   - `CLUSTER_JOB_SCHEDULER`: Job scheduler used by the cluster (SLURM or PBS)
   - `CLUSTER_ISAAC_SIM_CACHE_DIR`: Cache directory for Isaac Sim
   - `REMOVE_CODE_COPY_AFTER_JOB`: Whether to remove temporary code copy after job completion
   - `WANDB_API_KEY` and `WANDB_USERNAME`: Optional Weights & Biases configuration

## Wrapping with Singularity

To wrap your Docker image with Singularity:

1. **Ensure Naming**: The image must be named `isaac-lab-<extension_name>`.
2. **Push to the Cluster**:

    ```bash
    cd docker/cluster
    ./cluster_interface.sh push <extension_name>
    ```

## Submitting a Job to the Cluster

### Single GPU Training (Original Method)

1. **Set the script** in `CLUSTER_PYTHON_EXECUTABLE` within `.env.cluster`.
2. **Submit a job**:

    ```bash
    cd docker/cluster
    ./cluster_interface.sh job <extension_name> <command>
    ```

For example, to run the `rls_rl/train.py` script:

```bash
cd docker/cluster
./cluster_interface.sh job <extension_name> --headless --task <task_name>
```

### Multi-GPU Training (New Method)

For multi-GPU distributed training, use the new convenience script:

```bash
cd docker/cluster

# Train with 2 A100 GPUs
./submit_multi_gpu_job.sh --gpus 2 --gpu-type a100_80gb

# Train with 4 RTX 3090 GPUs with custom parameters
./submit_multi_gpu_job.sh --gpus 4 --gpu-type rtx_3090 --num-envs 128000 --max-iterations 2000

# Train with 8 GPUs for large-scale experiments
./submit_multi_gpu_job.sh --gpus 8 --gpu-type a100_80gb --num-envs 256000
```

See `MULTI_GPU_GUIDE.md` for detailed multi-GPU training instructions.

### Example

To submit a job for the `moleworks_ext` extension with the `Isaac-m545-digging` task:

```bash
cd docker/cluster
# Single GPU
./cluster_interface.sh job moleworks_ext --headless --task Isaac-m545-digging

# Multi-GPU (4 GPUs)
./submit_multi_gpu_job.sh --gpus 4 --task Isaac-m545-digging --num-envs 128000
```

## Synchronizing Experiment Logs

A script (`sync_logs.sh`) is provided to sync logs from remote cluster experiments to your local machine.

### Usage

1. **Ensure `.env.cluster` is configured**.
2. **Run the script**:

    ```bash
    ./sync_logs.sh [--remove] [local_log_folder]
    ```

    - `--remove`: Deletes remote experiment folders after sync.
    - `local_log_folder`: Destination folder for logs (default: `./logs`).

### Example

To sync logs and remove remote experiment folders:

```bash
./sync_logs.sh --remove ~/experiments/logs
```

## Troubleshooting

### Job Submission Issues

- Ensure correct script paths in `.env.cluster`.
- Check cluster job status using:

    ```bash
    squeue -u $USER
    ```

### Log Synchronization Issues

- Ensure `sync_logs.sh` is executable:

    ```bash
    chmod +x sync_logs.sh
    ```
- Verify SSH access to the cluster.
- Check for sufficient local storage.

For Docker-specific issues, see the [Isaac Lab Docker Setup Guide](docker_setup.md).

## Debugging Configurations

The project provides two debugging configurations in `.vscode/launch.json`:

### 1. Python: Debug with ROS
This configuration is available only in containers with ROS installed. To use it:

1. First attach VSCode to the running container
2. Select "Python: Debug with ROS" from the debug configurations
3. The configuration uses the `.env_combined` environment file
4. Debugging will start in the integrated terminal

### 2. Python: Debug
This is the standard Python debugging configuration that works in any environment:

1. Select "Python: Debug" from the debug configurations
2. The configuration uses the `.env_local` environment file
3. Debugging will start in the integrated terminal

Both configurations support full debugging capabilities including breakpoints, variable inspection, and step-through debugging.

