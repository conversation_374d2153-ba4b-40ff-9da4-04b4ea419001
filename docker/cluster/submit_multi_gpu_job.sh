#!/usr/bin/env bash

# Multi-GPU Training Job Submission Script for SLURM
# This script provides easy configuration for multi-GPU training jobs

set -e  # Exit on any error

# GPU Configuration Table - Based on actual Euler cluster specifications
declare -A GPU_CONFIGS
# Format: "gpu_type:gpus_per_node:cpus_per_node:memory_per_node_gb"
GPU_CONFIGS["rtx_2080_ti"]="8:36:384"  # Some nodes have 36 CPUs, others 128
GPU_CONFIGS["rtx_2080_ti_large"]="8:128:512"  # Large memory nodes
GPU_CONFIGS["rtx_3090"]="8:128:512"
GPU_CONFIGS["rtx_4090"]="8:128:512"
GPU_CONFIGS["titan_rtx"]="8:128:512"
GPU_CONFIGS["quadro_rtx_6000"]="8:128:512"
GPU_CONFIGS["v100"]="8:48:768"  # Some nodes have 48 CPUs, others 40
GPU_CONFIGS["v100_small"]="8:40:512"  # Smaller memory nodes
GPU_CONFIGS["a100-pcie-40gb"]="8:48:768"
GPU_CONFIGS["a100_80gb"]="10:48:1024"

# Default configuration
DEFAULT_NUM_GPUS=2
DEFAULT_GPU_TYPE="rtx_4090"
DEFAULT_TIME="23:00:00"
DEFAULT_TASK="Isaac-m545-digging"
DEFAULT_NUM_ENVS=64000
DEFAULT_MAX_ITERATIONS=1000

# Function to get GPU configuration
get_gpu_config() {
    local gpu_type="$1"
    if [[ -n "${GPU_CONFIGS[$gpu_type]}" ]]; then
        echo "${GPU_CONFIGS[$gpu_type]}"
    else
        echo ""
    fi
}

# Function to list available GPU types
list_gpu_types() {
    echo "Available GPU types:"
    for gpu_type in "${!GPU_CONFIGS[@]}"; do
        IFS=':' read -r gpus_per_node cpus_per_node mem_per_node <<< "${GPU_CONFIGS[$gpu_type]}"
        printf "  %-20s - %2d GPUs/node, %3d CPUs/node, %4d GB/node\n" \
            "$gpu_type" "$gpus_per_node" "$cpus_per_node" "$mem_per_node"
    done | sort
}

# Function to display usage
usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Multi-GPU Training Job Submission Script

OPTIONS:
    -g, --gpus NUM_GPUS         Number of GPUs to use (default: $DEFAULT_NUM_GPUS)
    -t, --gpu-type GPU_TYPE     GPU type to request (default: $DEFAULT_GPU_TYPE)
    -T, --time TIME             Job time limit (default: $DEFAULT_TIME)
    --task TASK                 Training task name (default: $DEFAULT_TASK)
    --num-envs NUM_ENVS         Number of environments (default: $DEFAULT_NUM_ENVS)
    --max-iterations ITERS      Maximum training iterations (default: $DEFAULT_MAX_ITERATIONS)
    --use-multi-gpu-script      Use the multi-GPU training script instead of regular script
    --list-gpu-types            List all available GPU types and their specifications
    -h, --help                  Show this help message

EXAMPLES:
    # Train with 2 A100 GPUs
    $0 --gpus 2 --gpu-type a100_80gb

    # Train with 4 RTX 3090 GPUs with custom task
    $0 --gpus 4 --gpu-type rtx_3090 --task Isaac-m545-single-boulder

    # Train with 8 GPUs and more environments (uses full node)
    $0 --gpus 8 --num-envs 128000 --max-iterations 2000

    # Single GPU training (equivalent to original script)
    $0 --gpus 1

NOTES:
    - Resources are automatically calculated based on actual cluster node configurations
    - Total environments will be distributed across all GPUs
    - Each GPU will handle NUM_ENVS/NUM_GPUS environments
    - Logging and video recording only happen on GPU 0 to avoid conflicts
    - For optimal performance, ensure NUM_ENVS is divisible by NUM_GPUS
    - GPU requests cannot exceed the number of GPUs available per node
EOF
}

# Parse command line arguments
NUM_GPUS=$DEFAULT_NUM_GPUS
GPU_TYPE=$DEFAULT_GPU_TYPE
TIME=$DEFAULT_TIME
TASK=$DEFAULT_TASK
NUM_ENVS=$DEFAULT_NUM_ENVS
MAX_ITERATIONS=$DEFAULT_MAX_ITERATIONS
USE_MULTI_GPU_SCRIPT=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -g|--gpus)
            NUM_GPUS="$2"
            shift 2
            ;;
        -t|--gpu-type)
            GPU_TYPE="$2"
            shift 2
            ;;
        -T|--time)
            TIME="$2"
            shift 2
            ;;
        --task)
            TASK="$2"
            shift 2
            ;;
        --num-envs)
            NUM_ENVS="$2"
            shift 2
            ;;
        --max-iterations)
            MAX_ITERATIONS="$2"
            shift 2
            ;;
        --use-multi-gpu-script)
            USE_MULTI_GPU_SCRIPT=true
            shift
            ;;
        --list-gpu-types)
            list_gpu_types
            exit 0
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Validate inputs
if ! [[ "$NUM_GPUS" =~ ^[0-9]+$ ]] || [ "$NUM_GPUS" -lt 1 ]; then
    echo "Error: NUM_GPUS must be a positive integer"
    exit 1
fi

if ! [[ "$NUM_ENVS" =~ ^[0-9]+$ ]] || [ "$NUM_ENVS" -lt 1 ]; then
    echo "Error: NUM_ENVS must be a positive integer"
    exit 1
fi

# Validate GPU type and get configuration
GPU_CONFIG=$(get_gpu_config "$GPU_TYPE")
if [[ -z "$GPU_CONFIG" ]]; then
    echo "Error: Unknown GPU type '$GPU_TYPE'"
    echo ""
    list_gpu_types
    exit 1
fi

# Parse GPU configuration
IFS=':' read -r GPUS_PER_NODE CPUS_PER_NODE MEM_PER_NODE <<< "$GPU_CONFIG"

# Validate that requested GPUs don't exceed node capacity
if [ "$NUM_GPUS" -gt "$GPUS_PER_NODE" ]; then
    echo "Error: Requested $NUM_GPUS GPUs exceeds the maximum of $GPUS_PER_NODE GPUs per node for $GPU_TYPE"
    echo "Consider using multiple nodes or reducing the number of GPUs"
    exit 1
fi

# Calculate resources based on actual node configuration
# For partial node usage, scale resources proportionally
if [ "$NUM_GPUS" -eq "$GPUS_PER_NODE" ]; then
    # Using full node
    TOTAL_CPUS=$CPUS_PER_NODE
    TOTAL_MEM=$MEM_PER_NODE
else
    # Using partial node - scale resources proportionally
    CPUS_PER_GPU=$((CPUS_PER_NODE / GPUS_PER_NODE))
    MEM_PER_GPU=$((MEM_PER_NODE / GPUS_PER_NODE))
    TOTAL_CPUS=$((NUM_GPUS * CPUS_PER_GPU))
    TOTAL_MEM=$((NUM_GPUS * MEM_PER_GPU))
fi

ENVS_PER_GPU=$((NUM_ENVS / NUM_GPUS))

# Check if NUM_ENVS is divisible by NUM_GPUS for optimal distribution
if [ $((NUM_ENVS % NUM_GPUS)) -ne 0 ]; then
    echo "Warning: NUM_ENVS ($NUM_ENVS) is not divisible by NUM_GPUS ($NUM_GPUS)"
    echo "This may lead to uneven environment distribution across GPUs"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Display configuration
echo "=========================================="
echo "Multi-GPU Training Configuration"
echo "=========================================="
echo "Number of GPUs: $NUM_GPUS"
echo "GPU Type: $GPU_TYPE"
echo "Node Configuration: $GPUS_PER_NODE GPUs, $CPUS_PER_NODE CPUs, ${MEM_PER_NODE}GB per node"
if [ "$NUM_GPUS" -eq "$GPUS_PER_NODE" ]; then
    echo "Resource Usage: Full node"
else
    echo "Resource Usage: Partial node ($NUM_GPUS/$GPUS_PER_NODE GPUs)"
    echo "CPUs per GPU: $CPUS_PER_GPU"
    echo "Memory per GPU: ${MEM_PER_GPU}GB"
fi
echo "Total CPUs: $TOTAL_CPUS"
echo "Total Memory: ${TOTAL_MEM}GB"
echo "Task: $TASK"
echo "Total Environments: $NUM_ENVS"
echo "Environments per GPU: $ENVS_PER_GPU"
echo "Max Iterations: $MAX_ITERATIONS"
echo "Job Time Limit: $TIME"
echo "Use Multi-GPU Script: $USE_MULTI_GPU_SCRIPT"
echo "=========================================="

# Determine which training script to use
if [ "$USE_MULTI_GPU_SCRIPT" = true ] || [ "$NUM_GPUS" -gt 1 ]; then
    PYTHON_SCRIPT="scripts/rl/rsl_rl/train_multi_gpu.py"
    echo "Using multi-GPU training script: $PYTHON_SCRIPT"
else
    PYTHON_SCRIPT="scripts/rl/rsl_rl/train.py"
    echo "Using single-GPU training script: $PYTHON_SCRIPT"
fi

# Export environment variables for the submit script
export NUM_GPUS
export GPU_TYPE
export TOTAL_CPUS
export TOTAL_MEM

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

# Update the cluster configuration to use the appropriate script
export CLUSTER_PYTHON_EXECUTABLE="$PYTHON_SCRIPT"

# Submit the job
echo "Submitting job..."
bash "$SCRIPT_DIR/submit_job_slurm.sh" "$SCRIPT_DIR/../.." moleworks_ext \
    --headless \
    --task "$TASK" \
    --num_envs "$NUM_ENVS" \
    --max_iterations "$MAX_ITERATIONS"

echo "Job submitted successfully!"
echo "Monitor job status with: squeue -u \$USER"
echo "View job output with: tail -f slurm-<job_id>.out"
