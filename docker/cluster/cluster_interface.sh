#!/usr/bin/env bash

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" >/dev/null 2>&1 && pwd )"

source $SCRIPT_DIR/.env.cluster

# Define custom paths for environment files
CUSTOM_ENV_CLUSTER="$SCRIPT_DIR/.env.cluster"

# Define custom codebase path
CUSTOM_CODEBASE_PATH="$SCRIPT_DIR/../../"  # Update this path as needed

# Path to the original cluster_interface.sh
ORIGINAL_SCRIPT="../../_isaaclab/docker/cluster/cluster_interface.sh"

# === Added Echo Statements for Sanity Checks ===
echo "[DEBUG] CUSTOM_ENV_CLUSTER: $CUSTOM_ENV_CLUSTER"
echo "[DEBUG] CUSTOM_CODEBASE_PATH: $CUSTOM_CODEBASE_PATH"
echo "[DEBUG] ORIGINAL_SCRIPT: $ORIGINAL_SCRIPT"
# === End of Added Echo Statements ===

# Export the custom environment path
export ENV_CLUSTER_PATH="$CUSTOM_ENV_CLUSTER"

# Export the custom codebase path
export CODEBASE_PATH="$CUSTOM_CODEBASE_PATH"

# Call the original script with overridden environment paths
"$ORIGINAL_SCRIPT" "$@"

