#!/usr/bin/env bash

# Test Multi-GPU Setup Script
# This script submits a test job to verify multi-GPU configuration

set -e

# Default configuration
NUM_GPUS=${NUM_GPUS:-2}
GPU_TYPE=${GPU_TYPE:-"a100_80gb"}

echo "Testing multi-GPU setup with $NUM_GPUS GPUs of type $GPU_TYPE"

# Load required modules
module load eth_proxy

# Create test job script
cat <<EOT > test_job.sh
#!/bin/bash

#SBATCH --nodes=1
#SBATCH --ntasks=$NUM_GPUS
#SBATCH --cpus-per-task=4
#SBATCH --gpus-per-node=$GPU_TYPE:$NUM_GPUS
#SBATCH --time=00:10:00
#SBATCH --mem=32G
#SBATCH --job-name="test-multi-gpu-$NUM_GPUS"

# Set environment variables for multi-GPU training
export MASTER_ADDR=\$(hostname)
export MASTER_PORT=29500
export WORLD_SIZE=$NUM_GPUS

echo "Starting multi-GPU test on \$(hostname)"
echo "Number of GPUs: $NUM_GPUS"
echo "GPU type: $GPU_TYPE"

# For single GPU test
if [ $NUM_GPUS -eq 1 ]; then
    export CUDA_VISIBLE_DEVICES=0
    export LOCAL_RANK=0
    export RANK=0
    echo "Single GPU test mode"
    
    # Run test script directly
    cd /workspace/moleworks_ext
    python scripts/rl/rsl_rl/test_multi_gpu.py
else
    echo "Multi-GPU test mode with $NUM_GPUS GPUs"
    
    # Use srun to launch distributed test
    srun --ntasks=$NUM_GPUS --ntasks-per-node=$NUM_GPUS bash -c '
        export LOCAL_RANK=\$SLURM_LOCALID
        export RANK=\$SLURM_PROCID
        export CUDA_VISIBLE_DEVICES=\$LOCAL_RANK
        
        echo "Process \$RANK starting on GPU \$LOCAL_RANK"
        
        cd /workspace/moleworks_ext
        python scripts/rl/rsl_rl/test_multi_gpu.py
    '
fi

echo "Multi-GPU test completed"
EOT

# Submit the test job
echo "Submitting test job..."
sbatch test_job.sh

# Clean up
rm test_job.sh

echo "Test job submitted!"
echo "Monitor with: squeue -u \$USER"
echo "View output with: tail -f slurm-*.out"
