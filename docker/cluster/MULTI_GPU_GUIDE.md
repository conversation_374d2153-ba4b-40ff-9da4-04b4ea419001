# Multi-GPU Training Guide

This guide explains how to use the multi-GPU training capabilities for reinforcement learning with RSL-RL on the Euler cluster.

## Overview

The multi-GPU training implementation provides:
- **Distributed Data Parallel (DDP)** training across multiple GPUs
- **Automatic environment distribution** across GPUs
- **Gradient synchronization** using NCCL backend
- **Scalable performance** with near-linear speedup
- **Backward compatibility** with single-GPU training

## Quick Start

### 1. Single Command Multi-GPU Training

Use the convenience script for easy multi-GPU job submission:

```bash
# Train with 2 A100 GPUs
cd docker/cluster
./submit_multi_gpu_job.sh --gpus 2 --gpu-type a100_80gb

# Train with 4 RTX 3090 GPUs
./submit_multi_gpu_job.sh --gpus 4 --gpu-type rtx_3090 --num-envs 128000

# Train with 8 GPUs and custom task
./submit_multi_gpu_job.sh --gpus 8 --task Isaac-m545-single-boulder --max-iterations 2000
```

### 2. Manual Configuration

For more control, set environment variables and use the original submit script:

```bash
# Set multi-GPU configuration
export NUM_GPUS=4
export GPU_TYPE="a100_80gb"
export CPUS_PER_GPU=16

# Submit job
./submit_job_slurm.sh ../.. moleworks_ext --headless --task Isaac-m545-digging --num_envs 128000
```

## Configuration Options

### GPU Types Available on Euler

| GPU Type | Memory | Recommended Use Case |
|----------|--------|---------------------|
| `a100_80gb` | 80GB | Large-scale training, 64K+ environments |
| `rtx_4090` | 24GB | High-performance training, 32K environments |
| `rtx_3090` | 24GB | Standard training, 16K-32K environments |
| `rtx_2080_ti` | 11GB | Small-scale training, 8K-16K environments |
| `v100` | 32GB | Legacy training, 16K-32K environments |

### Environment Distribution

The total number of environments is automatically distributed across GPUs:

```
Environments per GPU = Total Environments / Number of GPUs
```

**Example:**
- Total environments: 64,000
- Number of GPUs: 4
- Environments per GPU: 16,000

### Resource Scaling

Resources scale linearly with the number of GPUs:

| GPUs | CPUs | Memory | Recommended Environments |
|------|------|--------|-------------------------|
| 1 | 16 | 256GB | 16K-32K |
| 2 | 32 | 512GB | 32K-64K |
| 4 | 64 | 1TB | 64K-128K |
| 8 | 128 | 2TB | 128K-256K |

## Performance Considerations

### 1. Environment Distribution

For optimal performance, ensure the number of environments is divisible by the number of GPUs:

```bash
# Good: 64000 environments with 4 GPUs = 16000 per GPU
./submit_multi_gpu_job.sh --gpus 4 --num-envs 64000

# Suboptimal: 65000 environments with 4 GPUs = uneven distribution
./submit_multi_gpu_job.sh --gpus 4 --num-envs 65000
```

### 2. Batch Size Considerations

The effective batch size scales with the number of GPUs:
- Single GPU: `num_envs * num_steps_per_env`
- Multi-GPU: `(num_envs / num_gpus) * num_steps_per_env * num_gpus`

### 3. Learning Rate Scaling

Consider adjusting the learning rate for multi-GPU training:
- **Linear scaling**: `lr_multi_gpu = lr_single_gpu * num_gpus`
- **Square root scaling**: `lr_multi_gpu = lr_single_gpu * sqrt(num_gpus)`

## Training Scripts

### 1. Multi-GPU Training Script

`scripts/rl/rsl_rl/train_multi_gpu.py` - Optimized for distributed training:
- Automatic distributed setup
- Environment distribution
- Synchronized logging
- Video recording on rank 0 only

### 2. Original Training Script

`scripts/rl/rsl_rl/train.py` - Works for both single and multi-GPU:
- RSL-RL handles multi-GPU automatically
- Uses environment variables for configuration

## Monitoring and Debugging

### 1. Job Status

```bash
# Check job status
squeue -u $USER

# View job output
tail -f slurm-<job_id>.out

# Check GPU utilization
srun --jobid=<job_id> --pty nvidia-smi
```

### 2. Multi-GPU Debugging

Enable detailed NCCL logging by setting:
```bash
export NCCL_DEBUG=INFO
```

### 3. Common Issues

**Issue: Out of Memory**
- Reduce `num_envs` per GPU
- Reduce batch size (`num_mini_batches`)
- Use gradient checkpointing

**Issue: Slow Training**
- Check network bandwidth between GPUs
- Ensure environments are evenly distributed
- Monitor GPU utilization

**Issue: Convergence Problems**
- Adjust learning rate for multi-GPU
- Check gradient synchronization
- Verify batch size scaling

## Example Configurations

### Small Scale (2 GPUs)
```bash
./submit_multi_gpu_job.sh \
    --gpus 2 \
    --gpu-type rtx_3090 \
    --num-envs 32000 \
    --max-iterations 1000
```

### Medium Scale (4 GPUs)
```bash
./submit_multi_gpu_job.sh \
    --gpus 4 \
    --gpu-type a100_80gb \
    --num-envs 128000 \
    --max-iterations 2000
```

### Large Scale (8 GPUs)
```bash
./submit_multi_gpu_job.sh \
    --gpus 8 \
    --gpu-type a100_80gb \
    --num-envs 256000 \
    --max-iterations 5000 \
    --time 48:00:00
```

## Advanced Configuration

### Custom SLURM Parameters

Modify `submit_job_slurm.sh` for advanced configurations:

```bash
#SBATCH --constraint="gpu_mem:80gb"  # Specific GPU memory
#SBATCH --exclusive                  # Exclusive node access
#SBATCH --partition=gpu              # Specific partition
```

### Environment Variables

Key environment variables for multi-GPU training:

```bash
export WORLD_SIZE=4           # Number of GPUs
export MASTER_ADDR=hostname   # Master node address
export MASTER_PORT=29500      # Master port
export NCCL_DEBUG=INFO        # NCCL debugging
export CUDA_LAUNCH_BLOCKING=0 # Async CUDA operations
```

## Troubleshooting

### 1. Check RSL-RL Multi-GPU Support

Verify that RSL-RL detects multi-GPU setup:
```python
from rsl_rl.runners import OnPolicyRunner
# Should show distributed configuration in logs
```

### 2. Verify CUDA and NCCL

```bash
# Check CUDA version
nvidia-smi

# Test NCCL
python -c "import torch; print(torch.distributed.is_nccl_available())"
```

### 3. Debug Distributed Training

Add debugging prints to training script:
```python
print(f"Rank: {rank}, Local Rank: {local_rank}, World Size: {world_size}")
print(f"Device: {device}, CUDA Available: {torch.cuda.is_available()}")
```

## Performance Benchmarks

Expected speedup with multi-GPU training:

| GPUs | Speedup | Efficiency |
|------|---------|------------|
| 1 | 1.0x | 100% |
| 2 | 1.8x | 90% |
| 4 | 3.4x | 85% |
| 8 | 6.4x | 80% |

*Note: Actual performance depends on environment complexity, network bandwidth, and GPU type.*
