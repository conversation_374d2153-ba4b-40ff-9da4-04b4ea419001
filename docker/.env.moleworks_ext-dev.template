# User settings
HOST_HOME=/home/<USER>
DOCKER_USER_NAME=your_username
DOCKER_USER_HOME=/home/<USER>

# Extension settings
EXTENSION_NAME=moleworks_ext
EXTENSION_FOLDER=$HOST_HOME/path/to/extension/folder
EXT_PATH=$EXTENSION_FOLDER/$EXTENSION_NAME
DOCKER_EXT_PATH=/workspace/$EXTENSION_NAME

# NVIDIA EULA
ACCEPT_EULA=Y

# Isaac settings
DOCKER_ISAACLAB_PATH=$DOCKER_EXT_PATH
DOCKER_ISAACSIM_ROOT_PATH=/isaac-sim

# WANDB (optional)
WANDB_API_KEY="your_wandb_api_key"
WANDB_USERNAME="your_wandb_username" 