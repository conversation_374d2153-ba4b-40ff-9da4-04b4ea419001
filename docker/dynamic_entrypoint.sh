#!/bin/bash

# ===================================
# Dynamic User Creation for Docker Containers
# ===================================
# This script creates a user with the UID/GID from the host system
# and performs permission setup in the background, allowing immediate
# container access while permissions are being fixed.

# Get UID and GID from environment or use defaults
USER_ID=${LOCAL_UID:-1000}
GROUP_ID=${LOCAL_GID:-1000}
USER_NAME=${DOCKER_USER_NAME:-user}

echo "Starting container with dynamic user creation..."
echo "  UID: $USER_ID"
echo "  GID: $GROUP_ID"
echo "  Username: $USER_NAME"

# Create user and group if they don't exist
groupadd -g $GROUP_ID -o $USER_NAME 2>/dev/null || true
useradd -m -u $USER_ID -g $GROUP_ID -o -s /bin/bash $USER_NAME 2>/dev/null || true

# Add sudo permissions
echo "root ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers
echo "$USER_NAME ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Print welcome message
figlet MOLEWORKS ROS2
echo "Container is ready for use!"
echo "Setting up permissions in the background..."

# Run ownership changes in the background
(
    # These directories are critical, make them accessible immediately
    chmod -R 777 /tmp /var/tmp
    
    # Start with important directories that should be fixed first
    chown -R $USER_NAME:$USER_NAME ${DOCKER_USER_HOME:-/home/<USER>/dev/null
    
    # Fix isaac-sim permissions in the background
    nohup chown -R $USER_NAME:$USER_NAME /isaac-sim/kit 2>/dev/null &
    
    # Optional: notify when complete
    echo "Permission setup completed!" > ${DOCKER_USER_HOME:-/home/<USER>/.permissions_done
) &

# Execute command as the new user
exec gosu $USER_NAME bash --rcfile ${DOCKER_USER_HOME:-/home/<USER>/../bash.bashrc 