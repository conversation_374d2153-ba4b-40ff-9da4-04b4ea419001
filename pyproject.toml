[build-system]
requires = ["setuptools", "toml"]
build-backend = "setuptools.build_meta"

[tool.isort]

atomic = true
profile = "black"
line_length = 120
py_version = 310
skip_glob = ["docs/*", "logs/*"]
group_by_package = true

sections = [
    "FUTURE",
    "STDLIB",
    "THIRDPARTY",
    "ISAACLABPARTY",
    "FIRSTPARTY",
    "LOCALFOLDER",
]
extra_standard_library = [
    "numpy",
    "h5py",
    "open3d",
    "torch",
    "tensordict",
    "bpy",
    "matplotlib",
    "gymnasium",
    "gym",
    "scipy",
    "hid",
    "yaml",
    "prettytable",
    "toml",
    "trimesh",
    "tqdm",
    "psutil",
]
known_thirdparty = [
    "isaacsim.core.api",
    "omni.replicator.core",
    "pxr",
    "omni.kit.*",
    "warp",
    "carb",
    "Semantics",
]
known_isaaclabparty = [
    "isaaclab",
    "isaaclab_tasks",
    "isaaclab_assets",
    "isaaclab_mimic",
    "isaaclab_rl"
]

# Modify the following to include the package names of your first-party code
known_firstparty = "moleworks_ext"
known_local_folder = "config"

[tool.pyright]

exclude = [
    "**/__pycache__",
    "**/docs",
    "**/logs",
    ".git",
    ".vscode",
]

typeCheckingMode = "basic"
pythonVersion = "3.10"
pythonPlatform = "Linux"
enableTypeIgnoreComments = true

# This is required as the CI pre-commit does not download the module (i.e. numpy, torch, prettytable)
# Therefore, we have to ignore missing imports
reportMissingImports = "none"
# This is required to ignore for type checks of modules with stubs missing.
reportMissingModuleSource = "none" # -> most common: prettytable in mdp managers

reportGeneralTypeIssues = "none"       # -> raises 218 errors (usage of literal MISSING in dataclasses)
reportOptionalMemberAccess = "warning" # -> raises 8 errors
reportPrivateUsage = "warning"