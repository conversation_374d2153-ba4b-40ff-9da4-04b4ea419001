repos:
  - repo: https://github.com/python/black
    rev: 23.10.1
    hooks:
      - id: black
        args: ["--line-length", "120", "--preview"]
  - repo: https://github.com/pycqa/flake8
    rev: 6.1.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-simplify, flake8-return]
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: check-symlinks
      - id: destroyed-symlinks
      - id: check-yaml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-executables-have-shebangs
      - id: check-toml
      - id: end-of-file-fixer
      - id: check-shebang-scripts-are-executable
      - id: detect-private-key
      - id: debug-statements
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort (python)
        args: ["--profile", "black", "--filter-files"]
  - repo: https://github.com/asottile/pyupgrade
    rev: v3.15.0
    hooks:
      - id: pyupgrade
        args: ["--py37-plus"]
  - repo: https://github.com/codespell-project/codespell
    rev: v2.2.6
    hooks:
      - id: codespell
        additional_dependencies:
        - tomli
  - repo: https://github.com/pre-commit/pygrep-hooks
    rev: v1.10.0
    hooks:
      - id: rst-backticks
      - id: rst-directive-colons
      - id: rst-inline-touching-normal
