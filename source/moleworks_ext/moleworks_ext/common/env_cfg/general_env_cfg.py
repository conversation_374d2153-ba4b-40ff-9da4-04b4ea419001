"""
    This script is designed to get the general setting for the excavators acrosos all the environments
"""

import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObject, RigidObjectCfg
from isaaclab.sensors import CameraCfg, ContactSensorCfg, RayCasterCfg, patterns
from isaaclab.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg, UsdFileCfg
from moleworks_ext.common.sensors.rtx_lidar import RtxLidarCfg
from moleworks_ext.common.sensors.rtx_lidar.spawner_sensor_cfg.rtx_lidar_sensor_cfg import LidarCfg
from moleworks_ext.common.sim.spawners.from_files.from_files_cfg import GroundPlaneWithVisualMaterial
import torch

# Camera
m545_camera = CameraCfg(
    prim_path="{ENV_REGEX_NS}/Robot/BASE/front_cam",
    update_period=0.1,
    height=480,
    width=640,
    data_types=["distance_to_image_plane", "rgb"],
    spawn=sim_utils.PinholeCameraCfg(
        focal_length=24.0, focus_distance=400.0, horizontal_aperture=20.955, clipping_range=(0.1, 1.0e5)
    ),
    offset=CameraCfg.OffsetCfg(pos=(0.9, 0.2, 2), rot=(0.5, -0.5, 0.5, -0.5), convention="ros"),  # Position
)

# Lidar
m545_lidar = RayCasterCfg(
    prim_path="{ENV_REGEX_NS}/Robot/BASE",
    update_period=0.02,
    offset=RayCasterCfg.OffsetCfg(pos=(0.8, 0.8, 2.25), rot=(0.985, 0, 0.173, 0)),
    attach_yaw_only=False,
    pattern_cfg=patterns.LidarPatternCfg(
        channels=16, vertical_fov_range=(-15.0, 15.0), horizontal_fov_range=(-90.0, 90.0), horizontal_res=0.2
    ),
    debug_vis=True,
    mesh_prim_paths=[
        "/World/ground",
        ],#,"/World/envs/env_.*/Objects_0/"
)

#os1_lidar = RtxLidarCfg(
#        prim_path="{ENV_REGEX_NS}/Robot/os_sensor/os1_lidar",
#        debug_vis=False,
#        optional_data_types=[
#            "azimuth","elevation","emitterId","index","materialId","normal","objectId","velocity"
#        ],
#        offset=RtxLidarCfg.OffsetCfg(pos=(0,0,0), rot=(0.0, 0.0, 0.0, 1.0)), # (0.62,0.31,2.57)
#        spawn=LidarCfg(lidar_type=LidarCfg.LidarType.OUSTER_OS0_REV6_64_1HZ_1024RES_45DEGFOV),
#    )

os1_lidar = RtxLidarCfg(
        prim_path="{ENV_REGEX_NS}/Robot/os1_lidar",
        debug_vis=False,
        optional_data_types=[
            "azimuth","elevation","emitterId","index","materialId","normal","objectId","velocity"
        ],
        offset=RtxLidarCfg.OffsetCfg(pos=(0.92,0.31,2.37), rot=(0.9239,0,0.3827,0)),#(0.9397,0,0.3420,0) # (0.62,0.31,2.57)
        spawn=LidarCfg(lidar_type=LidarCfg.LidarType.OUSTER_OS0_REV6_128_10HZ_1024RES),
    )
# Transparent plane
transparent_plane = AssetBaseCfg(
    prim_path="/World/ground",
    init_state=AssetBaseCfg.InitialStateCfg(pos=[0, 0, -0.7049000000000001]),  # Asset spawned at (0,0,0)
    spawn=GroundPlaneWithVisualMaterial(
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # 0.8, # should be 0.8
            dynamic_friction=0.8,  # 0.8,
            restitution=0.8,
        ),
        visual_material=sim_utils.GlassMdlCfg(glass_ior=1.0003),
    ),
)

mole_default_plane = AssetBaseCfg(
    prim_path="/World/ground",
    init_state=AssetBaseCfg.InitialStateCfg(pos=[0, 0, -0.4849000000000001]),  # Asset spawned at (0,0,0)
    spawn=GroundPlaneWithVisualMaterial(
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # 0.8, # should be 0.8
            dynamic_friction=0.8,  # 0.8,
            restitution=0.8,
        ),
        visual_material=sim_utils.GlassMdlCfg(glass_ior=1.0003),
    ),
)