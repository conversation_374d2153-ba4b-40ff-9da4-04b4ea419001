import argparse
import numpy as np
import os
import torch
from array import array
from shutil import copyfile

from IPython import embed

from isaaclab.app import AppLauncher

import cli_args  # isort: skip




parser = argparse.ArgumentParser(description="Train an RL agent with RSL-RL.")
# parser.add_argument("-w", "--run_name", type=str, default="", help="folder name of run")
# parser.add_argument("-e", "--experiment_name", type=str, default="random_soil", help="folder name of run")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")
# parser.add_argument("-i", "--checkpoint", default=-1, type=int, help="algo iteration")
cli_args.add_rsl_rl_args(parser)

AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()
args_cli.headless = True
args_cli.task = "Dx225-Material-Handling-Vanilla"

app_launcher = AppLauncher(args_cli)

from isaaclab_tasks.utils import get_checkpoint_path, parse_env_cfg
from isaaclab_rl.rsl_rl import RslRlOnPolicyRunnerCfg

from moleworks_ext import ISAACLAB_GRAVIS_LOG_DIR

# load paths
agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(args_cli.task, args_cli)
log_root_path = os.path.join(ISAACLAB_GRAVIS_LOG_DIR, agent_cfg.experiment_name)
resume_path = get_checkpoint_path(log_root_path, agent_cfg.load_run, agent_cfg.load_checkpoint)

# obs_load_path = os.path.join(load_log_root, "obs.yaml")
# actions_load_path = os.path.join(load_log_root, "actions.yaml")

# save paths
policy_name = os.path.basename(os.path.dirname(resume_path)) + "_" + os.path.splitext(os.path.basename(resume_path))[0]

export_dir = os.path.join(os.path.dirname(resume_path), "cpp_export")
if not os.path.exists(export_dir):
    os.makedirs(export_dir)

policy_save_path = os.path.join(export_dir, policy_name + ".bin")
obs_save_path = os.path.join(export_dir, policy_name + "_obs.yaml")
actions_save_path = os.path.join(export_dir, policy_name + "_actions.yaml")

print("Loading model from: ", resume_path)
# print("Loading obs.yaml from: ", obs_load_path)
# print("Loading actions.yaml from: ", actions_save_path)
snapshot = torch.load(resume_path)

# store as binary to load with eignets
weights = snapshot["model_state_dict"]
paramsConcat = np.array([])
for key, value in weights.items():
    if key.startswith("actor"):
        w = value.cpu().numpy().transpose()
        paramsConcat = np.concatenate((paramsConcat, w.flatten(order="C")))

dims = array("L", [paramsConcat.shape[0], 1])
w_array = array("f", paramsConcat)

with open(policy_save_path, "wb") as out:
    dims.tofile(out)
    w_array.tofile(out)

# store obs and actions yaml
# copyfile(obs_load_path, obs_save_path)
# copyfile(actions_load_path, actions_save_path)

print("saved policy weights to: ", policy_save_path)
print("saved obs yaml to: ", obs_save_path)
print("saved actions yaml to: ", actions_save_path)

# MLP:
# h1 = np.tanh(w0.dot(ob) + b0)
# h2 = np.tanh(w1.dot(h1) + b1)
# out = w2.dot(h2) + b2
# out *= 0.116667
