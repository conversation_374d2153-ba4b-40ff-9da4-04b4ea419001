import numpy as np
import torch

from isaaclab.envs import ManagerBasedEnv
from isaaclab.managers import ObservationManager, ObservationTermCfg
from isaaclab.utils import configclass


@configclass
class ObservationWithMeanTermCfg(ObservationTermCfg):
    """The divisor after subtraction to apply to the observation after clipping. Defaults to None,
    in which case no scaling is applied (same as setting scale to :obj:`1`)."""

    divisor: float | None = None

    """The mean subtracted before muliplying or dividing the observation after clipping. Defaults to None,
    in which case no scaling is applied (same as setting scale to :obj:`1`)."""
    mean: float | None = None


class ObservationManagerWithMean(ObservationManager):
    """Observation manager for the excavation task.
    Unfortunately, IsaacLab Master does not support mean for the observation.
    Also, it does not support different scaling for different dims of the same observation (missing conversion to torch.tensor)
    """

    def __init__(self, cfg: object, env: ManagerBasedEnv):
        super().__init__(cfg, env)
        # Dim and cumulative dim        self.dims = [0]
        self.dims = [0]
        for group_name, group_term_dims in self.group_obs_term_dim.items():
            self.dims.extend([dims[0] for dims in group_term_dims])
        self.dims_cumsum = np.cumsum(self.dims)
        self.policy_ob_dict = dict.fromkeys(self.active_terms["policy"])
        count = 0
        for key, value in self.policy_ob_dict.items():
            self.policy_ob_dict[key] = torch.zeros(
                (env.num_envs, self.group_obs_term_dim["policy"][count][0]), device=self.device
            )
            count += 1

    def compute_group(self, group_name: str) -> torch.Tensor | dict[str, torch.Tensor]:
        """Computes the observations for a given group.

        The observations for a given group are computed by calling the registered functions for each
        term in the group. The functions are called in the order of the terms in the group. The functions
        are expected to return a tensor with shape (num_envs, ...).

        If a corruption/noise model is registered for a term, the function is called to corrupt
        the observation. The corruption function is expected to return a tensor with the same
        shape as the observation. The observations are clipped and scaled as per the configuration
        settings.

        The operations are performed in the order: compute, add corruption/noise, clip, scale.
        By default, no scaling or clipping is applied.

        Args:
            group_name: The name of the group for which to compute the observations. Defaults to None,
                in which case observations for all the groups are computed and returned.

        Returns:
            Depending on the group's configuration, the tensors for individual observation terms are
            concatenated along the last dimension into a single tensor. Otherwise, they are returned as
            a dictionary with keys corresponding to the term's name.

        Raises:
            ValueError: If input ``group_name`` is not a valid group handled by the manager.
        """

        # check ig group name is valid
        if group_name not in self._group_obs_term_names:
            raise ValueError(
                f"Unable to find the group '{group_name}' in the observation manager."
                f" Available groups are: {list(self._group_obs_term_names.keys())}"
            )
        # iterate over all the terms in each group
        group_term_names = self._group_obs_term_names[group_name]
        # buffer to store obs per group
        group_obs = dict.fromkeys(group_term_names, None)
        # read attributes for each term
        obs_terms = zip(group_term_names, self._group_obs_term_cfgs[group_name])
        # evaluate terms: compute, add noise, clip, scale.
        for name, term_cfg in obs_terms:
            # compute term's value
            obs: torch.Tensor = term_cfg.func(self._env, **term_cfg.params).clone()
            # apply post-processing
            if term_cfg.noise:
                obs = term_cfg.noise.func(obs, term_cfg.noise)
            if term_cfg.clip:
                obs = obs.clip_(min=term_cfg.clip[0], max=term_cfg.clip[1])
            # added for excavation!!
            if term_cfg.mean is not None:
                obs = obs.sub_(torch.tensor(term_cfg.mean, device=obs.device))
            if term_cfg.divisor:
                obs = obs.div_(torch.tensor(term_cfg.divisor, device=obs.device))
            if term_cfg.scale is not None:
                obs = obs.mul_(torch.tensor(term_cfg.scale, device=obs.device))
            # TODO: Introduce delay and filtering models.
            # Ref: https://robosuite.ai/docs/modules/sensors.html#observables
            # add value to list
            group_obs[name] = obs
        # concatenate all observations in the group together
        if self._group_obs_concatenate[group_name]:
            return torch.cat(list(group_obs.values()), dim=-1)
        else:
            return group_obs
