# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Ignore optional memory usage warning globally
# pyright: reportOptionalSubscript=false

from __future__ import annotations

import torch
from collections.abc import Sequence
from typing import TYPE_CHECKING

import omni.physics.tensors.impl.api as physx
from pxr import PhysxSchema

import isaaclab.sim as sim_utils
import isaaclab.utils.string as string_utils
from isaaclab.markers import VisualizationMarkers
from isaaclab.utils.math import convert_quat

import isaaclab.sim as sim_utils
#from isaaclab.sensors.contact_sensor.contact_sensor import ContactSensor
from moleworks_ext.common.sensors.ContactSensorPrecise.ContactSensorPreciseData import ContactSensorPreciseData
from isaaclab.sensors.contact_sensor import ContactSensor
from isaaclab.sensors.sensor_base import SensorBase
from isaaclab.sensors.contact_sensor.contact_sensor_data import ContactSensorData
if TYPE_CHECKING:
    from moleworks_ext.common.sensors.ContactSensorPrecise.ContactSensorPreciseCfg import ContactSensorPreciseCfg
class ContactSensorPrecise(ContactSensor):
    """A contact reporting sensor.

    The contact sensor reports the normal contact forces on a rigid body in the world frame.
    It relies on the `PhysX ContactReporter`_ API to be activated on the rigid bodies.

    To enable the contact reporter on a rigid body, please make sure to enable the
    :attr:`isaaclab.sim.spawner.RigidObjectSpawnerCfg.activate_contact_sensors` on your
    asset spawner configuration. This will enable the contact reporter on all the rigid bodies
    in the asset.

    The sensor can be configured to report the contact forces on a set of bodies with a given
    filter pattern using the :attr:`ContactSensorCfg.filter_prim_paths_expr`. This is useful
    when you want to report the contact forces between the sensor bodies and a specific set of
    bodies in the scene. The data can be accessed using the :attr:`ContactSensorData.force_matrix_w`.
    Please check the documentation on `RigidContactView`_ for more details.

    The reporting of the filtered contact forces is only possible as one-to-many. This means that only one
    sensor body in an environment can be filtered against multiple bodies in that environment. If you need to
    filter multiple sensor bodies against multiple bodies, you need to create separate sensors for each sensor
    body.

    As an example, suppose you want to report the contact forces for all the feet of a robot against an object
    exclusively. In that case, setting the :attr:`ContactSensorCfg.prim_path` and
    :attr:`ContactSensorCfg.filter_prim_paths_expr` with ``{ENV_REGEX_NS}/Robot/.*_FOOT`` and ``{ENV_REGEX_NS}/Object``
    respectively will not work. Instead, you need to create a separate sensor for each foot and filter
    it against the object.

    .. _PhysX ContactReporter: https://docs.omniverse.nvidia.com/kit/docs/omni_usd_schema_physics/104.2/class_physx_schema_physx_contact_report_a_p_i.html
    .. _RigidContactView: https://docs.omniverse.nvidia.com/py/isaacsim/source/extensions/omni.isaac.core/docs/index.html#isaacsim.core.prims.RigidContactView
    """

    cfg: ContactSensorPreciseCfg
    """The configuration parameters."""

    def __init__(self, cfg: ContactSensorPreciseCfg):
        """Initializes the contact sensor object.

        Args:
            cfg: The configuration parameters.
        """
        # initialize base class
        super().__init__(cfg)
        # Create empty variables for storing output data
        self._data: ContactSensorPreciseData = ContactSensorPreciseData()
        self.max_contact_data_count = cfg.max_contact_data_count
        self.max_contact_points_per_env = 200





    """
    Operations
    """

    def reset(self, env_ids: Sequence[int] | None = None):
        # reset the timers and counters
        super().reset(env_ids)
        # reset class specific buffers
        self._data.contact_normal_forces[env_ids] = 0.0    
        self._data.contact_points[env_ids] = 0.0            
        self._data.contact_normal_directions[env_ids] = 0.0 



    """
    Implementation.
    """

    def _initialize_impl(self):
        super()._initialize_impl()
        # create simulation view
        leaf_pattern = self.cfg.prim_path.rsplit("/", 1)[-1]
        template_prim_path = self._parent_prims[0].GetPath().pathString
        body_names = list()
        for prim in sim_utils.find_matching_prims(template_prim_path + "/" + leaf_pattern):
            # check if prim has contact reporter API
            if prim.HasAPI(PhysxSchema.PhysxContactReportAPI):
                prim_path = prim.GetPath().pathString
                body_names.append(prim_path.rsplit("/", 1)[-1])
        # check that there is at least one body with contact reporter API
        if not body_names:
            raise RuntimeError(
                f"Sensor at path '{self.cfg.prim_path}' could not find any bodies with contact reporter API."
                "\nHINT: Make sure to enable 'activate_contact_sensors' in the corresponding asset spawn configuration."
            )

        # construct regex expression for the body names
        body_names_regex = r"(" + "|".join(body_names) + r")"
        body_names_regex = f"{self.cfg.prim_path.rsplit('/', 1)[0]}/{body_names_regex}"
        # convert regex expressions to glob expressions for PhysX
        body_names_glob = body_names_regex.replace(".*", "*")
        filter_prim_paths_glob = [expr.replace(".*", "*") for expr in self.cfg.filter_prim_paths_expr]

        # create a rigid prim view for the sensor
        # ovewrite _contact_physx_view with non zero max_contact_data_count
        self._contact_physx_view = self._physics_sim_view.create_rigid_contact_view(
            body_names_glob, filter_patterns=filter_prim_paths_glob, max_contact_data_count=self.max_contact_data_count
            
        )
        # Initialize additional buffers, this is the maximum size
        # We have maximum data of self.max_contact_data_count but (TODO: double check) not necessarily self.max_contact_data_count/
        self._data.contact_normal_forces        = torch.zeros(self._num_envs, self._num_bodies, self.max_contact_points_per_env, 1, device=self._device)
        self._data.contact_points               = torch.zeros(self._num_envs, self._num_bodies, self.max_contact_points_per_env, 3, device=self._device)
        self._data.contact_normal_directions    = torch.zeros(self._num_envs, self._num_bodies, self.max_contact_points_per_env, 3, device=self._device)



    def _update_buffers_impl(self, env_ids: Sequence[int]):
        """Fills the buffers of the sensor data."""
        super()._update_buffers_impl(env_ids)
        # Class specific sensor data
        #self._data.net_contact_normal_forces    
        #self._data.net_contact_points           
        #self._data.net_contact_normal_directions
        #print('')
        # Update buffers, assumption: M = 1
        # based on https://docs.omniverse.nvidia.com/py/isaacsim/source/extensions/omni.isaac.core/docs/index.html?highlight=rigidcontactview#isaacsim.core.prims.RigidPrimView.get_contact_force_data
        
        contact_data = self.contact_physx_view.get_contact_data(dt=self._sim_physics_dt)
        contact_normal_forces = contact_data[0] 
        contact_points = contact_data[1]  # Shape: (max_contact_data_count, 3)
        contact_normal_directions = contact_data[2]
        start_indices = contact_data[5][:, 0]  # Shape: (num_envs,)
        contact_counts = contact_data[4][:, 0]  # Shape: (num_envs,)
        
        indices = torch.arange(contact_counts.shape[0], device=self._device).unsqueeze(0) - start_indices.unsqueeze(1)        
        valid_indices = (indices >= 0) & (indices < contact_counts.unsqueeze(1))
        
        # Fill in the net_contact_points buffer
        self._data.contact_normal_forces[:] = 0
        self._data.contact_points[:] = 0
        self._data.contact_normal_directions[:] = 0
        ''' Non vectorized
        if contact_points.sum() != 0:
            for i in range(self._num_envs):
                # Fill the environment buffers
                self._data.contact_normal_forces[i, 0, :contact_counts[i], :]       = contact_normal_forces[start_indices[i]:start_indices[i]+contact_counts[i]]
                self._data.contact_points[i, 0, :contact_counts[i], :]              = contact_points[start_indices[i]:start_indices[i]+contact_counts[i]]
                self._data.contact_normal_directions[i, 0, :contact_counts[i], :]   = contact_normal_directions[start_indices[i]:start_indices[i]+contact_counts[i]]
                # Fill remaining data with 0s
                self._data.contact_normal_forces[i, 0, contact_counts[i]:, :]       = 0
                self._data.contact_points[i, 0, contact_counts[i]:, :]              = 0
                self._data.contact_normal_directions[i, 0, contact_counts[i]:, :]   = 0'''
                
        # Create a mask to fill up to `max_contact_points_per_env` for each environment
        env_range = torch.arange(self.max_contact_points_per_env, device=self._device)
        indices = start_indices.unsqueeze(1) + env_range
        valid_indices = (env_range.unsqueeze(0) < contact_counts.unsqueeze(1))

        # Masked assignment
        self._data.contact_normal_forces[:, 0, :self.max_contact_points_per_env, :] = torch.where(
            valid_indices.unsqueeze(-1), 
            contact_normal_forces[indices], 
            torch.tensor(0.0, device=self._device)
        )
        self._data.contact_points[:, 0, :self.max_contact_points_per_env, :] = torch.where(
            valid_indices.unsqueeze(-1), 
            contact_points[indices], 
            torch.tensor(0.0, device=self._device)
        )
        self._data.contact_normal_directions[:, 0, :self.max_contact_points_per_env, :] = torch.where(
            valid_indices.unsqueeze(-1), 
            contact_normal_directions[indices], 
            torch.tensor(0.0, device=self._device)
        )








            # Mask to zero out entries that exceed the contact count
            # Attempt to pararlellize this loop, TODO: do it at some point
            #mask = valid_indices.unsqueeze(-1).expand(-1, -1, 3)  # Expand to match the (num_envs, max_contact_data_count, 3) shape
            #self._data.contact_normal_forces[i, 0, :contact_counts[i], :] = contact_normal_forces[valid_indices[i]]
            ## Fill the buffers using masked indexing
            #self._data.contact_normal_forces[:, 0, :, :] = torch.where(
            #    valid_indices, 
            #    contact_normal_forces[start_indices.unsqueeze(1) + indices], 
            #    0
            #)
            ##self._data.contact_points[:, 0, :contact_points.shape[0], :] = torch.where(
            ##    mask, contact_points[start_indices.unsqueeze(1) + indices], 0
            ##)
            #self._data.contact_normal_directions[:, 0, :contact_points.shape[0], :] = torch.where(
            #    mask, contact_normal_directions[start_indices.unsqueeze(1) + indices], 0
            #)