# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


#from isaaclab.sensors.contact_sensor.contact_sensor_cfg import ContactSensorCfg
from moleworks_ext.common.sensors.ContactSensorPrecise.ContactSensorPrecise import ContactSensorPrecise

# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from isaaclab.utils import configclass

from isaaclab.sensors.contact_sensor.contact_sensor_cfg import ContactSensorCfg

@configclass
class ContactSensorPreciseCfg(ContactSensorCfg):
    # Type
    class_type: type = ContactSensorPrecise

    max_contact_data_count: int = 7