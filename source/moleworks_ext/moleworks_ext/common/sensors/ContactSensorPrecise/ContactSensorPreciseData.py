# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# needed to import for allowing type-hinting: torch.Tensor | None
from __future__ import annotations

import torch
from dataclasses import dataclass

from isaaclab.sensors.contact_sensor.contact_sensor_data import ContactSensorData

@dataclass
class ContactSensorPreciseData(ContactSensorData):
    #(max_data_count,1)
    contact_normal_forces: torch.Tensor | None = None 
    #(max_data_count,3)
    contact_points: torch.Tensor | None = None
    #(max_data_count,3)
    contact_normal_directions: torch.Tensor | None = None
    # separation, starting indices and num contact per points could be added
    # Those are just additional dimensions in the 