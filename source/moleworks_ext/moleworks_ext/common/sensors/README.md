# Updates on ROS2 Lidar Integration

There have been some updates concerning the ROS2 lidar integration:

1. The **standard raycaster** cannot be queried from the Omnigraph because it uses Warp instead of RTX Lidar or PhysX Lidar. 
2. To address this, I explored [this pull request](https://github.com/isaac-sim/IsaacLab/pull/1372), which introduces RTX Lidar support into Isaac Lab. It works well but requires a small workaround.

## Issue with Lidar Configuration Path

When the lidar is spawned, it looks for its configuration file in:
```bash
/workspace/IsaacLab/source/exts/isaacsim.sensors.rtx/data/lidar_configs/
```

However, the correct location is:
```bash
/workspace/IsaacLab/_isaac_sim/exts/isaacsim.sensors.rtx/data/lidar_configs/
```

## Workaround: Creating a Symlink

To resolve this issue, you can create a symbolic link pointing to the correct location:

1. Navigate to the directory where you want to create the link:
```bash
   mkdir -p /workspace/isaaclab/source/exts/
   ```
2. Create a symlink to the correct path:
```bash
ln -s /workspace/isaaclab/_isaac_sim/exts/isaacsim.sensors.rtx /workspace/isaaclab/source/exts/isaacsim.sensors.rtx
```


This will redirect the lidar configuration lookup to the correct directory and ensure everything works as expected.