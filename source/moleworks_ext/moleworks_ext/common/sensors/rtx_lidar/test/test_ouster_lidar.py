# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""This script demonstrates how to spawn multiple objects in multiple environments.
.. code-block:: bash
    # Usage
    ./isaaclab.sh -p source/standalone/demos/multi_object.py --num_envs 512
"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Demo on spawning different objects in multiple environments.")
parser.add_argument("--num_envs", type=int, default=10, help="Number of environments to spawn.")
parser.add_argument("--num_objects_per_env", type=int, default=10, help="Number of objects to spawn.")
parser.add_argument("--randomize", default=True, action="store_true", help="Randomize the objects scale.")

POSITION = (0.0, 0.0, 1)
QUATERNION = (0.0, 0.3461835, 0.0, 0.9381668)
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.enable_cameras = True
# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import traceback

import carb

import isaaclab.sim as sim_utils
from isaaclab.assets import AssetBaseCfg, RigidObjectCfg
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import SimulationContext
from isaaclab.utils import configclass


from moleworks_ext.common.sensors.rtx_lidar import RtxLidar, RtxLidarCfg
from moleworks_ext.common.sensors.rtx_lidar.spawner_sensor_cfg.rtx_lidar_sensor_cfg import LidarCfg


@configclass
class MultiObjectSceneCfg(InteractiveSceneCfg):
    """Configuration for a multi-object scene."""

    # ground plane
    ground = AssetBaseCfg(prim_path="/World/ground", spawn=sim_utils.GroundPlaneCfg())

    # lights
    dome_light = AssetBaseCfg(
        prim_path="/World/Light",
        spawn=sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75)),
    )

    lidar_cfg = RtxLidarCfg(
        prim_path="/World/Lidar",
        debug_vis=True,
        optional_data_types=[
            "azimuth",
            "elevation",
            "emitterId",
            "index",
            "materialId",
            "normal",
            "objectId",
            "velocity",
        ],
        offset=RtxLidarCfg.OffsetCfg(pos=POSITION, rot=QUATERNION),
        spawn=LidarCfg(lidar_type=LidarCfg.LidarType.OUSTER_OS1_REV6_128_10HZ_1024RES),
    )

    cube: RigidObjectCfg = RigidObjectCfg(
        prim_path="/World/cube",
        spawn=sim_utils.CuboidCfg(
            size=(2,2,2),
            rigid_props=sim_utils.RigidBodyPropertiesCfg(max_depenetration_velocity=1.0),
            mass_props=sim_utils.MassPropertiesCfg(mass=1.0),
            physics_material=sim_utils.RigidBodyMaterialCfg(),
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 0.5)),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
            ),
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(0.0, 4.0, 1)),
    )


def run_simulator(sim: SimulationContext, scene: InteractiveScene):
    """Runs the simulation loop."""
    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    count = 0
    print("RTX sensors: ", sim.has_rtx_sensors())
    # Simulation loop
    while simulation_app.is_running():
        # Write data to sim
        scene.write_data_to_sim()
        # Perform step
        sim.step()
        # Increment counter
        count += 1
        # Update buffers
        scene.update(sim_dt)


def main():
    """Main function."""
    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device="cuda")
    # sim_cfg.use_fabric = False
    sim = SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view([3.0, 0.0, 3.0], [0.0, 0.0, 0.0])
    # Design scene
    scene_cfg = MultiObjectSceneCfg(num_envs=args_cli.num_envs, env_spacing=10, replicate_physics=False)
    scene = InteractiveScene(scene_cfg)

    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    # Run the simulator
    run_simulator(sim, scene)


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
