#!/usr/bin/env python3
import json
import math
import os

def filter_and_rescale_lidar_config(
    json_in: str,
    json_out: str,
    new_up_deg: float = None,
    new_down_deg: float = None,
    max_beams: int = None,
    new_frequency_hz: float = None
):
    """
    1. Reads a lidar config file (json_in).
    2. If new_up_deg/new_down_deg are specified, filter beams whose elevation is 
       NOT in [new_down_deg, new_up_deg].
    3. If max_beams is specified, keep up to that many beams in the original order.
    4. If new_frequency_hz is specified, read the original frequency from 'scanRateBaseHz'
       (or 'reportRateBaseHz'), compute the ratio, and rescale all firing times.
    5. Updates the JSON accordingly and writes out the result to json_out.
    """

    with open(json_in, 'r') as f:
        data = json.load(f)

    profile = data.get("profile", {})
    
    # -- Step 1: read original frequency (scanRateBaseHz)
    #    Fallback: if not found, try "reportRateBaseHz", or set to None.
    old_freq = profile.get("scanRateBaseHz", None)
    if old_freq is None:
        old_freq = profile.get("reportRateBaseHz", None)
    
    # ----------------------------------------------------------------
    # 2. Filter by new_up_deg/new_down_deg if provided.
    # ----------------------------------------------------------------
    emitter_state = profile.get("emitterStates", [])[0]  # assume single emitter state
    old_azimuths = emitter_state["azimuthDeg"]
    old_elevations = emitter_state["elevationDeg"]
    old_fire_times = emitter_state["fireTimeNs"]

    n_orig = len(old_azimuths)
    assert len(old_elevations) == n_orig, "Mismatch in azimuth/elevation array lengths!"
    assert len(old_fire_times) == n_orig, "Mismatch in array lengths with firing times!"

    # If we do NOT specify new_up_deg/new_down_deg, keep all beams
    valid_indices = list(range(n_orig))
    
    if new_up_deg is not None and new_down_deg is not None:
        # Filter to keep only beams with elevation in [new_down_deg, new_up_deg]
        valid_indices = []
        for i, el in enumerate(old_elevations):
            if new_down_deg <= el <= new_up_deg:
                valid_indices.append(i)
    
    # ----------------------------------------------------------------
    # 3. If max_beams is specified, truncate the beams in original order.
    # ----------------------------------------------------------------
    if max_beams is not None:
        valid_indices = valid_indices[:max_beams]

    # Build new arrays
    new_azimuths = [old_azimuths[i] for i in valid_indices]
    new_elevations = [old_elevations[i] for i in valid_indices]
    new_fire_times = [old_fire_times[i] for i in valid_indices]
    n_new = len(new_azimuths)

    # ----------------------------------------------------------------
    # 4. Rescale firing times if new_frequency_hz is specified
    # ----------------------------------------------------------------
    if (new_frequency_hz is not None) and (old_freq is not None) and (old_freq > 0):
        scaling_factor = old_freq / new_frequency_hz
        # Multiply each firing time by scaling_factor
        # We assume firing times are in integer nanoseconds => cast back to int
        for i in range(n_new):
            new_fire_times[i] = int(round(new_fire_times[i] * scaling_factor))

        # Also update the 'scanRateBaseHz' in the profile to the new frequency
        profile["scanRateBaseHz"] = new_frequency_hz
    
    # ----------------------------------------------------------------
    # 5. Update upElevationDeg/downElevationDeg if changed
    # ----------------------------------------------------------------
    if new_up_deg is not None:
        profile["upElevationDeg"] = new_up_deg
    if new_down_deg is not None:
        profile["downElevationDeg"] = new_down_deg

    # ----------------------------------------------------------------
    # 6. Overwrite the arrays in the JSON
    # ----------------------------------------------------------------
    emitter_state["azimuthDeg"] = new_azimuths
    emitter_state["elevationDeg"] = new_elevations
    emitter_state["fireTimeNs"] = new_fire_times

    # Adjust numberOfEmitters
    profile["numberOfEmitters"] = n_new

    # ----------------------------------------------------------------
    # Write out the JSON
    # ----------------------------------------------------------------
    with open(json_out, 'w') as f:
        json.dump(data, f, indent=4)

    print(f"Done! Original had {n_orig} beams, new config has {n_new} beams.")
    if new_frequency_hz is not None and old_freq is not None:
        print(f"Rescaled firing times by factor = {scaling_factor:.3f} "
              f"({old_freq} Hz -> {new_frequency_hz} Hz).")
    else:
        print("No frequency rescaling performed.")
    print(f"Output file: {json_out}")


if __name__ == "__main__":
    # Example usage:
    #   - Filter out beams above 0 deg or below -45 deg
    #   - Keep up to 32 beams
    #   - Rescale firing times from original freq to 1 Hz
    base_path = os.path.dirname(os.path.abspath(__file__))
    filter_and_rescale_lidar_config(
        json_in=os.path.join(base_path, "OS0_REV6_128ch10hz1024res.json"),
        json_out=os.path.join(base_path, "OS0_REV6_64ch1hz1024res_45degfov.json"),
        new_up_deg=0.0,
        new_down_deg=-45.0,
        max_beams=64,
        new_frequency_hz=1.0  # e.g. if original was 10 Hz, we do factor = 10 / 1 = 10
    )
