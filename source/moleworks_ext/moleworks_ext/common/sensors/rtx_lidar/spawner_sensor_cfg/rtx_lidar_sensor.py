# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import json
import os
from typing import TYPE_CHECKING

import isaacsim.core.utils.prims as prim_utils
import omni.kit.commands
import omni.log
from pxr import Gf, Sdf, Usd

from isaaclab.sim.utils import clone
from isaaclab.utils import to_camel_case

if TYPE_CHECKING:
    from . import LidarCfg


CUSTOM_PINHOLE_CAMERA_ATTRIBUTES = {
    "projection_type": ("cameraProjectionType", Sdf.ValueTypeNames.Token),
}
"""Custom attributes for pinhole camera model.

The dictionary maps the attribute name in the configuration to the attribute name in the USD prim.
"""



@clone
def spawn_lidar(
    prim_path: str,
    cfg: LidarCfg,
    translation: tuple[float, float, float] | None = None,
    orientation: tuple[float, float, float, float] = (1.0, 0.0, 0.0, 0.0),
) -> Usd.Prim:
    """Create a USD Camera prim used for RTX Lidar models.

    This function creates an RTX lidar model attached to a USD Camera prim. The RTX lidar model is configured from json
    files located within ``omni.isaac.core``.

    Custom configurations for the RTX lidar are passed in via the :class:`LidarCfg`. By setting the `lidar_type` to
    custom and providing a `sensor_profile` dictionary a json configuration file will be created and passed to the
    kit commands responsible for the RTX lidar creation.

    Args:
        prim_path: The prim path or pattern to spawn the asset at. If the prim path is a regex pattern,
            then the asset is spawned at all the matching prim paths.
        cfg: The configuration instance.
        translation: The translation to apply to the prim w.r.t. its parent prim. Defaults to None, in which case
            this is set to the origin.
        orientation: The orientation in (w, x, y, z) to apply to the prim w.r.t. its parent prim. Defaults to None,
            in which case this is set to identity.

    Returns:
        The created prim.

    Raises:
        ValueError: If the LidarCfg.sensor_profile is None when LidarCfg.lidar_type == "Custom"
        RuntimeError: If the creation of the RTXLidar fails
        ValueError: If a prim already exists at the given path.
    """

    if cfg.lidar_type == "Custom":
        if cfg.sensor_profile is None:
            raise ValueError("LidarCfg sensor_profile cannot be none for lidar_type: Custom")

        # make directories
        if not os.path.isdir(cfg.sensor_profile_temp_dir):
            os.makedirs(cfg.sensor_profile_temp_dir)
        print(cfg.sensor_profile_temp_dir)
        # create file path
        file_name = cfg.sensor_profile_temp_prefix + ".json"
        file_path = os.path.join(cfg.sensor_profile_temp_dir, file_name)

        # Check for tempfiles and remove
        while os.path.isfile(file_path):
            os.remove(file_path)

        # Write to file
        with open(file_path, "w") as outfile:
            json.dump(cfg.sensor_profile, outfile)

        print("Custom")
        config = file_path.split("/")[-1].split(".")[0]
        print(file_path)
    else:
        config = cfg.lidar_type

    if not prim_utils.is_prim_path_valid(prim_path):
        # prim_utils.create_prim(prim_path, "Camera", translation=translation, orientation=orientation)
        _, sensor = omni.kit.commands.execute(
            "IsaacSensorCreateRtxLidar",
            path=prim_path,
            parent=None,
            config=config,
            translation=translation,
            orientation=Gf.Quatd(orientation[0], orientation[1], orientation[2], orientation[3]),
        )
        if not sensor.IsValid():
            raise RuntimeError("IsaacSensorCreateRtxLidar failed to create a USD.Prim")

    else:
        raise ValueError(f"A prim already exists at path: '{prim_path}'.")

    return sensor