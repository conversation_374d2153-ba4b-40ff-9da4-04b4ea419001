# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import os
from collections.abc import Callable
from typing import Any, Literal

from moleworks_ext import MOLEWORKS_COMMON_DIR
from isaaclab.sim.spawners.spawner_cfg import SpawnerCfg
from isaaclab.utils import configclass

from isaaclab.sim.spawners import sensors
from moleworks_ext.common.sensors.rtx_lidar.spawner_sensor_cfg.rtx_lidar_sensor import (
    spawn_lidar,
)


@configclass
class LidarCfg(SpawnerCfg):
    """
    Lidar Configuration Table
    +-------------+-----------+-----------------------------------+---------------------------+
    | Manufacturer| Model     | UI Name                           | Config Name               |
    +=============+===========+===================================+===========================+
    | HESAI       |PandarXT-32| PandarXT-32 10hz                  | Hesai_XT32_SD10           |
    +-------------+-----------+-----------------------------------+---------------------------+
    | Ouster      | OS0       | OS0 128 10hz @ 1024 resolution    | OS0_128ch10hz1024res      |
    |             |           | OS0 128 10hz @ 2048 resolution    | OS0_128ch10hz2048res      |
    |             |           | OS0 128 10hz @ 512 resolution     | OS0_128ch10hz512res       |
    |             |           | OS0 128 20hz @ 1024 resolution    | OS0_128ch20hz1024res      |
    |             |           | OS0 128 20hz @ 512 resolution     | OS0_128ch20hz512res       |
    +-------------+-----------+-----------------------------------+---------------------------+
    | Ouster      | OS1       | OS1 32 10hz @ 1024 resolution     | OS1_32ch10hz1024res       |
    |             |           | OS1 32 10hz @ 2048 resolution     | OS1_32ch10hz2048res       |
    |             |           | OS1 32 10hz @ 512 resolution      | OS1_32ch10hz512res        |
    |             |           | OS1 32 20hz @ 1024 resolution     | OS1_32ch20hz1024res       |
    |             |           | OS1 32 20hz @ 512 resolution      | OS1_32ch20hz512res        |
    +-------------+-----------+-----------------------------------+---------------------------+
    | SICK        | TiM781    | SICK TiM781                       | Sick_TiM781               |
    +-------------+-----------+-----------------------------------+---------------------------+
    | SLAMTEC     |RPLidar S2E| RPLidar S2E                       | RPLIDAR_S2E               |
    +-------------+-----------+-----------------------------------+---------------------------+
    | Velodyne    | VLS-128   | Velodyne VLS-128                  | Velodyne_VLS128           |
    +-------------+-----------+-----------------------------------+---------------------------+
    | ZVISION     | ML-30s+   | ML-30s+                           | ZVISION_ML30S             |
    |             | ML-Xs     | ML-Xs                             | ZVISION_MLXS              |
    +-------------+-----------+-----------------------------------+---------------------------+
    | NVIDIA      | Generic   | Rotating                          | Example_Rotary            |
    |             | Generic   | Solid State                       | Example_Solid_State       |
    |             | Debug     | Simple Solid State                | Simple_Example_Solid_State|
    +-------------+-----------+-----------------------------------+---------------------------+
    """

    func = spawn_lidar
    """The RTX lidar spawn function."""

    lidar_type: str = "Example_Rotary"
    """The name of the lidar sensor profile. Defaults to Example_Rotatry.

    There are many built in configuration files specified by LidarType below.

    If a user want to create a custom configuration file set lidar_type="Custom" and create a sensor_profile dictionary."""

    class LidarType:
        """Class variables for autocompletion"""

        HESAI_PandarXT_32 = "Hesai_XT32_SD10"
        OUSTER_OS0_REV6_128_10HZ_1024RES = "OS0_REV6_128ch10hz1024res"
        OUSTER_OS0_REV6_64_1HZ_1024RES_45DEGFOV = "OS0_REV6_64ch1hz1024res_45degfov"
        OUSTER_OS0_REV6_128_10HZ_2048RES = "OS0_REV6_128ch10hz2048res"
        OUSTER_OS0_REV6_128_10HZ_512RES = "OS0_REV6_128ch10hz512res"
        # Here just the OS1 REV 6 are added, check in /workspace/isaaclab/source/exts/isaacsim.sensors.rtx/data/lidar_configs/Ouster
        # to add more configs
        OUSTER_OS1_REV6_32_10HZ_1024RES = "OS1_REV6_32ch10hz1024res"
        OUSTER_OS1_REV6_32_10HZ1_2048RES = "OS1_REV6_32ch10hz2048res"
        OUSTER_OS1_REV6_32_10HZ_512RES = "OS1_REV6_32ch10hz512res"
        OUSTER_OS1_REV6_32_20HZ_1024RES = "OS1_REV6_32ch20hz1024res"
        OUSTER_OS1_REV6_32_20HZ_512RES = "OS1_REV6_32ch20hz512res"
        # OS1 REV 6 128
        OUSTER_OS1_REV6_128_10HZ_1024RES = "OS1_REV6_128ch10hz1024res"
        OUSTER_OS1_REV6_128_10HZ_2048RES = "OS1_REV6_128ch10hz2048res"
        OUSTER_OS1_REV6_128_10HZ_512RES = "OS1_REV6_128ch10hz512res"
        OUSTER_OS1_REV6_128_20HZ_1024RES = "OS1_REV6_128ch20hz1024res"
        OUSTER_OS1_REV6_128_20HZ_512RES = "OS1_REV6_128ch20hz512res"

        SICK_TIM781 = "Sick_TiM781"
        SLAMTEC_RPLIDAR_S2E = "RPLIDAR_S2E"
        VELODYNE_VLS128 = "Velodyne_VLS128"
        ZVISION_ML30S = "ZVISION_ML30S"
        ZVISION_MLXS = "ZVISION_MLXS"
        EXAMPLE_ROTARY = "Example_Rotary"
        EXAMPLE_SOLID_STATE = "Example_Solid_State"
        SIMPLE_EXAMPLE_SOLID_STATE = "Simple_Example_Solid_State"

    sensor_profile: dict[str, Any] | None = None
    """Custom lidar parameters to use if lidar_type="Custom"

     see https://docs.omniverse.nvidia.com/kit/docs/omni.sensors.nv.lidar/latest/lidar_extension.html"""

    sensor_profile_temp_dir: str = os.path.abspath(
        os.path.join("MOLEWORKS_COMMON_DIR", "/sensors/rtx_lidar")
    )
    """The location of the generated custom sensor profile json file."""

    sensor_profile_temp_prefix: str = "Temp_Config_"
    """The custom sensor profile json file prefix. This is used for cleanup of the custom sensor profile."""
