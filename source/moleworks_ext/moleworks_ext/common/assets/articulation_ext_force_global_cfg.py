from isaaclab.assets.articulation.articulation_cfg import ArticulationCfg
from isaaclab.utils import configclass

from moleworks_ext.common.assets.articulation_ext_force_global import Articulation_ext_force_global


@configclass
class Articulation_ext_force_globalCfg(ArticulationCfg):
    """Configuration parameters for an articulation for which forces are applied in the global frame"""

    class_type: type = Articulation_ext_force_global
