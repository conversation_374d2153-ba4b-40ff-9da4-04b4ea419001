# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from collections.abc import Callable

from isaaclab.sim.spawners import materials
from isaaclab.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg
from isaaclab.utils import configclass

from moleworks_ext.common.sim.spawners.from_files import from_files


@configclass
class GroundPlaneWithVisualMaterial(GroundPlaneCfg):
    """Create a ground plane prim with visual material

    This uses the USD for the standard grid-world ground plane from <PERSON> Sim by default.
    """

    func: Callable = from_files.spawn_ground_plane_with_visual_material

    visual_material_path: str = "material"
    """Path to the visual material to use for the prim. Defaults to "material".

    If the path is relative, then it will be relative to the prim's path.
    This parameter is ignored if `visual_material` is not None.
    """

    visual_material: materials.VisualMaterialCfg | None = None
    """Visual material properties to override the visual material properties in the URDF file.

    Note:
        If None, then no visual material will be added.
    """
