from dataclasses import MISSING

from isaaclab.utils import configclass
from isaaclab_rl.rsl_rl import RslRlPpoAlgorithmCfg


@configclass
class RslRlSymmetryCfg:
    """Configuration for the PPO symmery augmentation"""

    use_augmentation: bool = MISSING
    use_loss: bool = MISSING
    class_name: str = MISSING  # symmetry function class name
    symmetry_coeff: float = MISSING
    env_cfg: dict = MISSING  # env config passed to the symmetry function class


@configclass
class RslRlPpoWithSymmetryCfg(RslRlPpoAlgorithmCfg):
    """Configuration for the PPO with symmetry augmentation"""

    symmetry_cfg: RslRlSymmetryCfg = None
