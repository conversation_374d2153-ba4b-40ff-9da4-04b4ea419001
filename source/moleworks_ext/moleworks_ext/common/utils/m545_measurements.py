"""
    This class manages measurement buffers of the excavation agent, updated each step/inter-decim step.
"""

import torch
import numpy as np
import isaacsim.core.utils.torch as torch_utils


class M545Measurements:
    def __init__(self, cfg, num_joints, num_envs, device, env):
        self.cfg = cfg
        self.env = env
        self.num_envs = num_envs
        self.num_dofs = num_joints
        # Make arm joints optional: set to empty if not defined.
        if hasattr(self.cfg, 'arm_joints_names') and self.cfg.arm_joints_names:
            self.arm_joints_names = self.cfg.arm_joints_names
            self.num_arm_dofs = len(self.arm_joints_names)
        else:
            self.arm_joints_names = []
            self.num_arm_dofs = 0
        self.device = device
        self.init_measurements_buffers()

    def initialize_asset(self):
        self.asset = self.env.scene.articulations['robot']

        # Arm joints (optional)
        if self.arm_joints_names:
            self.arm_joint_names = self.arm_joints_names
            self.arm_joint_ids = self.asset.find_joints(self.arm_joint_names)[0]
            self.jac_arm_joint_ids = [joint_id + 6 for joint_id in self.arm_joint_ids]
        else:
            self.arm_joint_names = []
            self.arm_joint_ids = []
            self.jac_arm_joint_ids = []

        # Bucket body (optional)
        if hasattr(self.cfg, 'ee_body_name') and self.cfg.ee_body_name:
            self.bucket_body_idx = self.asset.find_bodies(self.cfg.ee_body_name)[0]
        else:
            self.bucket_body_idx = None

        # Masses and inertias (always computed)
        self.masses = self.asset.root_physx_view.get_masses()[0, :].to(self.device)
        self.masses_expanded = self.masses.view(self.asset.num_bodies, 1)
        inertias = self.asset.root_physx_view.get_inertias().to(self.device)
        self.inertias = torch.reshape(inertias[0], (self.asset.num_bodies, 3, 3))
        self.num_bodies = len(self.asset.body_names)

        # Jacobian placeholders
        self.jacobian = torch.zeros(
            (self.num_envs, self.num_bodies, 6, self.num_dofs + 6),
            device=self.device
        )
        self.jac_lin = self.jacobian[:, :, 0:3, :]
        self.jac_lin_T = self.jac_lin.transpose(-2, -1)
        self.jac_rot = self.jacobian[:, :, 3:6, :]
        self.jac_rot_T = self.jac_rot.transpose(-2, -1)
        if self.bucket_body_idx is not None:
            self.bucket_jac_lin_T_dof = self.jacobian[:, self.bucket_body_idx[0], 0:3, -self.num_dofs:].transpose(-2, -1)
            self.bucket_jac_rot_T_dof = self.jacobian[:, self.bucket_body_idx[0], 3:6, -self.num_dofs:].transpose(-2, -1)
        else:
            self.bucket_jac_lin_T_dof = torch.zeros((self.num_envs, self.num_dofs, 3), device=self.device)
            self.bucket_jac_rot_T_dof = torch.zeros((self.num_envs, self.num_dofs, 3), device=self.device)

        # Initial references to simulator data (cloned so no in-place conflict)
        self.joint_pos = self.asset.data.joint_pos.clone()
        self.joint_vel = self.asset.data.joint_vel.clone()

        # assumes everything is merged into one body after the pitch joint
        self.j_pitch_pos = self.asset.data.body_pos_w[:, -1, :].clone()
        self.j_pitch_quat = self.asset.data.body_quat_w[:, -1, :].clone()
        self.j_pitch_vel = self.asset.data.body_lin_vel_w[:, -1, :].clone()
        self.j_pitch_ang_vel = self.asset.data.body_ang_vel_w[:, -1, :].clone()

        self.root_lin_vel_w = self.asset.data.root_lin_vel_w.clone()
        self.root_ang_vel_w = self.asset.data.root_ang_vel_w.clone()
        self.root_quat_w = self.asset.data.root_quat_w.clone()

        # "map" is the raw world frame data from the simulator
        self.root_pos_map = self.asset.data.root_pos_w.clone()
        # "w" is the same position shifted by env origins => local env frame
        self.root_pos_w = self.root_pos_map - self.env.scene.env_origins

        # Bucket measurements (optional: update only if "frame_transformer" sensor exists)
        if "frame_transformer" in self.env.scene.sensors:
            self.bucket_pos_map = torch.zeros_like(self.asset.data.body_pos_w[:, -1, :])
            self.bucket_pos_w = torch.zeros_like(self.asset.data.body_pos_w[:, -1, :])
            self.bucket_vel_w = torch.zeros_like(self.asset.data.body_lin_vel_w[:, -1, :])
        else:
            self.bucket_pos_map = torch.zeros((self.num_envs, 3), device=self.device)
            self.bucket_pos_w = torch.zeros((self.num_envs, 3), device=self.device)
            self.bucket_vel_w = torch.zeros((self.num_envs, 3), device=self.device)

        # Gravity terms
        self.gravity = torch.tensor([0, 0, -9.81], device=self.device)
        self.gravity_expanded = self.gravity.view(1, 3)
        self.gravity_forces = self.masses_expanded * self.gravity_expanded

        # A handy unit vector along Z
        self.unit_vec_z = torch.tensor([0.0, 0.0, 1.0], device=self.device)
        self.unit_vec_z_expanded = self.unit_vec_z.expand(self.num_envs, -1)

    def init_measurements_buffers(self):
        # Joint state buffers
        self.joint_pos = torch.zeros((self.num_envs, self.num_dofs), device=self.device)
        self.joint_vel = torch.zeros((self.num_envs, self.num_dofs), device=self.device)
        self.joint_vel_num = torch.zeros((self.num_envs, self.num_dofs), device=self.device)

        self.arm_joint_pos = torch.zeros((self.num_envs, self.num_arm_dofs), device=self.device)
        self.arm_joint_vel = torch.zeros((self.num_envs, self.num_arm_dofs), device=self.device)
        self.arm_joint_tau = torch.zeros((self.num_envs, self.num_arm_dofs), device=self.device)

        # Body pitch
        self.j_pitch_pos = torch.zeros((self.num_envs, 3), device=self.device)
        self.j_pitch_quat = torch.zeros((self.num_envs, 4), device=self.device)
        self.j_pitch_vel = torch.zeros((self.num_envs, 3), device=self.device)
        self.j_pitch_ang_vel = torch.zeros((self.num_envs, 3), device=self.device)

        # Root states
        self.root_lin_vel_w = torch.zeros((self.num_envs, 3), device=self.device)
        self.root_ang_vel_w = torch.zeros((self.num_envs, 3), device=self.device)
        self.root_quat_w = torch.zeros((self.num_envs, 4), device=self.device)
        self.root_pos_w = torch.zeros((self.num_envs, 3), device=self.device)
        self.root_pos_map = torch.zeros((self.num_envs, 3), device=self.device)

        # Buffers for mass matrix and gravity
        self.mm = torch.zeros(self.num_envs, self.num_arm_dofs, self.num_arm_dofs, device=self.device)
        self.gravity_tau = torch.zeros(self.num_envs, self.num_arm_dofs, device=self.device)
        self.w_r_pe = torch.zeros([self.num_envs, 3], device=self.device)

        # Bucket
        self.prev_bucket_pos_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_pos_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_vel_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.quat_bucket_w = torch.zeros([self.num_envs, 4], device=self.device)
        self.bucket_ang_vel = torch.zeros([self.num_envs, 3], device=self.device)
        self.bp_unit_vector_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_com_pos_w = torch.zeros([self.num_envs, 3], device=self.device)

        # Base-frame vectors
        self.bucket_pos_base = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_vel_base = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_ang_vel_base = torch.zeros([self.num_envs, 3], device=self.device)

        # Misc
        self.base_pitch_w = torch.zeros([self.num_envs], device=self.device)
        self.bucket_aoa = torch.zeros(self.num_envs, device=self.device)
        self.bucket_ang_w = torch.zeros([self.num_envs], device=self.device)
        self.bucket_ang_gac = torch.zeros([self.num_envs], device=self.device)
        self.bucket_vel_norm = torch.zeros([self.num_envs], device=self.device)
        self.base_z_w = torch.zeros([self.num_envs, 3], device=self.device)

        self.coriolis_centrifugal_force = torch.zeros((self.num_envs, self.num_arm_dofs), device=self.device)
        self.prev_joint_pos = torch.zeros((self.num_envs, self.num_dofs), device=self.device)

    def update_measurements(self):
        # Save previous joint positions for numerical velocity
        self.prev_joint_pos[:] = self.joint_pos

        # Update new joint states
        self.joint_pos = self.asset.data.joint_pos.clone()
        self.joint_vel = self.asset.data.joint_vel.clone()

        if self.arm_joint_ids:
            self.arm_joint_pos = self.joint_pos[:, self.arm_joint_ids]
            self.arm_joint_vel = self.joint_vel[:, self.arm_joint_ids]
            self.arm_joint_tau = self.asset.data.applied_torque[:, self.arm_joint_ids].clone()

        # Body pitch: update only if bucket body is defined.
        if self.bucket_body_idx is not None:
            pitch_map = self.asset.data.body_pos_w[:, self.bucket_body_idx, :].squeeze(1).clone()
            self.j_pitch_pos[:] = pitch_map - self.env.scene.env_origins
            self.j_pitch_quat[:] = self.asset.data.body_quat_w[:, self.bucket_body_idx, :].squeeze(1).clone()
            self.j_pitch_vel[:] = self.asset.data.body_lin_vel_w[:, self.bucket_body_idx, :].squeeze(1).clone()
            self.j_pitch_ang_vel[:] = self.asset.data.body_ang_vel_w[:, self.bucket_body_idx, :].squeeze(1).clone()

        # Root pose/vel
        self.root_lin_vel_w[:] = self.asset.data.root_lin_vel_w.clone()
        self.root_ang_vel_w[:] = self.asset.data.root_ang_vel_w.clone()
        self.root_quat_w[:] = self.asset.data.root_quat_w.clone()
        root_map = self.asset.data.root_pos_w.clone()
        self.root_pos_map[:] = root_map
        self.root_pos_w[:] = root_map - self.env.scene.env_origins

        # Jacobians
        self.jacobian[:] = self.asset.root_physx_view.get_jacobians()

        # Update bucket positions and velocities if "frame_transformer" sensor exists.
        if "frame_transformer" in self.env.scene.sensors:
            self.prev_bucket_pos_w[:] = self.bucket_pos_w
            self.bucket_pos_map[:] = self.env.scene.sensors["frame_transformer"].data.target_pos_w.squeeze(1)
            self.bucket_pos_w[:] = self.bucket_pos_map - self.env.scene.env_origins
            self.bucket_vel_w[:] = (self.bucket_pos_w - self.prev_bucket_pos_w) / self.env.cfg.sim.dt
            self.quat_bucket_w[:] = self.env.scene.sensors["frame_transformer"].data.target_quat_w.squeeze(1)
            self.bp_unit_vector_w[:] = torch_utils.quat_rotate(self.quat_bucket_w, self.unit_vec_z_expanded)
            self.bucket_ang_vel = self.j_pitch_ang_vel

        # com for the force computation (only if bucket body is defined)
        if self.bucket_body_idx is not None:
            com_bucket = self.asset.root_physx_view.get_coms()[:, self.bucket_body_idx, :3].squeeze(1).to(self.device)
            shovel_com_local_pos_w = torch_utils.quat_rotate(self.j_pitch_quat, com_bucket)
            self.bucket_com_pos_w[:] = shovel_com_local_pos_w + self.j_pitch_pos

        # Calculate gravity and mass matrix only if arm joints are defined.
        if self.arm_joint_ids:
            self.calculate_gravity_and_mass_matrix()

        # Coriolis/centrifugal: not important (skipped)
        # Numerical joint velocities
        self.joint_vel_num[:] = (self.joint_pos - self.prev_joint_pos) / self.cfg.sim.dt

        # Convert world to base frame
        self.bucket_pos_base[:] = torch_utils.quat_rotate_inverse(
            self.root_quat_w,
            (self.bucket_pos_w - self.root_pos_w)
        )
        self.bucket_vel_base[:] = torch_utils.quat_rotate_inverse(
            self.root_quat_w,
            (self.bucket_vel_w - self.root_lin_vel_w)
        )
        self.bucket_ang_vel_base[:] = torch_utils.quat_rotate_inverse(
            self.root_quat_w,
            (self.bucket_ang_vel - self.root_ang_vel_w)
        )

    def calculate_gravity_and_mass_matrix(self):
        self.gravity_tau[:] = torch.sum(
            torch.matmul(
                self.jac_lin_T[:, :, self.jac_arm_joint_ids, :],
                self.gravity_forces.unsqueeze(-1),
            ),
            dim=1,
        ).squeeze()

        self.mm[:] = torch.sum(
            self.jac_lin_T[:, :, self.jac_arm_joint_ids, :].matmul(
                self.masses.view(-1, 1, 1) * self.jac_lin[:, :, :, self.jac_arm_joint_ids]
            ) + self.jac_rot_T[:, :, self.jac_arm_joint_ids, :].matmul(
                self.inertias.matmul(self.jac_rot[:, :, :, self.jac_arm_joint_ids])
            ),
            dim=1,
        )

    def update_derived_measurements(self, env_ids, dim0):
        # Skip update if the frame_transformer sensor is not defined.
        if "frame_transformer" not in self.env.scene.sensors:
            return

        self.base_z_w = torch_utils.quat_rotate(
            self.root_quat_w[env_ids],
            self.unit_vec_z.expand(dim0, -1),
        )
        self.base_pitch_w[env_ids] = torch.atan2(self.base_z_w[:, 0], self.base_z_w[:, 2])
        vel_ang_w = torch.atan2(self.bucket_vel_w[env_ids, 2], self.bucket_vel_w[env_ids, 0])
        bp_ang_w = torch.atan2(-self.bp_unit_vector_w[env_ids, 2], -self.bp_unit_vector_w[env_ids, 0])
        aoa = bp_ang_w - vel_ang_w
        self.bucket_aoa[env_ids] = torch.where(
            torch.abs(aoa) > np.pi,
            aoa - torch.copysign(2.0 * np.pi * torch.ones(dim0, device=self.device), aoa),
            aoa,
        )
        self.bucket_ang_w[env_ids] = torch.atan2(self.bp_unit_vector_w[env_ids, 2], self.bp_unit_vector_w[env_ids, 0])
        self.bucket_ang_gac[env_ids] = self.bucket_ang_w[env_ids] - self.base_pitch_w[env_ids]
        self.bucket_vel_norm[env_ids] = torch.linalg.norm(self.bucket_vel_w[env_ids], dim=-1)
