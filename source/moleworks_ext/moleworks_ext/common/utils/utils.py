from __future__ import annotations

import matplotlib.pyplot as plt
import numpy as np
import torch
from copy import deepcopy
from matplotlib.backends.backend_pdf import PdfPages
from typing import TYPE_CHECKING
import isaaclab.utils.math as math_utils
from ruamel.yaml import YAM<PERSON>
from typing import Dict
import xml.etree.ElementTree as ET
if TYPE_CHECKING:
    from isaaclab.managers.observation_manager import ObservationManager


# Define the function again to fix URDF joints
def fix_urdf_joints(input_urdf_path, output_urdf_path, movable_joints):
    """
    Fixes joints in a URDF file if they are not in the movable joints list.

    Args:
        input_urdf_path (str): Path to the input URDF file.
        output_urdf_path (str): Path to save the modified URDF file.
        movable_joints (list): List of joint names that should remain movable.
    """
    # Parse the URDF file
    tree = ET.parse(input_urdf_path)
    root = tree.getroot()

    # Iterate through all joints in the URDF
    for joint in root.findall('joint'):
        joint_name = joint.get('name')
        if joint_name not in movable_joints:
            # Change the joint type to fixed if not in movable_joints
            joint.set('type', 'fixed')

    # Write the modified URDF to the output file
    tree.write(output_urdf_path, encoding='utf-8', xml_declaration=True)



def cylindrical_to_cartesian(r, theta, z):
    """
    Convert cylindrical coordinates to Cartesian coordinates.
    
    Args:
        r (float or torch.Tensor): Radial distance.
        theta (float or torch.Tensor): Angle in radians.
        z (float or torch.Tensor): Height.
        
    Returns:
        torch.Tensor: Cartesian coordinates as [x, y, z].
    """
    # Ensure inputs are torch tensors
    if not isinstance(r, torch.Tensor):
        r = torch.tensor(r)
    if not isinstance(theta, torch.Tensor):
        theta = torch.tensor(theta)
    if not isinstance(z, torch.Tensor):
        z = torch.tensor(z)
    
    x = r * torch.cos(theta)
    y = r * torch.sin(theta)
    return torch.stack((x, y, z), dim=-1)


def transform_to_world_frame(local_pos, base_pos, base_quat):
    """
    Transform a position from the base frame to the world frame.

    Args:
        local_pos (torch.Tensor): Position in the base frame.
        base_pos (torch.Tensor): Base position in the world frame.
        base_quat (torch.Tensor): Base orientation in the world frame.

    Returns:
        torch.Tensor: Position in the world frame.
    """
    # Rotate local_pos by base_quat
    rotated_pos = math_utils.quat_rotate(base_quat, local_pos)
    # Translate by base_pos
    world_pos = rotated_pos + base_pos
    return world_pos


def u_rand(size, min, max, device):
    return min + (max - min) * torch.rand(size, device=device)


def cart2cyl(cart: torch.tensor) -> torch.tensor:
    cyl = torch.zeros_like(cart)
    cyl[..., 0] = torch.sqrt(cart[..., 0] ** 2 + cart[..., 1] ** 2)
    cyl[..., 1] = torch.arctan2(cart[..., 1], cart[..., 0])
    cyl[..., 2] = cart[..., 2]
    return cyl


def cyl2cart(cyl: torch.tensor) -> torch.tensor:
    cart = torch.zeros_like(cyl)
    cart[..., 0] = cyl[..., 0] * torch.cos(cyl[..., 1])
    cart[..., 1] = cyl[..., 0] * torch.sin(cyl[..., 1])
    cart[..., 2] = cyl[..., 2]
    return cart


def get_log_dict(dict, N, dim=None):
    ret = deepcopy(dict)
    for key, value in ret.items():
        d = value.shape[-1] if dim == None else dim
        ret[key] = np.zeros((N, d)) * np.nan
    return ret


def multipage(filename, figs=None, dpi=200):
    pp = PdfPages(filename)
    if figs is None:
        figs = [plt.figure(n) for n in plt.get_fignums()]
    for fig in figs:
        fig.savefig(pp, format="pdf", dpi=dpi, bbox_inches="tight")
    pp.close()


def save_obs_yaml(obs_manager: ObservationManager, save_path):
    pass
    #yaml = YAML()
    #yaml.default_flow_style = False
    #policy_ob_dict = dict.fromkeys(obs_manager.active_terms["policy"])
#
    #out = {}
    #out["names"] = list(policy_ob_dict.keys())
    #out["scales"] = {}
#
    #for i, name in enumerate(out["names"]):
    #    scale = obs_manager._group_obs_term_cfgs["policy"][i].scale
    #    dim = obs_manager.group_obs_term_dim["policy"][i][0]
    #    out["scales"][name] = dim * [scale]
#
    #with open(save_path, "w") as f:
    #    yaml.dump(out, f)


def create_joint_index_mapping(action_manager) -> Dict[str, int]:
    """
    Creates a mapping from joint names to action indices based on the action manager's active terms.
    
    Args:
        action_manager: The action manager containing active terms and their configurations
        
    Returns:
        Dict[str, int]: Mapping from joint names to their corresponding indices in the action tensor
    """
    joint_to_index = {}
    current_index = 0
    
    for term_name in action_manager.active_terms:
        term_cfg = getattr(action_manager.cfg, term_name)
        if hasattr(term_cfg, "joint_names"):
            for joint_name in term_cfg.joint_names:
                joint_to_index[joint_name] = current_index
                current_index += 1
                
    return joint_to_index