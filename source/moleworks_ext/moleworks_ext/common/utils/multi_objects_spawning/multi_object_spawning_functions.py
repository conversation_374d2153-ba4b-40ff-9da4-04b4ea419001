import isaaclab.sim as sim_utils
from moleworks_ext.common.sim.spawners.multi_asset.asset_randomizer_cfg import AssetRandomizerCfg
from moleworks_ext.common.sim.spawners.multi_asset.randomizations import RandomizeScaleCfg, RandomizeScaleCfgw_returned_scale


def get_assets():
    """
    Retrieve a list of basic geometric shapes (sphere, cuboid, cylinder) with specific configurations.
    """

    # Common properties for the assets, including rigid body, mass, and collision settings
    kwargs = {
        "rigid_props": sim_utils.RigidBodyPropertiesCfg(),
        "mass_props": sim_utils.MassPropertiesCfg(mass=0.05),
        "collision_props": sim_utils.CollisionPropertiesCfg(),
    }

    # Return a list of shape configurations: Sphere, Cuboid, and Cylinder
    return [
        sim_utils.SphereCfg(
            radius=0.25,  # Radius of the sphere
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0)),  # Blue color
            **kwargs,
        ),
        sim_utils.CuboidCfg(
            size=(0.25, 0.25, 0.25),  # Dimensions of the cuboid
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 1.0, 0.0)),  # Green color
            **kwargs,
        ),
        sim_utils.CylinderCfg(
            radius=0.2,  # Radius of the cylinder
            height=0.3,  # Height of the cylinder
            axis="Y",  # Cylinder's axis orientation
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1.0, 0.0, 0.0)),  # Red color
            **kwargs,
        ),
    ]


def get_randomized_assets(num_random_assets):
    """
    retrieve a list of geometric shapes with randomized scale configurations.
    """
    # Get the base geometric shape assets without randomization
    assets = get_assets()

    # Apply randomization configurations (scaling) to each shape asset
    return [
        AssetRandomizerCfg(
            child_spawner_cfg=assets[0],  # Base configuration for the sphere
            randomization_cfg=RandomizeScaleCfg(
                x_range=(0.5, 1.25),  # Scale range for randomization
                equal_scale=True,  # Apply the same scale to all dimensions
            ),
            num_random_assets=num_random_assets,  # Number of random variations to create
        ),
        AssetRandomizerCfg(
            child_spawner_cfg=assets[1],  # Base configuration for the cuboid
            randomization_cfg=RandomizeScaleCfg(
                x_range=(0.5, 1.25),  # Scale range for randomization
                equal_scale=True,  # Apply the same scale to all dimensions
            ),
            num_random_assets=num_random_assets,  # Number of random variations to create
        ),
        AssetRandomizerCfg(
            child_spawner_cfg=assets[2],  # Base configuration for the cylinder
            randomization_cfg=RandomizeScaleCfg(
                x_range=(0.5, 1.25),  # Scale range for randomization
                equal_scale=True,  # Apply the same scale to all dimensions
            ),
            num_random_assets=num_random_assets,  # Number of random variations to create
        ),
    ]
