from moleworks_ext.common.utils.utils import fix_urdf_joints

# Define the list of movable joints to exclude from fixing
movable_joints = ['J_BOOM', 'J_DIPPER']

# Input and output URDF paths
input_urdf_path = '/mnt/data/m545_narrow.urdf'
output_urdf_path = '/mnt/data/verified_fixed_m545_narrow.urdf'

# Fix joints in the URDF file
fix_urdf_joints(input_urdf_path, output_urdf_path, movable_joints)

# Verify the results by reloading and checking the modified URDF
# Load the modified URDF
tree = ET.parse(output_urdf_path)
root = tree.getroot()

# Collect results for verification
verification_results = []
for joint in root.findall('joint'):
    joint_name = joint.get('name')
    joint_type = joint.get('type')
    verification_results.append((joint_name, joint_type))

import pandas as pd

# Convert to a DataFrame for easier readability and display
verification_df = pd.DataFrame(verification_results, columns=["Joint Name", "Joint Type"])
import ace_tools as tools; tools.display_dataframe_to_user(name="Verified URDF Joints", dataframe=verification_df)
