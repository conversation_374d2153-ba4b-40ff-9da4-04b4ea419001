import pickle as pkl
import os
import matplotlib.pyplot as plt
import torch
import numpy as np
import pathlib


""" 
Computes true joint velocity and torque limits from sampled torque factors of HEAP

data [dict]
boom [dict]
    joint_pos
    torque_factor


joint vel = cylinder vel / f(q)
joint torque = cylinder force / f(q)
"""
from moleworks_ext import MOLEWORKS_RSC_DIR


LAB_M545_DIR = f"{MOLEWORKS_RSC_DIR}/sim/model/m545"


class Limits:
    def __init__(self, excavation_env):
        self.device = excavation_env.device
        self.num_envs = excavation_env.num_envs
        self.cfg = excavation_env.cfg
        torque_factor_pickle_file = os.path.join(LAB_M545_DIR, "limits/torque_factors.pkl")
        with open(torque_factor_pickle_file, "rb") as f:
            self.torque_factors_dict = pkl.load(f)

        self.search_dim = self.torque_factors_dict["boom"]["joint_pos"].shape[0]
        self.num_joints = len(self.torque_factors_dict.keys())

        self.joint_positions = torch.zeros(self.num_joints, self.search_dim, device=self.device)
        self.torque_factors = self.joint_positions.clone()

        for i, name in enumerate(self.torque_factors_dict.keys()):
            self.joint_positions[i, :] = torch.tensor(self.torque_factors_dict[name]["joint_pos"], device=self.device)
            self.torque_factors[i, :] = torch.tensor(
                self.torque_factors_dict[name]["torque_factor"], device=self.device
            )

        self.delta = torch.diff(self.joint_positions)[:, 0]

        self.max_upper_idx = (self.search_dim - 1) * torch.ones(1, device=self.device, dtype=torch.int64)
        self.min_lower_idx = torch.zeros(1, device=self.device, dtype=torch.int64)

        self.pos_limits_lower = torch.tensor(self.cfg.limits.position.lower, device=self.device)
        self.pos_limits_upper = torch.tensor(self.cfg.limits.position.upper, device=self.device)

        self.cylinder_force_limit_lower = torch.tensor(self.cfg.limits.cylinder_force.lower, device=self.device)
        self.cylinder_force_limit_upper = torch.tensor(self.cfg.limits.cylinder_force.upper, device=self.device)

        self.cylinder_velocity_limit_lower = torch.tensor(self.cfg.limits.cylinder_velocity.lower, device=self.device)
        self.cylinder_velocity_limit_upper = torch.tensor(self.cfg.limits.cylinder_velocity.upper, device=self.device)

        # TF for boom is ALWAYS negative
        # limits have wrong sign -> flip lower and upper, otherwise clip(...) does not work
        cylinder_force_boom_lower = self.cylinder_force_limit_lower[0].item()  # reference otherwise!
        self.cylinder_force_limit_lower[0] = self.cylinder_force_limit_upper[0]
        self.cylinder_force_limit_upper[0] = cylinder_force_boom_lower

        cylinder_velocity_boom_lower = self.cylinder_velocity_limit_lower[0].item()  # reference otherwise!
        self.cylinder_velocity_limit_lower[0] = self.cylinder_velocity_limit_upper[0]
        self.cylinder_velocity_limit_upper[0] = cylinder_velocity_boom_lower

        self.curr_torque_limit_lower = torch.zeros(self.num_envs, self.num_joints, device=self.device)
        self.curr_torque_limit_upper = self.curr_torque_limit_lower.clone()
        self.curr_vel_limit_lower = self.curr_torque_limit_lower.clone()
        self.curr_vel_limit_upper = self.curr_torque_limit_lower.clone()

    def update(self, joint_pos):
        tf = self._compute_torque_factors(joint_pos)

        # TODO: make @ property
        self.curr_vel_limit_lower[:] = self.cylinder_velocity_limit_lower / tf
        self.curr_vel_limit_upper[:] = self.cylinder_velocity_limit_upper / tf

        self.curr_torque_limit_lower[:] = self.cylinder_force_limit_lower * tf
        self.curr_torque_limit_upper[:] = self.cylinder_force_limit_upper * tf

    def _compute_torque_factors(self, joint_pos):
        joint_pos = torch.clip(joint_pos, self.pos_limits_lower, self.pos_limits_upper)
        upper, lower = self._find_upper_and_lower_idxs(joint_pos)

        nom = joint_pos - torch.gather(self.joint_positions, 1, lower).t()
        weights = nom / self.delta
        res = torch.lerp(
            torch.gather(self.torque_factors, 1, lower).t(), torch.gather(self.torque_factors, 1, upper).t(), weights
        )

        return res

    def _find_upper_and_lower_idxs(self, pos):
        """finds the upper and lower idxs of val in self.x
                - val can have arbitrary dimensionality
                - if val exactly at knot, upper = lower
                - if at upper end: upper = lower = self.x_dim-1
                - if at lower end: upper = lower = 0

                - searchsorted gives one past array index if val larger than largest in array
                - gives 0 if smaller than first value
        Args:
            pos: (n_envs,n_joints) joint pos for which we need to find torque factor

        Returns:
            tuple (upper, lower) each (n_joints, n_envs)

        """
        pos_contiguous = pos.t().contiguous()
        # search along innermost (last) dimension, first dimension is joints
        upper = self._clip_upper_indices(torch.searchsorted(self.joint_positions, pos_contiguous))

        lower = self._clip_lower_indices(
            torch.where(torch.gather(self.joint_positions, 1, upper) == pos_contiguous, upper, upper - 1)
        )

        return upper, lower

    def _clip_upper_indices(self, upper):
        upper_val = torch.minimum(upper, self.max_upper_idx)
        return upper_val

    def _clip_lower_indices(self, lower):

        lower_val = torch.maximum(lower, self.min_lower_idx)
        return lower_val

    def _plot(self):
        # querry (above/below limit, at limits, inbetween)
        querry_pos = torch.zeros(5, self.num_joints, device=self.device)
        querry_pos[0] = self.pos_limits_lower - 0.5
        querry_pos[1] = self.pos_limits_upper + 0.5
        querry_pos[2] = self.pos_limits_lower
        querry_pos[3] = self.pos_limits_upper
        querry_pos[4] = (self.pos_limits_lower + self.pos_limits_upper) / 2.0

        computed_tf = self._compute_torque_factors(querry_pos)
        for i, name in enumerate(self.torque_factors_dict.keys()):
            # torque factors
            plt.figure()
            plt.plot(
                self.joint_positions[i, :].cpu().numpy(), self.torque_factors[i, :].cpu().numpy(), label=name + "_tf"
            )
            plt.scatter(
                querry_pos[:, i].cpu().numpy(), computed_tf[:, i].cpu().numpy(), label=name + "_computed", marker="x"
            )
            plt.legend()
            plt.grid()

            # torques
            plt.figure()
            torque_limits_lower = self.cylinder_force_limit_lower[i] * self.torque_factors[i, :]
            torque_limits_upper = self.cylinder_force_limit_upper[i] * self.torque_factors[i, :]
            avg_limits_lower = torch.mean(torque_limits_lower).item()
            avg_limits_upper = torch.mean(torque_limits_upper).item()
            print("avg limits {}: upper: {}, lower: {}".format(name, avg_limits_upper, avg_limits_lower))
            plt.plot(
                self.joint_positions[i, :].cpu().numpy(), torque_limits_lower.cpu().numpy(), label=name + "_torque_min"
            )
            plt.plot(
                self.joint_positions[i, :].cpu().numpy(), torque_limits_upper.cpu().numpy(), label=name + "_torque_max"
            )
            plt.plot(
                self.joint_positions[i, :].cpu().numpy(),
                avg_limits_lower * np.ones((self.search_dim)),
                label=name + "_avg_min",
            )
            plt.plot(
                self.joint_positions[i, :].cpu().numpy(),
                avg_limits_upper * np.ones((self.search_dim)),
                label=name + "_avg_max",
            )
            plt.legend()
            plt.grid()

            # velocities
            plt.figure()
            velocity_limits_lower = self.cylinder_velocity_limit_lower[i] / self.torque_factors[i, :]
            velocity_limits_upper = self.cylinder_velocity_limit_upper[i] / self.torque_factors[i, :]
            plt.plot(
                self.joint_positions[i, :].cpu().numpy(),
                velocity_limits_lower.cpu().numpy(),
                label=name + "_velocity_min",
            )
            plt.plot(
                self.joint_positions[i, :].cpu().numpy(),
                velocity_limits_upper.cpu().numpy(),
                label=name + "_velocity_max",
            )
            plt.legend()
            plt.grid()

        plt.show(block=False)
        input("hit [ENTER] to exit")

