from __future__ import annotations

from dataclasses import MISSING

from isaaclab.managers import CommandTermCfg
from isaaclab.utils import configclass

from moleworks_ext.common.commands.position_3d_cyl_coords_command import Position3DCylCoordsCommand
from moleworks_ext.common.commands.waypoints_3d_cyl_coords_command import Waypoints3DCylCoordsCommand


@configclass
class Position3DCylCoordsCommandCfg(CommandTermCfg):
    class_type: type = Position3DCylCoordsCommand

    rho_range: tuple[float, float] = MISSING  # rho, min, max
    z_range: tuple[float, float] = MISSING  # z, min, max
    phi_range: tuple[float, float] = MISSING  # max change in phi
    p_direction_change: float = MISSING  # probability of changing direction


@configclass
class Waypoints3DCylCoordsCommandCfg(CommandTermCfg):
    class_type: type = Waypoints3DCylCoordsCommand

    rho_range: tuple[float, float] = MISSING  # rho, min, max
    z_range: tuple[float, float] = MISSING  # z, min, max
    dphi_range: tuple[float, float] = MISSING  # max change in phi
    p_direction_change: float = MISSING  # probability of changing slew direction between waypoints
    num_waypoints: int = MISSING  # range ofnumber of waypoints
    waypoint_start_idx_range: tuple(int, int) = MISSING  # range ofnumber of waypoints
    tube_radius: float = MISSING  # radius of the tube between waypoints
    waypoint_bubble_radius: float = MISSING  # radius of the bubble around waypoints to determine if reached

    # command
    num_observed_waypoints: int = MISSING  # number of waypoints in obs
