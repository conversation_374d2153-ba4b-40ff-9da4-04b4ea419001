from __future__ import annotations

import numpy as np
import torch
from typing import TYPE_CHECKING

import isaaclab.sim as sim_utils
from isaaclab.managers import CommandTerm
from isaaclab.markers import FRAME_MARKER_CFG, VisualizationMarkers, VisualizationMarkersCfg

from moleworks_ext.common.utils.utils import cyl2cart

if TYPE_CHECKING:
    from moleworks_ext.common.commands.command_cfgs import Position3DCylCoordsCommandCfg
    from moleworks_ext.tasks.material_handling.vanilla.material_handler_env import MaterialHandlerRLEnv


class Position3DCylCoordsCommand(CommandTerm):
    cfg: Position3DCylCoordsCommandCfg
    _env: MaterialHandlerRLEnv

    def __init__(self, cfg: Position3DCylCoordsCommandCfg, env: MaterialHandlerRLEnv):
        super().__init__(cfg, env)
        self.tool_target_cyl = torch.zeros(self.num_envs, 3, device=self.device)
        self.tool_target_cart = torch.zeros(self.num_envs, 3, device=self.device)

        self.metrics["error_rho"] = torch.zeros(self.num_envs, device=self.device)
        self.metrics["error_phi"] = torch.zeros(self.num_envs, device=self.device)
        self.metrics["error_z"] = torch.zeros(self.num_envs, device=self.device)
        self.metrics["error_3d"] = torch.zeros(self.num_envs, device=self.device)
        self.zero_orientation = torch.tensor([1.0, 0.0, 0.0, 0.0], device=self.device).expand(self.num_envs, -1)

    def __str__(self) -> str:
        msg = "Position3DCylCoordsCommand:\n"
        msg += f"\tResampling time range: {self.cfg.resampling_time_range}"
        return msg

    """
    Properties
    """

    @property
    def command(self) -> torch.Tensor:
        """The command tensor. Shape is (num_envs, command_dim)."""
        return self.tool_target_cyl

    def _update_metrics(self):
        """Update the metrics based on the current state."""
        self.metrics["error_rho"] = self.tool_target_cyl[:, 0] - self._env.tool_pos_cyl[:, 0]
        self.metrics["error_phi"] = self.tool_target_cyl[:, 1] - self._env.tool_pos_cyl[:, 1]
        self.metrics["error_z"] = self.tool_target_cyl[:, 2] - self._env.tool_pos_cyl[:, 2]
        self.metrics["error_3d"] = torch.norm(self.tool_target_cart - self._env.tool_pos_cart, dim=1)

    def _resample_command(self, env_ids: Sequence[int]):
        """Resample the command for the specified environments.
        In Cylindrical Coords [rho, phi, z]"""
        r = torch.empty(len(env_ids), device=self.device)
        self.tool_target_cyl[env_ids, 0] = r.uniform_(*self.cfg.rho_range)
        self.tool_target_cyl[env_ids, 1] = (
            r.uniform_(*self.cfg.phi_range) + self._env.MH.data.joint_pos[env_ids, 0]
        )  # tool pos is not up to date (only afer a sim step, but we need it before)
        self.tool_target_cyl[env_ids, 2] = r.uniform_(*self.cfg.z_range)
        self.tool_target_cart[env_ids] = cyl2cart(self.tool_target_cyl[env_ids])

    def _update_command(self):
        """Update the command based on the current state.
        Once sampled, we keep it fixed"""
        pass

    def _set_debug_vis_impl(self, debug_vis: bool):
        """Set debug visualization into visualization objects.

        This function is responsible for creating the visualization objects if they don't exist
        and input ``debug_vis`` is True. If the visualization objects exist, the function should
        set their visibility into the stage.
        """
        if debug_vis:
            if not hasattr(self, "visualizer"):
                markers_cfg = VisualizationMarkersCfg(
                    markers={
                        "target": sim_utils.SphereCfg(
                            radius=0.2,
                            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1.0, 0.0, 0.0)),
                        ),
                    }
                )

                markers_cfg.markers["tool_frame"] = FRAME_MARKER_CFG.markers["frame"]
                markers_cfg.markers["tool_frame"].scale = (1.0, 1.0, 1.0)
                markers_cfg.prim_path = "/Visuals/myMarkers"

                self.visualizer = VisualizationMarkers(markers_cfg)

                # visualize all markers in all envs
                self.marker_indices = (
                    np.array([[idx] * self.num_envs for idx in range(self.visualizer.num_prototypes)])
                    .flatten()
                    .tolist()
                )

                self.visualizer.set_visibility(True)
            else:
                self.visualizer.set_visibility(False)

    def _debug_vis_callback(self, event):
        """Callback for debug visualization.

        This function calls the visualization objects and sets the data to visualize into them.
        """
        positions = torch.cat(
            (
                self.tool_target_cart + self._env.scene.env_origins,
                self._env.tool_pos_cart + self._env.scene.env_origins,
            ),
            dim=0,
        )
        orientations = torch.cat(
            (
                self.zero_orientation,
                self._env.scene["robot"].data.body_quat_w[:, self._env.tool_body_idx, :],
            ),
            dim=0,
        )

        self.visualizer.visualize(translations=positions, orientations=orientations, marker_indices=self.marker_indices)
