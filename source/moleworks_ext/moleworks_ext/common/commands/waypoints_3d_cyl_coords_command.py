from __future__ import annotations

import torch
from typing import TYPE_CHECKING, Sequence

import isaaclab.sim as sim_utils
from isaaclab.managers import CommandTerm
from isaaclab.markers import FRAME_MARKER_CFG, VisualizationMarkers, VisualizationMarkersCfg

from moleworks_ext.common.utils.utils import cyl2cart

if TYPE_CHECKING:
    from moleworks_ext.common.commands.command_cfgs import Waypoints3DCylCoordsCommandCfg
    from moleworks_ext.tasks.material_handling.vanilla.material_handler_env import MaterialHandlerRLEnv


class Waypoints3DCylCoordsCommand(CommandTerm):
    cfg: Waypoints3DCylCoordsCommandCfg
    _env: MaterialHandlerRLEnv

    def __init__(self, cfg: Waypoints3DCylCoordsCommandCfg, env: MaterialHandlerRLEnv):
        self.num_waypoints = cfg.num_waypoints

        # sets the debug vis, everything that is needed there, has to be init before
        super().__init__(cfg, env)

        # keep track of the current number of sampled waypoints
        self.current_waypoint_idx = torch.zeros(self.num_envs, device=self.device, dtype=torch.int)
        self.init_waypoint_idx = torch.zeros(self.num_envs, device=self.device, dtype=torch.int)
        self.previous_waypoint_idx = torch.zeros(self.num_envs, device=self.device, dtype=torch.int)

        # create buffer for max number of waypoints even though we might not use all of them
        self.all_waypoints_cyl = torch.zeros(self.num_envs, self.num_waypoints, 3, device=self.device)
        self.all_waypoints_cart = torch.zeros(self.num_envs, self.num_waypoints, 3, device=self.device)
        # previous waypoints for tube
        self.all_previous_waypoints_cyl = torch.zeros(self.num_envs, self.num_waypoints, 3, device=self.device)
        self.all_previous_waypoints_cart = torch.zeros(self.num_envs, self.num_waypoints, 3, device=self.device)
        self.current_waypoints = torch.zeros(self.num_envs, self.cfg.num_observed_waypoints, 3, device=self.device)
        self.previous_waypoints = torch.zeros(self.num_envs, self.cfg.num_observed_waypoints, 3, device=self.device)

        # for obs, we pad with last target
        # we observe the current + (num_observed_waypoints - 1) wapoints
        self.num_waypoints_for_obs = self.num_waypoints + self.cfg.num_observed_waypoints - 1
        self.waypoints_cyl_obs = torch.zeros(self.num_envs, self.num_waypoints_for_obs, 3, device=self.device)
        self.previous_waypoints_cyl_obs = torch.zeros(self.num_envs, self.num_waypoints_for_obs, 3, device=self.device)
        self.is_first = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.is_last = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_done = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)

        # self.metrics["final_error_rho"] = torch.zeros(self.num_envs, device=self.device)
        # self.metrics["final_error_phi"] = torch.zeros(self.num_envs, device=self.device)
        # self.metrics["final_error_z"] = torch.zeros(self.num_envs, device=self.device)
        # self.metrics["final_error_3d"] = torch.zeros(self.num_envs, device=self.device)
        self.metrics["frac_reached_waypoints"] = torch.zeros(self.num_envs, device=self.device)

        # zero orientation for waypoints
        self.zero_orientation = torch.tensor([1.0, 0.0, 0.0, 0.0], device=self.device).expand(
            self.num_envs * self.num_waypoints, -1
        )

        # for accessing single current waypoint
        self.all_env_ids = torch.arange(self.num_envs, device=self.device, dtype=torch.int)

    def __str__(self) -> str:
        msg = "Waypoints3DCylCoordsCommand:\n"
        msg += f"\tResampling time range: {self.cfg.resampling_time_range}"
        return msg

    """
    Properties
    """

    @property
    def command(self) -> torch.Tensor:
        """The command tensor. Shape is (num_envs, command_dim)."""

        return self.current_waypoints  # [n_envs, num_waypoints, 3]

    def _update_metrics(self):
        """Update the metrics based on the current state.
        These metrics are averaged over the number of envs that terminated when reset and logged in Tensorboard.
        """
        # metrics to be logged per step
        # self.per_step_metrics["distance_to_tube"] =

        # self.metrics["max_tube_violation"] = torch.max(self.per_step_metrics["distance_to_tube"])
        # # when terminated, how many waypoints have been reached
        self.metrics["frac_reached_waypoints"] = (
            self.current_waypoint_idx / (self.num_waypoints)
        ).float()  # _idx points one past last

        # self.metrics["error_rho"] = self.tool_target_cyl[:, 0] - self._env.tool_pos_cyl[:, 0]
        # self.metrics["error_phi"] = self.tool_target_cyl[:, 1] - self._env.tool_pos_cyl[:, 1]
        # self.metrics["error_z"] = self.tool_target_cyl[:, 2] - self._env.tool_pos_cyl[:, 2]
        # self.metrics["error_3d"] = torch.norm(self.tool_target_cart - self._env.tool_pos_cart, dim=1)

    def _resample_command(self, env_ids: Sequence[int]):
        """Resample the command for the specified environments.
        In Cylindrical Coords [rho, phi, z]"""
        r = torch.empty((len(env_ids), self.num_waypoints), device=self.device)

        # sample waypoint start index
        self.init_waypoint_idx[env_ids] = torch.randint(
            *self.cfg.waypoint_start_idx_range, (len(env_ids),), device=self.device
        ).int()
        self.current_waypoint_idx[env_ids] = self.init_waypoint_idx[env_ids]

        self.previous_waypoint_idx[env_ids] = self.current_waypoint_idx[env_ids]

        # sample rho and z within the admissible range
        self.all_waypoints_cyl[env_ids, :, 0] = r.uniform_(*self.cfg.rho_range)
        self.all_waypoints_cyl[env_ids, :, 2] = r.uniform_(*self.cfg.z_range)

        # sample phi
        # random direction & p of having direction changes between the waypoints
        # start around the sampled machine phi
        dphis = r.uniform_(*self.cfg.dphi_range)
        # rand, randint(low, high (1 above highest)): uniform distribution
        main_direction = (
            torch.randint(0, 2, (len(env_ids),), device=self.device) * 2 - 1
        ).float()  # [-1,1], 50/50 to go left or right
        direction_change = (
            torch.rand(len(env_ids), self.num_waypoints, device=self.device) < self.cfg.p_direction_change
        ).int() * -2 + 1  # flip sign with probability p_direction_change
        dphis = torch.matmul(torch.diag(main_direction), dphis)
        dphis *= direction_change

        dphis_cumsum = torch.cumsum(dphis, dim=-1)
        self.all_waypoints_cyl[env_ids, :, 1] = dphis_cumsum + self._env.MH.data.joint_pos[env_ids, 0].unsqueeze(
            -1
        )  # no need to wrap, joint angles is also not wrapped

        # center around the machine phi when not starting at waypoint 0
        offset = dphis_cumsum[torch.arange(len(env_ids), device=self.device), self.current_waypoint_idx[env_ids] - 1]
        offset *= (self.current_waypoint_idx[env_ids] > 0).float()
        self.all_waypoints_cyl[env_ids, :, 1] -= offset.unsqueeze(-1)

        # previous waypoints start at tool pos
        self.all_previous_waypoints_cyl[env_ids, 0, :] = self._env.tool_pos_cyl[
            env_ids
        ]  # WRONG TOOL POS (not updated after reset, only after an env step! DO NOT USE!)
        self.all_previous_waypoints_cyl[env_ids, 1:, :] = self.all_waypoints_cyl[env_ids, :-1, :]

        # convert to cart, for distance checking and vis
        self.all_waypoints_cart[env_ids] = cyl2cart(self.all_waypoints_cyl[env_ids])
        self.all_previous_waypoints_cart[env_ids] = cyl2cart(self.all_previous_waypoints_cyl[env_ids])

        # padding for obs
        self.waypoints_cyl_obs[env_ids, : self.num_waypoints, :] = self.all_waypoints_cyl[env_ids]
        self.waypoints_cyl_obs[env_ids, self.num_waypoints :, :] = self.all_waypoints_cyl[env_ids, -1, :].unsqueeze(1)

        self.previous_waypoints_cyl_obs[env_ids, : self.num_waypoints, :] = self.all_previous_waypoints_cyl[env_ids]
        self.previous_waypoints_cyl_obs[env_ids, self.num_waypoints :, :] = self.all_previous_waypoints_cyl[
            env_ids, -1, :
        ].unsqueeze(1)

    def _update_command(self):
        cf = 1.0 + min(max((self._env.num_ppo_update - 500) / 500, 0.0), 1.0)
        """Update the command based on the current state.
        Check if current waypoint is reached and update it"""
        self.previous_waypoint_idx[:] = self.current_waypoint_idx[:]
        # if self.current_waypoint_idx < self.num_waypoints:

        wp_idx = self.current_waypoint_idx.clamp(max=self.num_waypoints - 1)
        reached_current_waypoint = (
            torch.norm(
                self._env.tool_pos_cart - self.all_waypoints_cart[self.all_env_ids, wp_idx, :],
                dim=-1,
            )
        ) < (self.cfg.waypoint_bubble_radius / cf)
        self.current_waypoint_idx[reached_current_waypoint] += 1
        self.current_waypoint_idx[:] = self.current_waypoint_idx.clamp(
            max=self.num_waypoints
        )  # no more than max number of waypoints, points one past last

        # update waypoints for obs
        # waypoint selection mask
        tot_range = torch.arange(self.num_waypoints_for_obs, device=self.device, dtype=torch.int).unsqueeze(0)
        index_vec = self.current_waypoint_idx.unsqueeze(1).clamp(max=self.num_waypoints - 1)

        mask = tot_range >= index_vec
        mask *= tot_range < index_vec + self.cfg.num_observed_waypoints

        self.current_waypoints[:] = self.waypoints_cyl_obs[mask].view(self.num_envs, self.cfg.num_observed_waypoints, 3)
        self.previous_waypoints[:] = self.previous_waypoints_cyl_obs[mask].view(
            self.num_envs, self.cfg.num_observed_waypoints, 3
        )

        # access through command_manager.get_terms
        self.is_first[:] = self.current_waypoint_idx == self.init_waypoint_idx
        self.is_last[:] = self.current_waypoint_idx >= self.num_waypoints - 1
        self.last_done[:] = self.current_waypoint_idx == self.num_waypoints

    def _set_debug_vis_impl(self, debug_vis: bool):
        """Set debug visualization into visualization objects.

        This function is responsible for creating the visualization objects if they don't exist
        and input ``debug_vis`` is True. If the visualization objects exist, the function should
        set their visibility into the stage.
        """
        if debug_vis and self._env.sim.has_gui():
            if not hasattr(self, "visualizer"):
                markers_cfg = VisualizationMarkersCfg()
                markers_cfg.prim_path = "/Visuals/myMarkers"
                markers_cfg.markers = dict()

                # active waypoint
                markers_cfg.markers["active"] = sim_utils.SphereCfg(
                    radius=self.cfg.waypoint_bubble_radius,
                    visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0)),
                )
                self.active_marker_id = 0

                # done waypoints
                markers_cfg.markers["done"] = sim_utils.SphereCfg(
                    radius=self.cfg.waypoint_bubble_radius,
                    visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 1.0, 0.0)),
                )
                self.done_marker_id = 1

                markers_cfg.markers["tool_frame"] = FRAME_MARKER_CFG.markers["frame"]
                markers_cfg.markers["tool_frame"].scale = (1.0, 1.0, 1.0)
                self.tool_marker_id = 2

                self.visualizer = VisualizationMarkers(markers_cfg)
                # num_waypoints + tool frame
                self.marker_indices = torch.zeros(
                    self.num_envs, self.num_waypoints + 1, dtype=torch.int, device=self.device
                )

                # marker indices
                self.waypoint_marker_ids = torch.zeros(
                    self.num_envs, self.num_waypoints, dtype=torch.int, device=self.device
                )
                self.ids_in_waypoint_marker_ids = torch.arange(
                    self.num_waypoints, device=self.device, dtype=torch.int
                ).expand(self.num_envs, -1)

                self.tool_marker_ids = torch.zeros(self.num_envs, 1, dtype=torch.int, device=self.device)

                self.visualizer.set_visibility(True)

                import isaacsim.util.debug_draw._debug_draw as omni_debug_draw

                self.draw_interface = omni_debug_draw.acquire_debug_draw_interface()

            else:
                self.visualizer.set_visibility(False)

    def _debug_vis_callback(self, event):
        """Callback for debug visualization.

        This function calls the visualization objects and sets the data to visualize into them.
        """
        if self._env.sim.has_gui():
            waypoints_w = self.all_waypoints_cart + self._env.scene.env_origins.unsqueeze(1)
            positions = torch.cat(
                (
                    waypoints_w.flatten(end_dim=1),  # waypoints
                    self._env.tool_pos_cart + self._env.scene.env_origins,  # tool frame
                ),
                dim=0,
            )
            orientations = torch.cat(
                (
                    self.zero_orientation,
                    self._env.scene["robot"].data.body_quat_w[:, self._env.tool_body_idx, :],
                ),
                dim=0,
            )

            done_waypoints = self.ids_in_waypoint_marker_ids < self.current_waypoint_idx.unsqueeze(-1)
            self.waypoint_marker_ids[:] = self.active_marker_id
            self.waypoint_marker_ids[done_waypoints] = self.done_marker_id

            self.tool_marker_ids[:] = self.tool_marker_id

            self.visualizer.visualize(
                translations=positions,
                orientations=orientations,
                marker_indices=self.waypoint_marker_ids.view(-1).tolist() + self.tool_marker_ids.view(-1).tolist(),
            )

            # connecting lines
            self.draw_interface.clear_lines()
            lines_colors = [[1.0, 1.0, 1.0, 1.0]] * (self.num_waypoints - 1) * self.num_envs
            line_thicknesses = [4.0] * (self.num_waypoints - 1) * self.num_envs

            source = waypoints_w[:, :-1, :].flatten(end_dim=1)
            target = waypoints_w[:, 1:, :].flatten(end_dim=1)
            self.draw_interface.draw_lines(source.tolist(), target.tolist(), lines_colors, line_thicknesses)
