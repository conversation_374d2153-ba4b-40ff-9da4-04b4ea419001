from dataclasses import dataclass, field
from typing import List, Optional, Literal

import numpy as np

from isaaclab.envs.mdp import JointActionCfg
from isaaclab.managers.action_manager import ActionTerm
from isaaclab.envs.mdp.actions import ActionTermCfg
from isaaclab.utils import configclass
from isaaclab.controllers import DifferentialIKControllerCfg

from moleworks_ext.common.actions.joint_actions import InverseDynamicsAction
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg, InverseDynamicsController


@configclass
class InverseDynamicsActionCfg(JointActionCfg):
    """Configuration for the joint effort action term.

    See :class:`InverseDynamicsAction` for more details.
    """

    class_type: type[ActionTerm] = InverseDynamicsAction
    controller_cfg: type[InverseDynamicsControllerCfg] = InverseDynamicsControllerCfg


@configclass
class DifferentialIKWithInvDynActionCfg(ActionTermCfg):
    """Configuration for the combined Differential Inverse Kinematics and Inverse Dynamics action term."""

    @configclass
    class OffsetCfg:
        """The offset pose from parent frame to child frame.

        On many robots, end-effector frames are fictitious frames that do not have a corresponding
        rigid body. In such cases, it is easier to define this transform w.r.t. their parent rigid body.
        """
        pos: tuple[float, float, float] = (0.0, 0.0, 0.0)
        """Translation w.r.t. the parent frame. Defaults to (0.0, 0.0, 0.0)."""
        rot: tuple[float, float, float, float] = (1.0, 0.0, 0.0, 0.0)
        """Quaternion rotation ``(w, x, y, z)`` w.r.t. the parent frame. Defaults to (1.0, 0.0, 0.0, 0.0)."""

    class_type: type = None  # To be set in the environment setup
    """The associated action term class."""

    # IK Configuration
    joint_names: List[str] = field(default_factory=list)
    """List of joint names or regex expressions that the action will be mapped to."""
    body_name: str = ""
    """Name of the body or frame for which IK is performed."""
    body_offset: Optional[OffsetCfg] = None
    """Offset of target frame w.r.t. the body frame. Defaults to None, in which case no offset is applied."""
    scale: float = 1.0
    """Scale factor for the action. Defaults to 1.0."""
    controller: DifferentialIKControllerCfg = field(default_factory=DifferentialIKControllerCfg)
    """The configuration for the differential IK controller."""

    # Inverse Dynamics Configuration
    inv_dyn_cfg: InverseDynamicsActionCfg = field(default_factory=InverseDynamicsActionCfg)
    """The configuration for the inverse dynamics controller."""
    # Other joints configs 
    joint_cfgs: Optional[JointActionCfg] = None
