import torch
from typing import Sequence
import carb
from isaaclab.envs.manager_based_env import ManagerBasedEnv
import isaaclab.utils.math as math_utils
from isaaclab.controllers import DifferentialIKController
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsController
from isaaclab.assets import Articulation
from isaaclab.managers.action_manager import ActionTerm, ActionTermCfg
from moleworks_ext.common.actions.actions_cfg import DifferentialIKWithInvDynActionCfg
from isaaclab.envs.mdp.actions import JointVelocityActionCfg, JointPositionActionCfg, RelativeJointPositionActionCfg
from moleworks_ext.common.actions.joint_actions import InverseDynamicsAction
from isaaclab.envs.mdp.actions import JointVelocityAction, JointPositionAction, RelativeJointPositionAction

class DifferentialIKWithInvDynAction(ActionTerm):
    """Action term that combines Differential Inverse Kinematics and Inverse Dynamics controllers.

    It takes task-space commands (e.g., end-effector velocities), computes desired joint velocities via the IK controller,
    and then computes joint torques via the inverse dynamics controller to apply to the robot.
    """

    cfg: DifferentialIKWithInvDynActionCfg
    """Configuration for the combined IK and InvDyn action term."""

    def __init__(self, cfg: DifferentialIKWithInvDynActionCfg, env: ManagerBasedEnv):
        super().__init__(cfg, env)
        self._env = env

        # Get the articulation asset
        self._asset: Articulation = env.scene['robot']
        if self._asset is None:
            raise ValueError(f"Asset '{cfg.asset_name}' not found in the environment.")

        # Check that all joints are covered between joints_cfg and inv_dyn_cfg
        inv_dyn_joints = set(cfg.inv_dyn_cfg.joint_names)
        other_joints = set()
        if cfg.joint_cfgs is not None:
            for joint_cfg in cfg.joint_cfgs:
                other_joints.update(joint_cfg.joint_names)
        
        # Get the union of all joints
        all_configured_joints = inv_dyn_joints.union(other_joints)
        # Check against the specified joint names in the main config
        if set(cfg.joint_names) != all_configured_joints:
            raise ValueError(
                f"Mismatch in joint coverage. Main config joints: {cfg.joint_names}, "
                f"Configured joints (inv_dyn + others): {all_configured_joints}"
            )

        # Initialize controllers
        self._ik_controller = DifferentialIKController(
            cfg=cfg.controller,
            num_envs=env.num_envs,
            device=env.device
        )
        
        # Initialize inverse dynamics action
        self._inv_dyn_action = InverseDynamicsAction(cfg.inv_dyn_cfg, env)
        self._inv_dyn_joint_ids, _ = self._asset.find_joints(cfg.inv_dyn_cfg.joint_names)
        
        # Initialize other joint actions
        self._other_joint_actions = []
        self._other_joint_ids = []
        if cfg.joint_cfgs is not None:
            for joint_cfg in cfg.joint_cfgs:
                # Determine the action class based on the config type
                action_class = None
                if isinstance(joint_cfg, JointVelocityActionCfg):
                    action_class = JointVelocityAction
                elif isinstance(joint_cfg, JointPositionActionCfg):
                    action_class = JointPositionAction
                elif isinstance(joint_cfg, RelativeJointPositionActionCfg):
                    action_class = RelativeJointPositionAction
                else:
                    raise ValueError(f"Unsupported joint action config type: {type(joint_cfg)}")
                
                # Initialize the action
                joint_action = action_class(joint_cfg, env)
                self._other_joint_actions.append(joint_action)
                # Store joint IDs for this action
                joint_ids, _ = self._asset.find_joints(joint_cfg.joint_names)
                self._other_joint_ids.append(joint_ids)

        # Resolve joint indices
        self._joint_ids, self._joint_names = self._asset.find_joints(cfg.joint_names)
        self._num_joints = len(self._joint_ids)
        if self._num_joints == 0:
            raise ValueError("No joints found for the specified joint names.")

        # Resolve body index
        body_ids, body_names = self._asset.find_bodies(cfg.body_name)
        if len(body_ids) != 1:
            raise ValueError(f"Expected one match for the body name: {cfg.body_name}. Found {len(body_ids)}: {body_names}.")
        self._body_idx = body_ids[0]
        self._body_name = body_names[0]
        
        # Simplify indexing if controlling all joints
        if self._num_joints == self._asset.num_joints:
            self._joint_ids = slice(None)
        
        # Adjust Jacobian index for fixed base
        if self._asset.is_fixed_base:
            self._jacobi_body_idx = self._body_idx - 1
            self._jacobi_joint_ids = self._joint_ids
        else:
            self._jacobi_body_idx = self._body_idx
            # Shift by 6 the joint indices for the DOFs of the floating base
            if isinstance(self._joint_ids, slice):
                # Shift the slice start and stop by 6
                start = self._joint_ids.start + 6 if self._joint_ids.start is not None else 6
                stop = self._joint_ids.stop + 6 if self._joint_ids.stop is not None else None
                step = self._joint_ids.step
                self._jacobi_joint_ids = slice(start, stop, step)
            else:
                self._jacobi_joint_ids = [jid + 6 for jid in self._joint_ids]
        
        # Initialize action tensors
        self._raw_actions = torch.zeros(env.num_envs, self.action_dim, device=env.device)
        self._processed_actions = torch.zeros_like(self._raw_actions)

        # Scaling factor
        if isinstance(cfg.scale, float):
            self._scale = torch.full((env.num_envs, self.action_dim), cfg.scale, device=env.device)
        else:
            self._scale = torch.tensor(cfg.scale, device=env.device).unsqueeze(0).repeat(env.num_envs, 1)

        # Handle body offset
        if cfg.body_offset is not None:
            self._offset_pos = torch.tensor(cfg.body_offset["pos"], device=env.device).unsqueeze(0).repeat(env.num_envs, 1)
            self._offset_rot = torch.tensor(cfg.body_offset["rot"], device=env.device).unsqueeze(0).repeat(env.num_envs, 1)
        else:
            self._offset_pos, self._offset_rot = None, None

        # Storage for desired joint velocities
        self._desired_joint_vel = torch.zeros(env.num_envs, self._num_joints, device=env.device)

        # Log resolved joints and body
        carb.log_info(
            f"Resolved joint names for the combined action term {self.__class__.__name__}: "
            f"{self._joint_names} [{self._joint_ids}]"
        )
        carb.log_info(
            f"Resolved body name for the combined action term {self.__class__.__name__}: {self._body_name} [{self._body_idx}]"
        )

    @property
    def action_dim(self) -> int:
        return self._ik_controller.action_dim

    @property
    def raw_actions(self) -> torch.Tensor:
        return self._raw_actions

    @property
    def processed_actions(self) -> torch.Tensor:
        return self._processed_actions

    def process_actions(self, actions: torch.Tensor):
        """Process raw velocity actions by scaling and setting commands for the ID controller."""
        # Store raw actions
        self._raw_actions[:] = actions

        # Scale actions and convert joint velocities to delta
        self._processed_actions[:] = self._raw_actions * self._scale * self._env.step_dt

        # Compute current end-effector pose
        ee_pos_curr, ee_quat_curr = self._compute_frame_pose()

        # Set command in IK controller
        self._ik_controller.set_command(self._processed_actions, ee_pos_curr, ee_quat_curr)

        # Compute Jacobian
        jacobian = self._compute_frame_jacobian()

        # Get current joint positions
        joint_pos = self._asset.data.joint_pos[:, self._joint_ids]

        # Compute desired joint positions via IK
        desired_joint_pos = self._ik_controller.compute(
            ee_pos_curr, ee_quat_curr, jacobian, joint_pos
        )

        # Compute desired joint velocities
        self._desired_joint_vel = (desired_joint_pos - joint_pos) / self._env.step_dt
        self._inv_dyn_action.process_actions(self._desired_joint_vel[:, self._inv_dyn_joint_ids])

        if self._other_joint_actions is not None:
            for joint_action in self._other_joint_actions:
                joint_action.process_actions(self._desired_joint_vel[:, self._other_joint_ids].squeeze(1))

    def apply_actions(self):
        """Compute and apply joint torques using the inverse dynamics controller."""
        self._inv_dyn_action.apply_actions()

        if self._other_joint_actions is not None:
            for joint_action in self._other_joint_actions:
                joint_action.apply_actions()

    def reset(self, env_ids: Sequence[int] | None = None) -> None:
        """Reset the action term."""
        if env_ids is not None:
            self._raw_actions[env_ids] = 0.0
            self._processed_actions[env_ids] = 0.0
            self._desired_joint_vel[env_ids] = 0.0

    def _compute_frame_pose(self) -> tuple[torch.Tensor, torch.Tensor]:
        """Computes the pose of the target frame in the root frame."""
        # Obtain quantities from simulation
        ee_pose_w = self._asset.data.body_state_w[:, self._body_idx, :7]
        root_pose_w = self._asset.data.root_state_w[:, :7]

        # Compute the pose of the body in the root frame
        ee_pose_b, ee_quat_b = math_utils.subtract_frame_transforms(
            root_pose_w[:, 0:3],
            root_pose_w[:, 3:7],
            ee_pose_w[:, 0:3],
            ee_pose_w[:, 3:7]
        )

        # Account for the offset if any
        if self.cfg.body_offset is not None:
            ee_pose_b, ee_quat_b = math_utils.combine_frame_transforms(
                ee_pose_b,
                ee_quat_b,
                self._offset_pos,
                self._offset_rot
            )

        return ee_pose_b, ee_quat_b

    def _compute_frame_jacobian(self) -> torch.Tensor:
        """Computes the geometric Jacobian of the target frame in the root frame."""
        # Get the Jacobian from the asset
        jacobian = self._asset.root_physx_view.get_jacobians()[:, self._jacobi_body_idx, :, self._jacobi_joint_ids]

        # Account for offset if any
        if self.cfg.body_offset is not None:
            # Modify the Jacobian to account for the offset
            # Translational part
            jacobian[:, 0:3, :] += torch.bmm(
                -math_utils.skew_symmetric_matrix(self._offset_pos),
                jacobian[:, 3:, :]
            )
            # Rotational part
            jacobian[:, 3:, :] = torch.bmm(
                math_utils.matrix_from_quat(self._offset_rot),
                jacobian[:, 3:, :]
            )

        return jacobian
