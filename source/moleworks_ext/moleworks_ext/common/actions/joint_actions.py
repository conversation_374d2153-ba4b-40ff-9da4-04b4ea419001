from __future__ import annotations

import numpy as np
import torch
from typing import TYPE_CHECKING

from isaaclab.envs.mdp.actions import JointAction
from isaaclab.managers.action_manager import ActionTerm

from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsController, InverseDynamicsControllerCfg

if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedEnv

    from . import actions_cfg


class InverseDynamicsAction(JointAction):
    """Joint action term that applies the processed actions to the articulation's joints as effort commands."""

    cfg: actions_cfg.InverseDynamicsActionCfg
    """The configuration of the action term."""

    def __init__(self, cfg: actions_cfg.InverseDynamicsActionCfg, env: ManagerBasedEnv):
        super().__init__(cfg, env)

        self.inv_dyn_controller = InverseDynamicsController(cfg.controller_cfg, env.num_envs, self._asset.device)
        self._joint_ids = torch.tensor(self._env.scene.articulations[cfg.asset_name].find_joints(self.cfg.joint_names)[0], device=self._env.device)
        print("inverse dynamics joint ids: ", self._joint_ids)
        
        # Extract lower and upper velocity limits from dof_limits list
        dof_limits_tensor = cfg.controller_cfg.dof_limits
        lower_limits = [limit[0] for limit in dof_limits_tensor]
        upper_limits = [limit[1] for limit in dof_limits_tensor]
        
        self.vel_limits_lower = torch.tensor(lower_limits, device=self.device)
        self.vel_limits_upper = torch.tensor(upper_limits, device=self.device)

    def apply_actions(self):
        # Set the right command
        # Again Preprocessing: Transform action into des_dof_vel, called here since called every inter decimation steps
        self._env.des_dof_vel = torch.clip(self._processed_actions, self.vel_limits_lower, self.vel_limits_upper)  # = self._env.clipped_scale_actions
        self._env.des_dof_pos = self._env.scene['robot'].data.joint_pos[:, self._joint_ids]
        # Output of preprocessing
        #self._env.des_dof_vel[self._env.inter_decimation_self_collision.squeeze()] = 0.0

        # Set the right command
        at_min_limit = self._env.scene['robot'].data.joint_pos.clone() <= self._env.scene['robot'].data.soft_joint_pos_limits[..., 0]
        at_max_limit = self._env.scene['robot'].data.joint_pos.clone() >= self._env.scene['robot'].data.soft_joint_pos_limits[..., 1]
        # Need to handle reset
        if hasattr(self._env, "episode_length_buf"):
            self._env.des_dof_pos[self._env.episode_length_buf%100 == 0] = self._env.scene['robot'].data.joint_pos[self._env.episode_length_buf%100 == 0]
            self._env.des_dof_pos[~(at_max_limit | at_min_limit)] += (self._env.des_dof_vel.reshape(self._env.num_envs, 4) * self._env.physics_dt)[~(at_max_limit | at_min_limit)]

        self.inv_dyn_controller.set_command(self._env.des_dof_vel, self._env.des_dof_pos)
        ext_f_genco_tau = torch.matmul(self._env.m545_measurements.bucket_jac_lin_T_dof[:, self._joint_ids, :], self._env.bucket_force_com.unsqueeze(-1))

        external_force = ext_f_genco_tau.squeeze(-1)
        ext_m_genco_tau = torch.matmul(self._env.m545_measurements.bucket_jac_rot_T_dof[:, self._joint_ids, :], self._env.bucket_moment_com.unsqueeze(-1))
        external_moment = ext_m_genco_tau.squeeze(-1)

        # Log
        self._env.ext_f_tau = external_force
        self._env.ext_m_tau = external_moment
        
        id_torques = self.inv_dyn_controller.compute(
            self._asset.data.joint_pos[:, self._joint_ids],
            self._asset.data.joint_vel[:, self._joint_ids],
            self._env.m545_measurements.mm,
            self._env.m545_measurements.gravity_tau,
            self._env.m545_measurements.coriolis_centrifugal_force,
            external_force,
            external_moment,
            self._env
        )
        self._env.torques = id_torques
        # # Post Processing: Limit updates and clip
        self._env.torques[:] = id_torques
        self._env.limits.update(self._asset.data.joint_pos[:, self._joint_ids])
        id_torques = torch.clip(
            id_torques, self._env.limits.curr_torque_limit_lower, self._env.limits.curr_torque_limit_upper
        )
        # Set joint effort targets
        self._asset.set_joint_effort_target(id_torques, joint_ids=self._joint_ids)
