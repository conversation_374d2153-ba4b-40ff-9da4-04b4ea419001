# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import torch
from isaaclab.markers import VisualizationMarkers, VisualizationMarkersCfg
import isaaclab.utils.math as math_utils
import isaaclab.sim as sim_utils
from isaaclab.markers.config import FRAME_MARKER_CFG


class BaseTrajectoryGenerator:
    def __init__(self, device):
        self.device = device
        self.threshold = 0.05
        
        # Visualization settings
        self.target_marker = None
        self.path_markers = []
        self.num_path_markers = 36
        
        # Control parameters
        self.integral_error = torch.zeros(3, device=device)
        self.max_integral = 1.0
        self.kp = 50.0  # Proportional gain
        self.ki = 20.0  # Integral gain
        
        self.current_time = 0.0
        
    def get_desired_position(self, t):
        """Get desired position at time t. Must be implemented by subclasses."""
        raise NotImplementedError
        
    def get_velocity_command(self, current_position, dt, max_velocity=10.0):
        """Generic PI control for trajectory tracking"""
        desired_pos = self.get_desired_position(self.current_time)
        
        # Calculate position error
        error = desired_pos - current_position
        
        # Update integral error with anti-windup
        self.integral_error += error * dt
        self.integral_error = torch.clamp(self.integral_error, -self.max_integral, self.max_integral)
        
        # PI control
        velocity = self.kp * error + self.ki * self.integral_error
        
        # Remove this line to allow y-axis velocity
        # velocity[1] = 0.0
        # self.integral_error[1] = 0.0
        
        # Clip velocity magnitude
        velocity_magnitude = torch.norm(velocity)
        if velocity_magnitude > max_velocity:
            velocity = velocity * (max_velocity / velocity_magnitude)
            
        self.current_time += dt
        return velocity

    def setup_visualization(self):
        """Setup visualization markers for the trajectory"""
        # Create target marker (red sphere)
        target_marker_cfg = VisualizationMarkersCfg(
            prim_path="/World/Visuals/target_marker",
            markers={
                "sphere": sim_utils.SphereCfg(
                    radius=0.05,
                    visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1.0, 0.0, 0.0))
                )
            }
        )
        self.target_marker = VisualizationMarkers(target_marker_cfg)
        
        # Create path markers (small blue spheres)
        for i in range(self.num_path_markers):
            marker_cfg = VisualizationMarkersCfg(
                prim_path=f"/World/Visuals/path_marker_{i}",
                markers={
                    "sphere": sim_utils.SphereCfg(
                        radius=0.05,
                        visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0))
                    )
                }
            )
            self.path_markers.append(VisualizationMarkers(marker_cfg))


class CircleTrajectoryGenerator(BaseTrajectoryGenerator):
    def __init__(self, device):
        super().__init__(device)
        self.center = None
        self.radius = None
        self.angular_velocity = None
        self.current_angle = 0.0
        
    def initialize(self, center, radius, period=10.0):
        """Initialize circle parameters"""
        self.center = center.clone().detach().to(self.device).squeeze()
        self.radius = radius
        self.angular_velocity = 2 * torch.pi / period
        self.current_angle = 0.0
        
    def get_desired_position(self, t):
        """Get desired position at time t"""
        angle = self.angular_velocity * t
        x = self.center[0] + self.radius * torch.cos(torch.tensor(angle, device=self.device))
        z = self.center[2] + self.radius * torch.sin(torch.tensor(angle, device=self.device))
        return torch.tensor([x, self.center[1], z], device=self.device)
        
    def update_visualization(self):
        """Update visualization for circle trajectory"""
        if self.center is None or self.target_marker is None:
            return
            
        # Update target marker
        target_pos = self.get_desired_position(self.current_time).unsqueeze(0)
        self.target_marker.visualize(translations=target_pos)
        
        # Update path markers
        for i, marker in enumerate(self.path_markers):
            angle = 2 * torch.pi * i / self.num_path_markers
            pos = self.get_desired_position(angle / self.angular_velocity).unsqueeze(0)
            marker.visualize(translations=pos)


class LinearTrajectoryGenerator(BaseTrajectoryGenerator):
    def __init__(self, device):
        super().__init__(device)
        self.start_pos = None
        self.end_pos = None
        self.duration = None
        self.current_time = 0.0
        
    def initialize(self, start_pos, end_pos, duration):
        """Initialize linear trajectory parameters"""
        self.start_pos = start_pos.clone().detach().to(self.device)
        self.end_pos = end_pos.clone().detach().to(self.device)
        self.duration = duration
        self.current_time = 0.0
        
    def get_desired_position(self, t):
        """Get desired position at time t"""
        alpha = min(t / self.duration, 1.0)
        return self.start_pos + alpha * (self.end_pos - self.start_pos)


class Circular3DTrajectoryGenerator(BaseTrajectoryGenerator):
    def __init__(self, device):
        super().__init__(device)
        self.center = None
        self.radius = None
        self.angular_velocity = None
        self.height = None
        self.current_angle = 0.0
        
    def initialize(self, center, radius, height, period=10.0):
        """Initialize 3D circle parameters
        
        Args:
            center (torch.Tensor): Center point of the circle in XY plane
            radius (float): Radius of the circular path
            height (float): Constant Z height to maintain
            period (float): Time to complete one full rotation in seconds
        """
        self.center = center.clone().detach().to(self.device).squeeze()
        self.radius = radius
        self.height = height
        self.angular_velocity = 2 * torch.pi / period
        self.current_angle = 0.0
        
    def get_desired_position(self, t):
        """Get desired position at time t"""
        angle = self.angular_velocity * t
        x = self.center[0] + self.radius * torch.cos(torch.tensor(angle, device=self.device))
        y = self.center[1] + self.radius * torch.sin(torch.tensor(angle, device=self.device))
        return torch.tensor([x, y, self.height], device=self.device)
        
    def update_visualization(self):
        """Update visualization for 3D circle trajectory"""
        if self.center is None or self.target_marker is None:
            return
            
        # Update target marker
        target_pos = self.get_desired_position(self.current_time).unsqueeze(0)
        self.target_marker.visualize(translations=target_pos)
        
        # Update path markers
        for i, marker in enumerate(self.path_markers):
            angle = 2 * torch.pi * i / self.num_path_markers
            pos = self.get_desired_position(angle / self.angular_velocity).unsqueeze(0)
            marker.visualize(translations=pos)


class CircularPoseTrajectoryGenerator(BaseTrajectoryGenerator):
    def __init__(self, device, debug_vis=False):
        super().__init__(device)
        self.center = None
        self.radius = None
        self.angular_velocity = None
        self.height = None
        self.target_orientation = None
        
        # Separate gains for position and orientation
        self.kp_pos = 50.0
        self.ki_pos = 20.0
        self.kp_rot = 20.0
        self.ki_rot = 5.0
        
        # Modified to handle batched inputs
        self.pos_integral_error = None
        self.rot_integral_error = None

        # Add visualization markers for path poses
        self.path_pose_visualizers = []
        for i in range(self.num_path_markers):
            path_marker_cfg = FRAME_MARKER_CFG.copy()
            path_marker_cfg.markers["frame"].scale = (0.05, 0.05, 0.05)  # Smaller frames for path
            path_marker_cfg.prim_path = f"/Visuals/Trajectory/path_pose_{i}"
            self.path_pose_visualizers.append(VisualizationMarkers(path_marker_cfg))

        # Add a flag to track if path poses have been initialized
        self.path_poses_initialized = False

        self.debug_vis = debug_vis
        self.current_position = None
        self.current_orientation = None

        # Add visualization markers for current pose (blue)
        current_marker_cfg = FRAME_MARKER_CFG.copy()
        current_marker_cfg.markers["frame"].scale = (0.1, 0.1, 0.1)
        current_marker_cfg.prim_path = "/Visuals/Trajectory/current_pose"
        self.current_pose_visualizer = VisualizationMarkers(current_marker_cfg)

        # Add storage for current base quaternion
        self.current_base_quat = None
        self.current_base_pos = None

        # Add visualization markers for current target pose (blue)
        target_marker_cfg = FRAME_MARKER_CFG.copy()
        target_marker_cfg.markers["frame"].visual_material = sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0))
        target_marker_cfg.markers["frame"].scale = (0.05, 0.05, 0.05)  # Smaller frames for path
        target_marker_cfg.prim_path = "/Visuals/Trajectory/target_pose"
        self.target_pose_visualizer = VisualizationMarkers(target_marker_cfg)

    def initialize(self, center, radius, height, target_orientation, period=10.0):
        """Initialize trajectory parameters
    
        Args:
            center (torch.Tensor): Center points of shape (B, 3)
            radius (torch.Tensor): Radii of shape (B, 1)
            height (torch.Tensor): Heights of shape (B, 1)
            target_orientation (torch.Tensor): Target orientations in world frame (B, 4)
            period (float or torch.Tensor): Time periods of shape (B, 1)
        """
        self.center = center.clone().detach().to(self.device)
        self.radius = radius.clone().detach().to(self.device)
        self.height = height.clone().detach().to(self.device)
        
        # Store the target orientation in world frame
        self.target_orientation = target_orientation.clone().detach().to(self.device)
        
        # Handle both scalar and batched periods
        if isinstance(period, (int, float)):
            period = torch.full((center.shape[0], 1), period, device=self.device)
        self.angular_velocity = 2 * torch.pi / period
        
        # Initialize integral errors with proper batch dimension
        self.pos_integral_error = torch.zeros(center.shape[0], 3, device=self.device)
        self.rot_integral_error = torch.zeros(center.shape[0], 3, device=self.device)
        
        # Reset the path poses initialization flag
        self.path_poses_initialized = False

    def get_desired_pose(self, t, base_quat, base_pos):  # Added base_pos parameter
        """Get desired position and orientation at time t"""
        # Compute position as before
        angle = self.angular_velocity * t
        x = self.center[..., 0:1] + self.radius * torch.cos(angle)
        y = self.center[..., 1:2] + self.radius * torch.sin(angle)
        pos = torch.cat([x, y, self.height], dim=-1)
        
        # Extract the desired pitch from target orientation
        target_roll, target_pitch, target_yaw = math_utils.euler_xyz_from_quat(self.target_orientation)
        
        # Get the current base orientation
        base_roll, base_pitch, base_yaw = math_utils.euler_xyz_from_quat(base_quat)
        
        # Calculate direction vector from base to current position on path
        direction = pos - base_pos  # Changed from self.center to base_pos
        print(f"direction: {direction}")
        print(f"base_pos: {base_pos}")
        print(f"pos: {pos}")
        # Compute path yaw as the angle between the direction vector and x-axis
        path_yaw = torch.atan2(direction[..., 1], direction[..., 0])
        print(f"path_yaw: {path_yaw}")
        print(f"base_yaw: {base_yaw}")
        # Add base yaw to get the desired yaw in world frame
        desired_yaw = path_yaw + base_yaw
        # zero tesnors for testing purposes 
        # desired_yaw = torch.zeros_like(desired_yaw)
        
        # Create orientation quaternion
        desired_quat = math_utils.quat_from_euler_xyz(
            torch.zeros_like(base_roll),  # roll
            target_pitch,                 # pitch
            desired_yaw                   # yaw that follows the path
        )

        return pos, desired_quat
        
    def get_velocity_command(self, current_position, current_orientation, dt, base_quat, base_pos, max_velocity=10.0, max_angular_velocity=1.0):
        """Compute batched velocity commands
        
        Args:
            current_position (torch.Tensor): Current end effector position (B, 3)
            current_orientation (torch.Tensor): Current end effector orientation (B, 4)
            dt (float): Time step
            base_quat (torch.Tensor): Base frame quaternion (B, 4)
            max_velocity (float): Maximum linear velocity
            max_angular_velocity (float): Maximum angular velocity
        """
        if self.debug_vis:
            self.current_position = current_position
            self.current_orientation = current_orientation
            
        # Store the current base quaternion for visualization
        self.current_base_quat = base_quat.clone()
        self.current_base_pos = base_pos.clone()

        desired_pos, desired_quat = self.get_desired_pose(self.current_time, base_quat, base_pos)
        
        # Compute errors (assumes math_utils.compute_pose_error handles batched inputs)
        pos_error, rot_error = math_utils.compute_pose_error(
            current_position,
            current_orientation,
            desired_pos,
            desired_quat,
            rot_error_type="axis_angle"
        )
        
        # Update integral errors with anti-windup (batched)
        self.pos_integral_error += pos_error * dt
        self.pos_integral_error = torch.clamp(self.pos_integral_error, -self.max_integral, self.max_integral)
        
        self.rot_integral_error += rot_error * dt
        self.rot_integral_error = torch.clamp(self.rot_integral_error, -self.max_integral, self.max_integral)
        
        # Compute velocities (batched)
        linear_velocity = self.kp_pos * pos_error + self.ki_pos * self.pos_integral_error
        angular_velocity = self.kp_rot * rot_error + self.ki_rot * self.rot_integral_error
        
        # Clip velocities (batched)
        linear_velocity_magnitude = torch.norm(linear_velocity, dim=-1, keepdim=True)  # Shape: (B, 1)
        scaling = torch.where(linear_velocity_magnitude > max_velocity, max_velocity / linear_velocity_magnitude, torch.ones_like(linear_velocity_magnitude))
        linear_velocity = linear_velocity * scaling

        angular_velocity_magnitude = torch.norm(angular_velocity, dim=-1, keepdim=True)  # Shape: (B, 1)
        scaling = torch.where(angular_velocity_magnitude > max_angular_velocity, max_angular_velocity / angular_velocity_magnitude, torch.ones_like(angular_velocity_magnitude))
        angular_velocity = angular_velocity * scaling

        # Combine into command vector (B, 6)
        velocity_command = torch.cat([linear_velocity, angular_velocity], dim=-1)
        
        self.current_time += dt
        return velocity_command

    def update_visualization(self):
        """Updates the visualization markers for the target and current poses."""
        if self.center is None or self.current_base_quat is None:
            return
        
        # Update current pose visualization if debug is enabled
        if self.debug_vis and self.current_position is not None and self.current_orientation is not None:
            self.current_pose_visualizer.visualize(
                translations=self.current_position,
                orientations=self.current_orientation
            )
        
        # Initialize path poses only once
        if not self.path_poses_initialized:
            for i, marker in enumerate(self.path_pose_visualizers):
                t = (i / self.num_path_markers) * (2 * torch.pi / self.angular_velocity)
                pos, quat = self.get_desired_pose(t, self.current_base_quat, self.current_base_pos)
                marker.visualize(
                    translations=pos,
                    orientations=quat
                )
            self.path_poses_initialized = True
        
        # Visualize current target pose
        desired_pos, desired_quat = self.get_desired_pose(self.current_time, self.current_base_quat, self.current_base_pos)
        self.target_pose_visualizer.visualize(
            translations=desired_pos,
            orientations=desired_quat
        )




class CylindricalPoseTrajectoryGenerator(BaseTrajectoryGenerator):
    def __init__(self, device, debug_vis=False):
        super().__init__(device)
        self.start_pos = None
        self.start_quat = None
        self.target_pos = None
        self.target_quat = None
        self.duration = None
        
        # Control gains
        self.kp_pos = 50.0
        self.ki_pos = 20.0
        self.kp_rot = 20.0
        self.ki_rot = 5.0
        
        # Integral errors
        self.pos_integral_error = None
        self.rot_integral_error = None
        
        # Visualization setup similar to CircularPoseTrajectoryGenerator
        self.debug_vis = debug_vis
        self.path_poses_initialized = False
        self.path_pose_visualizers = []
        
        # Setup visualization markers
        self._setup_visualization_markers()
        
    def _setup_visualization_markers(self):
        """Setup all visualization markers"""
        # Path pose markers
        for i in range(self.num_path_markers):
            path_marker_cfg = FRAME_MARKER_CFG.copy()
            path_marker_cfg.markers["frame"].scale = (0.05, 0.05, 0.05)
            path_marker_cfg.prim_path = f"/Visuals/Trajectory/cylindrical_path_{i}"
            self.path_pose_visualizers.append(VisualizationMarkers(path_marker_cfg))
            
        # Current pose marker
        current_marker_cfg = FRAME_MARKER_CFG.copy()
        current_marker_cfg.markers["frame"].scale = (0.1, 0.1, 0.1)
        current_marker_cfg.prim_path = "/Visuals/Trajectory/cylindrical_current"
        self.current_pose_visualizer = VisualizationMarkers(current_marker_cfg)
        
        # Target pose marker
        target_marker_cfg = FRAME_MARKER_CFG.copy()
        target_marker_cfg.markers["frame"].scale = (0.1, 0.1, 0.1)
        target_marker_cfg.markers["frame"].visual_material = sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0))
        target_marker_cfg.prim_path = "/Visuals/Trajectory/cylindrical_target"
        self.target_pose_visualizer = VisualizationMarkers(target_marker_cfg)
        
    def initialize(self, start_pos, start_quat, target_pos, target_quat, duration):
        """Initialize trajectory with start and target poses
        
        Args:
            start_pos (torch.Tensor): Starting position (B, 3)
            start_quat (torch.Tensor): Starting orientation (B, 4)
            target_pos (torch.Tensor): Target position (B, 3)
            target_quat (torch.Tensor): Target orientation (B, 4)
            duration (float): Duration of the trajectory
        """
        self.start_pos = start_pos.clone().detach().to(self.device)
        self.start_quat = start_quat.clone().detach().to(self.device)
        self.target_pos = target_pos.clone().detach().to(self.device)
        self.target_quat = target_quat.clone().detach().to(self.device)
        self.duration = duration
        
        # Initialize integral errors
        batch_size = start_pos.shape[0]
        self.pos_integral_error = torch.zeros(batch_size, 3, device=self.device)
        self.rot_integral_error = torch.zeros(batch_size, 3, device=self.device)
        
        self.current_time = 0.0
        self.path_poses_initialized = False
        
    def get_desired_pose(self, t):
        """Get desired pose using cylindrical interpolation
        
        The position interpolation uses cylindrical coordinates (r, θ, z)
        """
        # Convert to cylindrical coordinates
        start_r = torch.norm(self.start_pos[..., :2], dim=-1)
        start_theta = torch.atan2(self.start_pos[..., 1], self.start_pos[..., 0])
        start_z = self.start_pos[..., 2]
        
        target_r = torch.norm(self.target_pos[..., :2], dim=-1)
        target_theta = torch.atan2(self.target_pos[..., 1], self.target_pos[..., 0])
        target_z = self.target_pos[..., 2]
        
        # Ensure the shortest angular path
        delta_theta = target_theta - start_theta
        delta_theta = torch.where(
            delta_theta > torch.pi,
            delta_theta - 2*torch.pi,
            torch.where(
                delta_theta < -torch.pi,
                delta_theta + 2*torch.pi,
                delta_theta
            )
        )
        
        # Interpolate in cylindrical coordinates
        alpha = torch.clamp(t / self.duration, 0.0, 1.0)
        current_r = start_r + alpha * (target_r - start_r)
        current_theta = start_theta + alpha * delta_theta
        current_z = start_z + alpha * (target_z - start_z)
        
        # Convert back to Cartesian coordinates
        pos = torch.stack([
            current_r * torch.cos(current_theta),
            current_r * torch.sin(current_theta),
            current_z
        ], dim=-1)
        
        # Spherical interpolation for orientation
        quat = math_utils.slerp(self.start_quat, self.target_quat, alpha)
        
        return pos, quat




