# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import torch
from collections.abc import Sequence
from dataclasses import MISSING
from typing import Optional, Sequence, Tuple, Union

from isaaclab.utils import configclass


@configclass
class InverseDynamicsControllerCfg:
    """Configuration for Inverse Dynamics regulation controller."""

    command_type: str = "vel"
    """Type of command: pos (absolute) or vel (absolute)."""

    dof_offset: Optional[Sequence[float]] = None
    """Offset to DOF command given to controller. (default: :obj:`None`).

    If :obj:`None` then offsets are set to zero.
    """

    dof_limits: Optional[Sequence[Tuple[float, float]]] = None

    k_p: Union[float, Sequence[float]] = MISSING
    """The positional gain for determining desired torques based on position error."""

    k_d: Optional[Union[float, Sequence[float]]] = None
    """The gain to compute desired torques based on joint velocity error."""

    dof_efforts_limits: Optional[Sequence[Tuple[float, float]]] = None


class InverseDynamicsController:
    """Inverse dynamics controller.

    Reference:
        [1] https://ethz.ch/content/dam/ethz/special-interest/mavt/robotics-n-intelligent-systems/rsl-dam/documents/RobotDynamics2017/RD_HS2017script.pdf
    """

    def __init__(self, cfg: InverseDynamicsControllerCfg, num_robots: int, device: str):
        """Initialize Inverse Dynamics controller.

        Args:
            cfg (InverseDynamicsControllerCfg): The configuration for the controller.
            num_robots (int): The number of robots to control.
            dof_limits (np.array): The joint position limits for each robot. This is a tensor of shape
                (num_dof, 2) where the last dimension contains the lower and upper limits.
            dof_effors_limits (np.array): The joint effort limits for each robot. This is a tensor of shape
            device (str): The device to use for computations.

        Raises:
            ValueError: When the shape of :obj:`dof_limits` is not (num_robots, num_dof, 2).
        """
        # store inputs
        self.cfg = cfg
        self.num_robots = num_robots
        # first dimension of the list
        self._device = device
        self.num_dof = len(cfg.dof_limits)

        # create buffers
        # -- commands
        self._dof_target = torch.zeros(self.num_robots, self.num_dof, device=self._device)
        self._dof_pos_target = torch.zeros(self.num_robots, self.num_dof, device=self._device)
        # -- offsets
        self._dof_offset = torch.zeros(self.num_robots, self.num_dof, device=self._device)
        # -- limits
        self._dof_limits = torch.tensor(cfg.dof_limits, device=self._device)
        self._dof_limits = self._dof_limits.unsqueeze(0).repeat(self.num_robots, 1, 1)
        # -- positional gains
        self._p_gains = torch.zeros(self.num_robots, self.num_dof, device=self._device)
        self._p_gains[:] = torch.tensor(self.cfg.k_p, device=self._device)
        # -- velocity gains
        self._d_gains = torch.zeros(self.num_robots, self.num_dof, device=self._device)
        self._d_gains[:] = torch.tensor(self.cfg.k_d, device=self._device)
        # -- position offsets
        if self.cfg.dof_offset is not None:
            self._dof_offset[:] = torch.tensor(self.cfg.dof_offset, device=self._device)
        # -- effort limits
        self._effort_limits = torch.tensor(cfg.dof_efforts_limits, device=self._device)
        self._effort_limits = self._effort_limits.unsqueeze(0).repeat(self.num_robots, 1, 1)

    def initialize(self):
        """Initialize the internals."""
        pass

    def reset_idx(self, robot_ids: torch.Tensor = None):
        """Reset the internals."""
        pass

    def set_command(self, vel_command: torch.Tensor, pos_command: torch.Tensor):
        """Set target end-effector pose command.

        Args:
            command (torch.Tensor): The command to set. This is a tensor of shape (num_robots, num_actions) where
                :obj:`num_actions` is the dimension of the action space of the controller.
        """
        # check input size, removed here since 2 joints are unactuated
        # if command.shape != (self.num_robots, self.num_dof):
        #    raise ValueError(
        #        f"Invalid command shape '{command.shape}'. Expected: '{(self.num_robots, self.num_dof)}'."
        #    )
        self._dof_target[:] = vel_command
        self._dof_pos_target[:] = pos_command

        #target_joint_pos[~(at_max_limit | at_min_limit)] += (4*desired_arm_vel.reshape(env.unwrapped.num_envs, 4) * env.unwrapped.physics_dt)[~(at_max_limit | at_min_limit)]

    def compute(
        self,
        dof_pos: torch.Tensor,
        dof_vel: torch.Tensor,
        mass_matrix: Optional[torch.Tensor] = None,
        gravity: Optional[torch.Tensor] = None,
        coriolis_centrifugal_force: Optional[torch.Tensor] = None,
        external_force: Optional[torch.Tensor] = None,
        external_moment: Optional[torch.Tensor] = None,
        env=None,
    ) -> torch.Tensor:
        """Performs inference with the controller.

        Args:
            dof_pos (torch.Tensor): The current joint positions.
            dof_vel (torch.Tensor): The current joint velocities.
            mass_matrix (Optional[torch.Tensor], optional): The joint-space inertial matrix. Defaults to None.
            gravity (Optional[torch.Tensor], optional): The joint-space gravity vector. Defaults to None.
            coriolis_centrifugal_force (Optional[torch.Tensor], optional): The joint-space coriolis and centrifugal vector. Defaults to None.
            external_force (Optional[torch.Tensor], optional): The joint-space external force. Defaults to None.
            external_moment (Optional[torch.Tensor], optional): The joint-space external force. Defaults to None.
            env (Optional[ExcavationEnv], optional): The excavation environmnent. Defaults to None.

        Raises:
            ValueError: When the command type is invalid.

        Returns:
            torch.Tensor: The target joint torques commands.
        """
        # Number of environments and number of joints, not using env to let it be optional
        num_envs = dof_pos.shape[0]
        num_dofs = dof_pos.shape[1]

        # Compute errors
        desired_dof = self._dof_target.clip_(min=self._dof_limits[..., 0], max=self._dof_limits[..., 1])

        # Create masks to check if dof_pos is at its minimum or maximum limit
        # Limits
        if env is not None:
            at_min_limit = dof_pos <= env.scene['robot'].data.soft_joint_pos_limits[..., 0][:,env.m545_measurements.arm_joint_ids]
            at_max_limit = dof_pos >= env.scene['robot'].data.soft_joint_pos_limits[..., 1][:,env.m545_measurements.arm_joint_ids]
            # Check dof vel direction
            moving_towards_min = (desired_dof < 0) & at_min_limit
            moving_towards_max = (desired_dof > 0) & at_max_limit
        # Debug
        #env.at_min_limit = moving_towards_min.float()
        #if at_min_limit.any():
        #    print("at_min_limit", torch.where(at_min_limit))
        #env.at_max_limit = moving_towards_max.float()
        #if at_max_limit.any():
        #    print("at_max_limit", torch.where(at_max_limit))s
        #desired_dof[at_min_limit | at_max_limit] = dof_vel[at_min_limit | at_max_limit] 
        # Command type, usually vel for excavation

        if self.cfg.command_type == "pos":
            dof_pos_error = desired_dof - dof_pos
            dof_vel_error = -dof_vel
        elif self.cfg.command_type == "vel":
            dof_pos_error = self._dof_pos_target-dof_pos
            dof_vel_error = desired_dof - dof_vel
        else:
            raise ValueError(f"Invalid command type: {self.cfg.command_type}.")

        # Desired acc based on error
        des_dof_acc = self._p_gains * dof_pos_error + self._d_gains * dof_vel_error
        # Set the necessary dof_acc to 0 
        if env is not None:
            des_dof_acc[moving_towards_min]= 0
            des_dof_acc[moving_towards_max]= 0
        #env.des_dof_acc = des_dof_acc
        
        # Inertial torque
        inertia_tau = mass_matrix.matmul(des_dof_acc.view(num_envs, num_dofs, 1)).view(num_envs, num_dofs)
        if env is not None:
            env.inertia_tau = inertia_tau
        #print("inertia_tau", inertia_tau)
        # Compute torques
        desired_torques = inertia_tau
        desired_torques -= gravity
        # Coriolis and centrifugal force
        if coriolis_centrifugal_force is not None:
            desired_torques += coriolis_centrifugal_force
        # External forces and moments due to soil, optional to tune PID without complexity
        if external_force is not None:
            desired_torques -= external_force
        if external_moment is not None:
            desired_torques -= external_moment
    
        #if env is not None:
        #    if hasattr(env.m545_measurements, "contact_tau_force"):
        #        desired_torques -= env.m545_measurements.contact_tau_force
        #        desired_torques -= env.m545_measurements.contact_tau_moment
        if env is not None:
            if hasattr(env.m545_measurements, "contact_tau_ext_force"):
                desired_torques -= env.m545_measurements.filtered_contact_tau_ext_force
                desired_torques -= env.m545_measurements.filtered_contact_tau_ext_moment
        #desired_torques -= env.contact_tau_ext_force
        #desired_torques -= env.contact_tau_ext_moment
        # cap torques
        desired_torques.clamp_(min=self._effort_limits[..., 0], max=self._effort_limits[..., 1])

        return desired_torques