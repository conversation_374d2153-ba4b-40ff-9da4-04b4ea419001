
import torch
from isaaclab.markers import VisualizationMarkers, VisualizationMarkersCfg
import isaaclab.utils.math as math_utils
import isaaclab.sim as sim_utils
from isaaclab.markers.config import FRAME_MARKER_CFG


class PoseTrajectoryGenerator:
    def __init__(self, device, debug_vis=False):
        self.device = device
        self.threshold = 0.05

        # Visualization settings
        self.target_marker = None
        self.path_markers = []
        self.num_path_markers = 36

        # Control parameters
        self.integral_error = None  # To be initialized based on batch size
        self.max_integral = 1.0
        self.kp = 50.0  # Proportional gain
        self.ki = 20.0  # Integral gain

        self.current_time = None  # To be initialized based on batch size

        # PoseTrajectoryGenerator specific settings
        # Control gains for position and rotation
        self.kp_pos = 50.0
        self.ki_pos = 20.0
        self.kp_rot = 20.0
        self.ki_rot = 5.0

        # Integral errors for position and rotation
        self.pos_integral_error = None
        self.rot_integral_error = None

        # Orientation waypoints
        self.orientation_waypoints = None  # List of (time, quaternion) pairs

        # Visualization setup
        self.debug_vis = debug_vis
        self.path_poses_initialized = False
        self.path_pose_visualizers = []
        self.current_position = None
        self.current_orientation = None

        # Orientation control flags
        self.auto_yaw = True
        self.maintain_base_relative = True
        self.current_base_quat = None
        self.current_base_pos = None

        # Current pose visualization markers
        self.current_pose_visualizer = None
        self.target_pose_visualizer = None

        # Setup visualization markers
        self._setup_visualization_markers()

    def get_desired_position(self, t):
        """Get desired position at time t. Must be implemented by subclasses."""
        raise NotImplementedError

    def get_velocity_command(self, current_position, current_orientation, dt, base_quat=None, base_pos=None, max_velocity=10.0, max_angular_velocity=1.0):
        """Common velocity command computation for all pose trajectories

        Args:
            current_position (torch.Tensor): Current positions (B, 3)
            current_orientation (torch.Tensor): Current orientations quaternions (B, 4)
            dt (torch.Tensor): Time step increments (B,)
            base_quat (torch.Tensor, optional): Base frame quaternions (B, 4)
            base_pos (torch.Tensor, optional): Base frame positions (B, 3)
            max_velocity (float): Maximum linear velocity
            max_angular_velocity (float): Maximum angular velocity

        Returns:
            torch.Tensor: Velocity commands (B, 6) [linear (3) + angular (3)]
        """
        # for visualization
        self.current_base_quat = base_quat
        self.current_base_pos = base_pos
        self.current_position = current_position
        self.current_orientation = current_orientation

        if self.pos_integral_error is None or self.rot_integral_error is None:
            batch_size = current_position.shape[0]
            self.pos_integral_error = torch.zeros(batch_size, 3, device=self.device)
            self.rot_integral_error = torch.zeros(batch_size, 3, device=self.device)

        # Get desired pose with base information
        desired_pos, desired_quat = self.get_desired_pose(
            self.current_time,
            base_quat=base_quat,
            base_pos=base_pos
        )  # (B, 3), (B, 4)

        # Compute errors
        pos_error, rot_error = math_utils.compute_pose_error(
            current_position,
            current_orientation,
            desired_pos,
            desired_quat,
            rot_error_type="axis_angle"
        )  # Both (B, 3)

        # Update integral errors with anti-windup
        self.pos_integral_error += pos_error * dt  # (B, 3)
        self.pos_integral_error = torch.clamp(self.pos_integral_error, -self.max_integral, self.max_integral)

        self.rot_integral_error += rot_error * dt  # (B, 3)
        self.rot_integral_error = torch.clamp(self.rot_integral_error, -self.max_integral, self.max_integral)

        # Compute velocities
        linear_velocity = self.kp_pos * pos_error + self.ki_pos * self.pos_integral_error  # (B, 3)
        angular_velocity = self.kp_rot * rot_error + self.ki_rot * self.rot_integral_error  # (B, 3)

        # Clip linear velocities
        linear_velocity_magnitude = torch.norm(linear_velocity, dim=-1, keepdim=True)  # (B, 1)
        epsilon = 1e-6
        scaling_linear = torch.where(
            linear_velocity_magnitude > max_velocity,
            max_velocity / (linear_velocity_magnitude + epsilon),
            torch.ones_like(linear_velocity_magnitude)
        )
        linear_velocity = linear_velocity * scaling_linear  # (B, 3)

        # Clip angular velocities
        angular_velocity_magnitude = torch.norm(angular_velocity, dim=-1, keepdim=True)  # (B, 1)
        scaling_angular = torch.where(
            angular_velocity_magnitude > max_angular_velocity,
            max_angular_velocity / (angular_velocity_magnitude + epsilon),
            torch.ones_like(angular_velocity_magnitude)
        )
        angular_velocity = angular_velocity * scaling_angular  # (B, 3)

        velocity_command = torch.cat([linear_velocity, angular_velocity], dim=-1)  # (B, 6)

        self.current_time += dt  # (B,)

        return velocity_command  # (B, 6)

    def setup_visualization(self):
        """Setup visualization markers for the trajectory"""
        # Create target marker (red sphere)
        target_marker_cfg = VisualizationMarkersCfg(
            prim_path="/World/Visuals/target_marker",
            markers={
                "sphere": sim_utils.SphereCfg(
                    radius=0.05,
                    visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1.0, 0.0, 0.0))
                )
            }
        )
        self.target_marker = VisualizationMarkers(target_marker_cfg)

        # Create path markers (small blue spheres)
        for i in range(self.num_path_markers):
            marker_cfg = VisualizationMarkersCfg(
                prim_path=f"/World/Visuals/path_marker_{i}",
                markers={
                    "sphere": sim_utils.SphereCfg(
                        radius=0.05,
                        visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0))
                    )
                }
            )
            self.path_markers.append(VisualizationMarkers(marker_cfg))

    def _setup_visualization_markers(self):
        """Setup all visualization markers"""
        # Path pose markers
        for i in range(self.num_path_markers):
            path_marker_cfg = FRAME_MARKER_CFG.copy()
            path_marker_cfg.markers["frame"].scale = (0.05, 0.05, 0.05)
            path_marker_cfg.prim_path = f"/Visuals/Trajectory/PoseTrajectoryGenerator/path_{i}"
            self.path_pose_visualizers.append(VisualizationMarkers(path_marker_cfg))

        # Current pose marker
        current_marker_cfg = FRAME_MARKER_CFG.copy()
        current_marker_cfg.markers["frame"].scale = (0.1, 0.1, 0.1)
        current_marker_cfg.prim_path = f"/Visuals/Trajectory/PoseTrajectoryGenerator/current"
        self.current_pose_visualizer = VisualizationMarkers(current_marker_cfg)

        # Target pose marker
        target_marker_cfg = FRAME_MARKER_CFG.copy()
        target_marker_cfg.markers["frame"].scale = (0.1, 0.1, 0.1)
        target_marker_cfg.markers["frame"].visual_material = sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0))
        target_marker_cfg.prim_path = f"/Visuals/Trajectory/PoseTrajectoryGenerator/target"
        self.target_pose_visualizer = VisualizationMarkers(target_marker_cfg)

    def set_orientation_waypoints(self, waypoints):
        """Set orientation waypoints for the trajectory"""
        if not waypoints:
            raise ValueError("Orientation waypoints list cannot be empty.")

        # Normalize all quaternions in waypoints
        normalized_waypoints = []
        for t, quat in waypoints:
            normalized_quat = quat / quat.norm(dim=-1, keepdim=True)
            normalized_waypoints.append((t, normalized_quat))
        
        # Sort waypoints by time
        self.orientation_waypoints = sorted(normalized_waypoints, key=lambda x: x[0])

        # Debugging: Check for any NaNs
        for t, quat in self.orientation_waypoints:
            if torch.isnan(quat).any():
                raise ValueError(f"Waypoint quaternion at time {t} contains NaNs.")

    def get_desired_orientation(self, normalized_time):
        """Get desired orientation at normalized time using waypoint interpolation

        Args:
            normalized_time (torch.Tensor): Normalized time (B,)

        Returns:
            torch.Tensor: Desired orientation quaternions (B, 4)
        """
        if not self.orientation_waypoints:
            raise ValueError("Orientation waypoints are not set.")

        batch_size = normalized_time.shape[0]
        device = normalized_time.device

        # Initialize desired_quat with the last waypoint's quaternion
        desired_quat = self.orientation_waypoints[-1][1].clone().to(device)  # (B, 4)

        # Iterate through waypoints to find appropriate interpolation
        for i in range(len(self.orientation_waypoints) - 1):
            t1, q1 = self.orientation_waypoints[i]       # q1: (B, 4)
            t2, q2 = self.orientation_waypoints[i + 1]   # q2: (B, 4)
            mask = (normalized_time >= t1) & (normalized_time <= t2)  # (B,)

            if mask.any():
                # Extract relevant batch elements
                alpha = (normalized_time[mask] - t1) / (t2 - t1 + 1e-6)  # (N,)
                q1_masked = q1[mask]  # (N, 4)
                q2_masked = q2[mask]  # (N, 4)
                
                # Perform SLERP
                interpolated_quat = slerp(q1_masked, q2_masked, alpha)  # (N, 4)
                # Assign interpolated quaternions to desired_quat
                desired_quat[mask] = interpolated_quat

        return desired_quat  # (B, 4)

    def initialize_path_visualization(self, env_origins):
        """Initialize visualization markers for the path"""
        if self.debug_vis and not self.path_poses_initialized:
            # Sample points along the trajectory
            times = torch.linspace(0, self.duration, self.num_path_markers, device=self.device)
            for i, t in enumerate(times):
                # Get pose at time t
                pos, quat = self.get_desired_pose(
                    t.expand(self.start_pos.shape[0]),  # Expand to batch size
                    self.current_base_quat,
                    self.current_base_pos
                )

                # Update path marker
                self.path_pose_visualizers[i].visualize(
                    translations=pos + env_origins,
                    orientations=quat
                )
            
            self.path_poses_initialized = True

    def update_visualization(self, env_origins):
        """Common visualization update"""
        # Initialize path visualization if needed
        if self.debug_vis and not self.path_poses_initialized:
            self.initialize_path_visualization(env_origins)

        # Update current pose marker
        if self.debug_vis and self.current_position is not None and self.current_orientation is not None:
            self.current_pose_visualizer.visualize(
                translations=self.current_position + env_origins,
                orientations=self.current_orientation
            )

        # Update target pose marker
        if self.current_time is not None:
            desired_pos, desired_quat = self.get_desired_pose(
                self.current_time,
                self.current_base_quat,
                self.current_base_pos
            )
            self.target_pose_visualizer.visualize(
                translations=desired_pos + env_origins,
                orientations=desired_quat
            )

    def set_orientation_mode(self, auto_yaw=False, maintain_base_relative=False):
        """Set the orientation control mode

        Args:
            auto_yaw (bool): If True, automatically compute yaw based on movement direction
            maintain_base_relative (bool): If True, maintain orientation relative to base frame
        """
        self.auto_yaw = auto_yaw
        self.maintain_base_relative = maintain_base_relative

    def adjust_yaw(self, pos, target_quat, base_quat=None, base_pos=None):
        """Compute desired orientation based on current settings

        Args:
            pos (torch.Tensor): Current desired position (B, 3)
            target_quat (torch.Tensor): Target orientation quaternion (B, 4)
            base_quat (torch.Tensor, optional): Base frame quaternion (B, 4)
            base_pos (torch.Tensor, optional): Base frame position (B, 3)

        Returns:
            torch.Tensor: Desired orientation quaternions (B, 4)
        """
        # Extract pitch from target orientation (we'll maintain this)
        target_roll, target_pitch, target_yaw = math_utils.euler_xyz_from_quat(target_quat)

        if self.auto_yaw and base_quat is not None and base_pos is not None:
            # Get base yaw
            _, _, base_yaw = math_utils.euler_xyz_from_quat(base_quat)
            
            # Calculate direction vector and path yaw
            direction = pos - base_pos
            path_yaw = torch.atan2(direction[..., 1], direction[..., 0])
            
            # Combine path and base yaw
            desired_yaw = path_yaw + base_yaw
            
            # Create orientation quaternion
            desired_quat = math_utils.quat_from_euler_xyz(
                target_roll,                # roll
                target_pitch,               # pitch
                desired_yaw                 # yaw
            )
        else:
            # Use target orientation as is
            desired_quat = target_quat

        return desired_quat


class CylindricalPoseTrajectoryGenerator(PoseTrajectoryGenerator):
    def __init__(self, device, debug_vis=False):
        super().__init__(device, debug_vis)
        self.start_pos = None
        self.start_quat = None
        self.target_pos = None
        self.target_quat = None
        self.duration = None

    def initialize(self, start_pos, start_quat, target_pos, target_quat, duration):
        """Initialize trajectory with start and target poses

        Args:
            start_pos (torch.Tensor): Starting position (B, 3)
            start_quat (torch.Tensor): Starting orientation (B, 4)
            target_pos (torch.Tensor): Target position (B, 3)
            target_quat (torch.Tensor): Target orientation (B, 4)
            duration (float): Duration of the trajectory
        """
        self.start_pos = start_pos.clone().detach().to(self.device)  # (B, 3)
        self.start_quat = start_quat.clone().detach().to(self.device)  # (B, 4)
        self.current_quat = start_quat.clone().detach().to(self.device)  # (B, 4)
        self.current_pos = start_pos.clone().detach().to(self.device)  # (B, 3)
        self.target_pos = target_pos.clone().detach().to(self.device)  # (B, 3)
        self.target_quat = target_quat.clone().detach().to(self.device)  # (B, 4)
        self.duration = duration

        # Initialize integral errors
        batch_size = start_pos.shape[0]
        self.pos_integral_error = torch.zeros(batch_size, 3, device=self.device)
        self.rot_integral_error = torch.zeros(batch_size, 3, device=self.device)

        # Set default orientation waypoints (linear interpolation)
        self.set_orientation_waypoints([
            (0.0, start_quat),
            (1.0, target_quat)
        ])

        self.current_time = torch.zeros(batch_size, device=self.device)  # (B,)
        self.path_poses_initialized = False
    
    def get_desired_pose(self, t, base_quat=None, base_pos=None):
        """Get desired pose using cylindrical interpolation

        The position interpolation uses cylindrical coordinates (r, θ, z) relative to the base position.
        Creates an arc motion between start and target positions.

        Args:
            t (torch.Tensor): Current time (B,)
            base_quat (torch.Tensor, optional): Base frame quaternion (B, 4)
            base_pos (torch.Tensor, optional): Base frame position (B, 3)

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: Desired positions (B, 3) and orientations (B, 4)
        """
        if base_pos is None:
            base_pos = torch.zeros_like(self.start_pos)

        # Convert to coordinates relative to base position
        rel_start_pos = self.start_pos - base_pos  # (B, 3)
        rel_target_pos = self.target_pos - base_pos  # (B, 3)

        # Convert to cylindrical coordinates (r, θ, z) relative to base
        start_xy = rel_start_pos[..., :2]  # (B, 2)
        start_r = torch.norm(start_xy, dim=-1)  # (B,)
        start_theta = torch.atan2(start_xy[..., 1], start_xy[..., 0])  # (B,)
        start_z = rel_start_pos[..., 2]  # (B,)

        target_xy = rel_target_pos[..., :2]  # (B, 2)
        target_r = torch.norm(target_xy, dim=-1)  # (B,)
        target_theta = torch.atan2(target_xy[..., 1], target_xy[..., 0])  # (B,)
        target_z = rel_target_pos[..., 2]  # (B,)

        # Ensure the shortest angular path
        delta_theta = (target_theta - start_theta + torch.pi) % (2 * torch.pi) - torch.pi  # (B,)

        # Linear interpolation parameter
        alpha = torch.clamp(t / self.duration, 0.0, 1.0)  # (B,)

        # Interpolate in cylindrical coordinates
        current_r = start_r + alpha * (target_r - start_r)  # (B,)
        current_theta = start_theta + alpha * delta_theta  # (B,)
        current_z = start_z + alpha * (target_z - start_z)  # (B,)

        # Convert back to Cartesian coordinates
        pos_x = current_r * torch.cos(current_theta)  # (B,)
        pos_y = current_r * torch.sin(current_theta)  # (B,)

        # Combine coordinates and convert back to world frame
        rel_pos = torch.stack([pos_x, pos_y, current_z], dim=-1)  # (B, 3)
        pos = rel_pos + base_pos  # (B, 3)

        # Get orientation using new computation
        quat = self.adjust_yaw(
            pos,
            self.target_quat,
            base_quat,
            base_pos
        )  # (B, 4)

        return pos, quat  # (B, 3), (B, 4)



class SmoothCylindricalPoseTrajectoryGenerator(CylindricalPoseTrajectoryGenerator):
    def __init__(self, device, debug_vis=False):
        super().__init__(device, debug_vis)

    def get_desired_pose(self, t, base_quat=None, base_pos=None):
        """Get desired pose using smooth cylindrical interpolation.

        Args:
            t (torch.Tensor): Current time (B,)
            base_quat (torch.Tensor, optional): Base frame quaternion (B, 4)
            base_pos (torch.Tensor, optional): Base frame position (B, 3)

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: Desired positions (B, 3) and orientations (B, 4)
        """
        if base_pos is None:
            base_pos = torch.zeros_like(self.start_pos)

        # Convert to coordinates relative to base position
        rel_start_pos = self.start_pos - base_pos  # (B, 3)
        rel_target_pos = self.target_pos - base_pos  # (B, 3)

        # Convert to cylindrical coordinates (r, θ, z) relative to base
        start_xy = rel_start_pos[..., :2]  # (B, 2)
        start_r = torch.norm(start_xy, dim=-1)  # (B,)
        start_theta = torch.atan2(start_xy[..., 1], start_xy[..., 0])  # (B,)
        start_z = rel_start_pos[..., 2]  # (B,)

        target_xy = rel_target_pos[..., :2]  # (B, 2)
        target_r = torch.norm(target_xy, dim=-1)  # (B,)
        target_theta = torch.atan2(target_xy[..., 1], target_xy[..., 0])  # (B,)
        target_z = rel_target_pos[..., 2]  # (B,)

        # Ensure the shortest angular path
        delta_theta = (target_theta - start_theta + torch.pi) % (2 * torch.pi) - torch.pi  # (B,)

        # Smooth interpolation parameter using smoothstep function
        s = torch.clamp(t / self.duration, 0.0, 1.0)  # (B,)
        alpha = 3 * s**2 - 2 * s**3  # Smoothstep function

        # Interpolate in cylindrical coordinates
        current_r = start_r + alpha * (target_r - start_r)  # (B,)
        current_theta = start_theta + alpha * delta_theta  # (B,)
        current_z = start_z + alpha * (target_z - start_z)  # (B,)

        # Convert back to Cartesian coordinates
        pos_x = current_r * torch.cos(current_theta)  # (B,)
        pos_y = current_r * torch.sin(current_theta)  # (B,)

        # Combine coordinates and convert back to world frame
        rel_pos = torch.stack([pos_x, pos_y, current_z], dim=-1)  # (B, 3)
        pos = rel_pos + base_pos  # (B, 3)

        # Get desired orientation using adjusted yaw
        quat = self.adjust_yaw(
            pos,
            self.get_desired_orientation(s),  # Use the same smooth interpolation for orientation
            base_quat,
            base_pos
        )  # (B, 4)

        return pos, quat  # (B, 3), (B, 4)

class DumpingTrajectoryGenerator(SmoothCylindricalPoseTrajectoryGenerator):
    def __init__(self, device, debug_vis=False):
        super().__init__(device, debug_vis)
        self.start_pos = None
        self.start_quat = None
        self.target_pos = None
        self.target_quat = None
        self.duration = None

        # Create cylindrical trajectory generator for position control
        # Always set debug_vis=False to avoid duplicate visualizations
        self.cylindrical_traj = CylindricalPoseTrajectoryGenerator(device, debug_vis=False)

    def initialize(self, start_pos, start_quat, target_pos, target_quat, duration, dump_start_time=0.8):
        """Initialize dumping trajectory"""
        # Initialize parent class variables first
        self.start_pos = start_pos.clone().detach().to(self.device)
        self.start_quat = start_quat.clone().detach().to(self.device)
        self.current_quat = start_quat.clone().detach().to(self.device)
        self.current_pos = start_pos.clone().detach().to(self.device)
        self.target_pos = target_pos.clone().detach().to(self.device)
        self.target_quat = target_quat.clone().detach().to(self.device)
        self.duration = duration

        # Initialize cylindrical trajectory
        self.cylindrical_traj.initialize(
            start_pos=start_pos,
            start_quat=start_quat,
            target_pos=target_pos,
            target_quat=target_quat,
            duration=duration
        )

        # Initialize integral errors and current time
        batch_size = start_pos.shape[0]
        self.pos_integral_error = torch.zeros(batch_size, 3, device=self.device)
        self.rot_integral_error = torch.zeros(batch_size, 3, device=self.device)
        self.current_time = torch.zeros(batch_size, device=self.device)
        print("start_quat: ", start_quat)
        # Set orientation waypoints for dumping motion
        self.set_orientation_waypoints([
            (0.0, start_quat),
            (dump_start_time, start_quat),
            (1.0, target_quat)
        ])

        # Reset visualization state
        self.path_poses_initialized = False

    def get_desired_pose(self, t, base_quat=None, base_pos=None):
        """Get desired pose using cylindrical coordinates for position and waypoints for orientation

        Args:
            t (torch.Tensor): Current time (B,)
            base_quat (torch.Tensor, optional): Base frame quaternion (B, 4)
            base_pos (torch.Tensor, optional): Base frame position (B, 3)

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: Desired positions (B, 3) and orientations (B, 4)
        """
        # Use normalized time for orientation interpolation
        normalized_time = torch.clamp(t / self.duration, 0.0, 1.0)  # (B,)

        # Get position using cylindrical interpolation
        pos, _ = self.cylindrical_traj.get_desired_pose(t, base_quat, base_pos)  # (B, 3), _

        # Get orientation from waypoints
        quat = self.get_desired_orientation(normalized_time)  # (B, 4)

        # Apply orientation computation
        quat = self.adjust_yaw(
            pos,
            quat,
            base_quat,
            base_pos
        )  # (B, 4)

        return pos, quat  # (B, 3), (B, 4)


class CircularPoseTrajectoryGenerator(PoseTrajectoryGenerator):
    def __init__(self, device, debug_vis=False):
        super().__init__(device, debug_vis)
        self.center = None
        self.radius = None
        self.angular_velocity = None
        self.height = None
        self.target_orientation = None
        self.duration = 2 * torch.pi  # One full rotation

    def initialize(self, center, radius, height, target_orientation, period=10.0, start_pos=None, start_quat=None):
        """Initialize trajectory parameters

        Args:
            center (torch.Tensor): Center points of shape (B, 3)
            radius (torch.Tensor): Radii of shape (B, 1)
            height (torch.Tensor): Heights of shape (B, 1)
            target_orientation (torch.Tensor): Target orientations in world frame (B, 4)
            period (float or torch.Tensor): Time periods (scalar or (B, 1))
            start_pos (torch.Tensor, optional): Starting positions (B, 3). If None, inferred from center and radius.
            start_quat (torch.Tensor, optional): Starting orientations (B, 4). If None, should be provided.
        """
        self.center = center.clone().detach().to(self.device)
        self.radius = radius.clone().detach().to(self.device)
        self.height = height.clone().detach().to(self.device)
        self.target_orientation = target_orientation.clone().detach().to(self.device)
        
        # Handle both scalar and batched periods
        if isinstance(period, (int, float)):
            self.angular_velocity = 2 * torch.pi / torch.full((center.shape[0], 1), period, device=self.device)
        else:
            self.angular_velocity = (2 * torch.pi / period.to(self.device))
        
        # Initialize integral errors with proper batch dimension
        batch_size = center.shape[0]
        self.pos_integral_error = torch.zeros(batch_size, 3, device=self.device)
        self.rot_integral_error = torch.zeros(batch_size, 3, device=self.device)
        
        self.current_time = torch.zeros(batch_size, device=self.device)
        self.path_poses_initialized = False
        self.duration = period  # Set duration to match period

        # Initialize start_pos and start_quat
        if start_pos is not None:
            self.start_pos = start_pos.clone().detach().to(self.device)
        else:
            # Inferred start_pos based on center and radius (e.g., angle = 0)
            pos_x = self.center[:, 0] + self.radius.squeeze(-1) * torch.cos(torch.zeros(batch_size, device=self.device))
            pos_y = self.center[:, 1] + self.radius.squeeze(-1) * torch.sin(torch.zeros(batch_size, device=self.device))
            pos_z = self.height.squeeze(-1)
            self.start_pos = torch.stack([pos_x, pos_y, pos_z], dim=-1)

        if start_quat is not None:
            self.start_quat = start_quat.clone().detach().to(self.device)
        else:
            # If start_quat is not provided, initialize it to identity quaternion
            self.start_quat = torch.tensor([1.0, 0.0, 0.0, 0.0], device=self.device).repeat(batch_size, 1)

    def get_batch_size(self):
        return self.center.shape[0]

    def get_desired_pose(self, t, base_quat=None, base_pos=None):
        """Get desired pose using circular trajectory

        Args:
            t (torch.Tensor): Current time (B,)
            base_quat (torch.Tensor, optional): Base frame quaternion (B, 4)
            base_pos (torch.Tensor, optional): Base frame position (B, 3)

        Returns:
            Tuple[torch.Tensor, torch.Tensor]: Desired positions (B, 3) and orientations (B, 4)
        """
        pos = self.compute_circular_position(t)

        quat = self.adjust_yaw(
            pos,
            self.target_orientation,
            base_quat,
            base_pos
        )

        return pos, quat

    def compute_circular_position(self, t):
        """Compute position on circular path at time t

        Args:
            t (torch.Tensor): Current time (B,)

        Returns:
            torch.Tensor: Positions (B, 3)
        """
        angle = self.angular_velocity.squeeze(-1) * t
        pos_x = self.center[..., 0] + self.radius.squeeze(-1) * torch.cos(angle)
        pos_y = self.center[..., 1] + self.radius.squeeze(-1) * torch.sin(angle)
        pos_z = self.height.squeeze(-1)
        
        return torch.stack([pos_x, pos_y, pos_z], dim=-1)


def slerp(q1, q2, t):
    """Spherical Linear Interpolation (SLERP) between two quaternions."""
    # Normalize quaternions to ensure valid SLERP
    q1 = q1 / q1.norm(dim=-1, keepdim=True)
    q2 = q2 / q2.norm(dim=-1, keepdim=True)

    # Compute the cosine of the angle between the quaternions
    dot = torch.sum(q1 * q2, dim=-1, keepdim=True)

    # Clamp dot product to avoid numerical errors (it should be between -1 and 1)
    dot = torch.clamp(dot, -1.0, 1.0)

    # If the dot product is negative, negate one quaternion to take the shorter path
    q2 = torch.where(dot < 0, -q2, q2)
    dot = torch.where(dot < 0, -dot, dot)

    # Compute coefficients
    DOT_THRESHOLD = 0.9995
    linear_interp = q1 + t.unsqueeze(-1) * (q2 - q1)
    linear_interp = linear_interp / linear_interp.norm(dim=-1, keepdim=True)

    theta_0 = torch.acos(dot)  # Angle between input quaternions
    sin_theta_0 = torch.sin(theta_0)

    # Avoid division by zero
    sin_theta_0 = torch.where(sin_theta_0 < 1e-6, torch.ones_like(sin_theta_0)*1e-6, sin_theta_0)

    theta = theta_0 * t.unsqueeze(-1)
    sin_theta = torch.sin(theta)

    s0 = torch.cos(theta) - dot * sin_theta / sin_theta_0
    s1 = sin_theta / sin_theta_0

    spherical_interp = (s0 * q1) + (s1 * q2)
    return torch.where(
        dot > DOT_THRESHOLD,
        linear_interp,
        spherical_interp
    )