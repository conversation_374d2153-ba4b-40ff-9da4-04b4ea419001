
# sim_cfg.py
from moleworks_ext.rsc.sim.mole_usd_cfg import MOLE_CFG
from moleworks_ext.tasks.sim.sim_base_cfg import BaseExcavationSceneCfg, BaseSimEnvCfg, Limits, mdp, configclass, m545_camera
from moleworks_ext.common.actions.actions_cfg import InverseDynamicsActionCfg
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg

import numpy as np

@configclass
class ExcavationSceneCfg(BaseExcavationSceneCfg):
    robot = MOLE_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

@configclass
class ActionsCfg:
    joint_wheel_velocity = mdp.JointVelocityActionCfg(asset_name="robot",
        joint_names=["J_LF_WHEEL","J_LH_WHEEL","J_RF_WHEEL","J_RH_WHEEL"])
    joint_steer_velocity = mdp.JointVelocityActionCfg(asset_name="robot",
        joint_names=["J_LF_STEER","J_RF_STEER","J_LH_STEER","J_RH_STEER"])
    joint_abd_velocity = mdp.JointVelocityActionCfg(asset_name="robot",
        joint_names=["J_LF_HAA","J_RF_HAA","J_LH_HAA","J_RH_HAA"])
    joint_flex_velocity = mdp.JointVelocityActionCfg(asset_name="robot",
        joint_names=["J_LF_HFE","J_RF_HFE","J_LH_HFE","J_RH_HFE"])
    joint_mimic_velocity = mdp.JointVelocityActionCfg(asset_name="robot",
        joint_names=["J_LF_PARALLEL1","J_LF_PARALLEL2","J_RF_PARALLEL1","J_RF_PARALLEL2", ""
        "J_LH_PARALLEL1","J_LH_PARALLEL2","J_RH_PARALLEL1","J_RH_PARALLEL2"])
    cabin_velocity = mdp.JointVelocityActionCfg(asset_name="robot", joint_names=["J_TURN"])
    inv_dyn_cfg = InverseDynamicsActionCfg(
        asset_name="robot",
        joint_names=["J_BOOM","J_STICK","J_TELE","J_EE_PITCH"],
        controller_cfg=InverseDynamicsControllerCfg(
            command_type="vel",
            k_p=[0,0,0,0],
            k_d=[25,30,20,20],
            dof_limits=[[-0.5,0.5],[-0.6,0.6],[-0.4,0.4],[-0.8,0.8]],
            dof_efforts_limits=[[-2e6,2e6],[-1e6,1e6],[-1e6,1e6],[-1e6,1e6]]
        )
    )
    rotortilt_velocity = mdp.JointVelocityActionCfg(asset_name="robot", joint_names=["J_EE_ROLL","J_EE_YAW"])



@configclass
class MoleSimEnvCfg(BaseSimEnvCfg):
    scene = ExcavationSceneCfg(num_envs=1, env_spacing=15, lazy_sensor_update=True)
    actions = ActionsCfg()
    enable_cameras = False
    def __post_init__(self):
        if self.enable_cameras:
            self.scene.camera = m545_camera
        self.sim.dt = 0.04
        self.sim.render_interval = 10
        self.common_post_init()


from isaaclab.sensors import CameraCfg
import isaaclab.sim as sim_utils
from moleworks_ext import MOLEWORKS_RSC_DIR
from isaaclab.assets import RigidObjectCfg



@configclass
class ArmActionsCfg:
    # temporary class until we find how to stabilize the sim
    inv_dyn_cfg = InverseDynamicsActionCfg(
        asset_name="robot",
        joint_names=["J_BOOM","J_STICK","J_TELE","J_EE_PITCH"],
        controller_cfg=InverseDynamicsControllerCfg(
            command_type="vel",
            k_p=[0,0,0,0],
            k_d=[25,30,20,20],
            dof_limits=[[-0.5,0.5],[-0.6,0.6],[-0.4,0.4],[-0.8,0.8]],
            dof_efforts_limits=[[-2e6,2e6],[-1e6,1e6],[-1e6,1e6],[-1e6,1e6]]
        )
    )
