# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations
import isaacsim.core.utils.prims as prim_utils

import os

# import isaacsim.core.utils.nucleus as nucleus_utils
import torch

# Update the import paths to match the new structure
from moleworks_ext.common.env_cfg.general_env_cfg import m545_lidar, m545_camera, transparent_plane

from moleworks_ext.tasks.sim.sim_cfg import MoleSimEnvCfg

class M545SimCameraEnvCfg(MoleSimEnvCfg):
    def __post_init__(self):
        # post init of parent
        super().__post_init__()
        self.scene.camera = m545_camera