
# sim_ik_cfg.py
from moleworks_ext.rsc.sim.m545 import M545_CFG, M545_DOF_ARM_W_CABIN_CFG
from moleworks_ext.rsc.sim.mole_usd_cfg import MOLE_DOF_ARM_W_CABIN_MERGED_CFG
from moleworks_ext.tasks.sim.sim_base_cfg import BaseExcavationSceneCfg, BaseSimEnvCfg, configclass
from moleworks_ext.common.actions.actions_cfg import DifferentialIKWithInvDynActionCfg, InverseDynamicsActionCfg
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg
from isaaclab.controllers import DifferentialIKControllerCfg
import moleworks_ext.tasks.excavation.mdp as mdp
from moleworks_ext.common.actions.task_space_actions import DifferentialIKWithInvDynAction
from moleworks_ext.common.env_cfg.general_env_cfg import os1_lidar
from isaaclab.sensors import FrameTransformerCfg, OffsetCfg
EE_OFFSET = OffsetCfg(pos=(1.418, 0.0, 0.053), rot=(0.992567, 0.0, -0.121698, 0.0))

@configclass
class ExcavationSceneCfg(BaseExcavationSceneCfg):
    robot = MOLE_DOF_ARM_W_CABIN_MERGED_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

@configclass
class ActionsCfg:
    def __init__(self, command_type="pose"):
        self.ik_inv_id_cfg = DifferentialIKWithInvDynActionCfg(
            class_type=DifferentialIKWithInvDynAction,
            asset_name="robot",
            joint_names=["J_TURN","J_BOOM","J_STICK","J_TELE","J_EE_PITCH"],
            body_name="ROTO_BASE",
            body_offset= {
                "pos": (1.418, 0.0, 0.053),
                "rot": (0.992567, 0.0, -0.121698, 0.0)
            },
            scale=1.0,
            controller=DifferentialIKControllerCfg(command_type=command_type,use_relative_mode=True,ik_method="pinv",ik_params={"k_val":1}),
            joint_cfgs=[mdp.JointVelocityActionCfg(asset_name="robot",joint_names=["J_TURN"])],
            inv_dyn_cfg=InverseDynamicsActionCfg(
                asset_name="robot",
                joint_names=["J_BOOM","J_STICK","J_TELE","J_EE_PITCH"],
                controller_cfg=InverseDynamicsControllerCfg(
                    command_type="vel",
                    k_p=[0,0,0,0],
                    k_d=[25,30,20,20],
                    dof_limits=[[-0.5,0.5],[-0.6,0.6],[-0.4,0.4],[-0.8,0.8]],
                    dof_efforts_limits=[[-2e6,2e6],[-1e6,1e6],[-1e6,1e6],[-1e6,1e6]]
                )
            )
        )

# @configclass
# class MoleSimEnvCfg(BaseSimEnvCfg):
#     scene = ExcavationSceneCfg(num_envs=1, env_spacing=15, lazy_sensor_update=False)
#     actions: ActionsCfg = None
#     command_type: str = "pose"
#     def __post_init__(self):
#         self.actions = ActionsCfg(command_type=self.command_type)
#         self.sim.dt = 0.01
#         self.sim.use_gpu_pipeline = True
#         self.common_post_init()



@configclass
class MoleSimEnvCfg(BaseSimEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""
    # Scene settings
    scene: ExcavationSceneCfg = ExcavationSceneCfg(num_envs=1, env_spacing=15, lazy_sensor_update=False)  # 15
    # Basic settings
    actions: ActionsCfg = ActionsCfg()

    # gains PID Tuning
    k_d = [25, 30, 20, 20]
    arm_joints_names = ["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"]
    ee_body_name = "ROTO_BASE"

    enable_lidar = False

    command_type: str = "pose"

    def __post_init__(self):
        """Post initialization."""
        if self.enable_lidar:
            self.scene.lidar = os1_lidar
        
        self.actions = ActionsCfg(command_type=self.command_type)
        # general settings
        self.decimation = 1
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.01
        # self.sim.render_interval = 10
        # self.sim.physx.gpu_max_rigid_patch_count = 100000
