# sim_base_cfg.py
from __future__ import annotations
import isaacsim.core.utils.prims as prim_utils
import isaaclab.sim as sim_utils
from isaaclab.utils import configclass
from isaaclab.assets import ArticulationCfg, AssetBaseCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sensors import CameraCfg, ContactSensorCfg
from isaaclab.envs import ManagerBasedEnvCfg
from isaaclab.managers import ObservationTermCfg as ObsTerm, SceneEntityCfg, ObservationGroupCfg as ObsGroup
from isaaclab.sensors import FrameTransformerCfg, OffsetCfg
from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR
from moleworks_ext.common.env_cfg.general_env_cfg import transparent_plane, mole_default_plane, m545_camera
from moleworks_ext.common.sensors.rtx_lidar import RtxLidarCfg
from moleworks_ext.common.sensors.rtx_lidar.spawner_sensor_cfg.rtx_lidar_sensor_cfg import LidarCfg
import moleworks_ext.tasks.excavation.mdp as mdp
from moleworks_ext.rsc.sim.mole_usd_cfg import MOLE_CFG

ASSET_NAME = "robot"
EE_OFFSET = OffsetCfg(pos=(1.418, 0.0, 0.053), rot=(0.992567, 0.0, -0.121698, 0.0))

@configclass
class BaseExcavationSceneCfg(InteractiveSceneCfg):
    # scene shared config
    terrain_importer_cfg = mole_default_plane
    robot: ArticulationCfg = MOLE_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")
    camera = None
    lidar = None
    # contact_forces = ContactSensorCfg(prim_path="{ENV_REGEX_NS}/Robot/ROTO_BASE")
    frame_transformer = FrameTransformerCfg(
        prim_path="{ENV_REGEX_NS}/Robot/BASE",
        target_frames=[
            FrameTransformerCfg.FrameCfg(
                name="EE",
                prim_path="{ENV_REGEX_NS}/Robot/ROTO_BASE",  # Replace with actual EE link path if different
                offset=EE_OFFSET
            ),
        ],
    )
    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(
            intensity=750.0,
            texture_file=f"{ISAAC_NUCLEUS_DIR}/Materials/Textures/Skies/PolyHaven/kloofendal_43d_clear_puresky_4k.hdr",
        ),
    )

@configclass
class BaseObservationsCfg:
    @configclass
    class PolicyCfg(ObsGroup):
        dof_pos = ObsTerm(func=mdp.dof_pos, params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])})
        dof_vel = ObsTerm(func=mdp.dof_vel, params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])})
        def __post_init__(self):
            self.concatenate_terms = True
    policy: PolicyCfg = PolicyCfg()

@configclass
class BaseCommandsCfg:
    command = mdp.NullCommandCfg()

@configclass
class Limits:
    @configclass
    class Velocity:
        upper = [0.3,0.6,0.4,0.8]
        lower = [-0.3,-0.6,-0.4,-0.8]
    velocity = Velocity()
    @configclass
    class Position:
        upper = [0.4,2.76,1.8,2.32]
        lower = [-1.29,0.54,0.0,-0.59]
    position = Position()
    @configclass
    class CylinderForce:
        upper = [530100.0,398100.0,190800.0,235500.0]
        lower = [-379180.53,-282611.19,-105894.0,-150908.4]
    cylinder_force = CylinderForce()
    @configclass
    class CylinderVelocity:
        upper = [0.118,0.308,0.416,0.334]
        lower = [-0.132,-0.295,-0.381,-0.33]
    cylinder_velocity = CylinderVelocity()

@configclass
class BaseSimEnvCfg(ManagerBasedEnvCfg):
    observations = BaseObservationsCfg()
    commands = BaseCommandsCfg()
    limits = Limits()
    k_d = [25,30,20,20]
    arm_joints_names = ["J_BOOM","J_STICK","J_TELE","J_EE_PITCH"]
    ee_body_name = "ROTO_BASE"
    ee_frame = "ENDEFFECTOR_CONTACT"
    
    decimation = 1
    episode_length_s = 1000

    def common_post_init(self):
        # common settings
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0