# sim_arm_w_cabin_cfg.py
from moleworks_ext.rsc.sim.mole_usd_cfg import MOLE_DOF_ARM_MERGED_CFG
from moleworks_ext.tasks.sim.sim_base_cfg import BaseExcavationSceneCfg, BaseSimEnvCfg, configclass, m545_camera
from moleworks_ext.common.env_cfg.general_env_cfg import os1_lidar
from moleworks_ext.common.actions.actions_cfg import InverseDynamicsActionCfg
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg
import moleworks_ext.tasks.excavation.mdp as mdp


@configclass
class ExcavationSceneCfg(BaseExcavationSceneCfg):
    robot = MOLE_DOF_ARM_MERGED_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")


@configclass
class ActionsCfg:
    inv_dyn_cfg = InverseDynamicsActionCfg(
        asset_name="robot",
        joint_names=["<PERSON>_<PERSON><PERSON><PERSON>","<PERSON>_<PERSON><PERSON><PERSON>","<PERSON>_<PERSON><PERSON><PERSON>","J_<PERSON><PERSON>_PITCH"],
        controller_cfg=InverseDynamicsControllerCfg(
            command_type="vel",
            k_p=[0,0,0,0],
            k_d = [25, 30, 20, 20],
            dof_limits=[[-0.5,0.5],[-0.6,0.6],[-0.4,0.4],[-0.8,0.8]],
            dof_efforts_limits=[[-2e6,2e6],[-1e6,1e6],[-1e6,1e6],[-1e6,1e6]]
        )
    )


@configclass
class MoleSimEnvCfg(BaseSimEnvCfg):
    scene = ExcavationSceneCfg(num_envs=1, env_spacing=15, lazy_sensor_update=True)
    actions = ActionsCfg()
    enable_cameras = False
    enable_lidar = False

    ee_body_name = "ROTO_BASE"
    # ee_frame = "ENDEFFECTOR_CONTACT"
    # body_pitch_joint_name = "ROTO_BASE"

    def __post_init__(self):
        if self.enable_cameras:
            self.scene.camera = m545_camera
        if self.enable_lidar:
            self.scene.lidar = os1_lidar
        self.sim.dt = 0.04
        self.sim.render_interval = 1
        self.common_post_init()