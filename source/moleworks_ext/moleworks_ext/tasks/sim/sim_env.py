# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import gymnasium as gym
import builtins
import math
import numpy as np
import torch
from typing import Any, ClassVar, Dict, Sequence, Tuple

from isaacsim.core.version import get_version
from isaaclab.managers import CurriculumManager, CommandManager, EventManager


from isaaclab.envs.manager_based_env import ManagerBasedEnv
from moleworks_ext.tasks.sim.sim_cfg import MoleSimEnvCfg

from isaaclab.envs.ui import ViewportCameraController
from moleworks_ext.common.utils.m545_measurements import M545Measurements
from moleworks_ext.common.utils.limits import Limits


from isaaclab.utils.timer import Timer
from isaaclab.scene import InteractiveScene
from isaaclab.sim import SimulationContext

from isaaclab.envs.common import VecEnvStepReturn


class MoleSimEnv(ManagerBasedEnv):
    """The class for environments for excavation.

    This class inherits from :class:`ManagerBasedRLEnv` and implements the core functionality for
    excavation environments. It is designed to handle the simulation and environment states without
    reinforcement learning components.

    Note:
        For vectorized environments, it is recommended to **only** call the :meth:`reset`
        method once before the first call to :meth:`step`, i.e., after the environment is created.
        After that, the :meth:`step` function handles the reset of terminated sub-environments.
        This is because the simulator does not support resetting individual sub-environments
        in a vectorized environment.
    """

    is_vector_env: ClassVar[bool] = True
    """Whether the environment is a vectorized environment."""
    metadata: ClassVar[dict[str, Any]] = {
        "render_modes": [None, "human", "rgb_array"],
        "isaac_sim_version": get_version(),
    }
    """Metadata for the environment."""

    cfg: MoleSimEnvCfg
    """Configuration for the environment."""

    def __init__(self, cfg: MoleSimEnvCfg, render_mode: str | None = None, **kwargs):
        #--- Initialiazes measurements buffers first for the managers to know their dimension
        # Environment buffers
        # Post init now that we have access to the asset and the env
        self.m545_measurements = M545Measurements(cfg=cfg, num_joints=len(cfg.scene.robot.init_state.joint_pos), num_envs=cfg.scene.num_envs, device=cfg.sim.device, env=self)
        # obs need m545_measurements
        super().__init__(cfg)
        self.m545_asset = self.scene.articulations["robot"]
        self.m545_measurements.initialize_asset()

        # M545 specific
        # Update Static measuremetns
        self.joint_ids, self._joint_names = self.scene.articulations["robot"].find_joints(['.*'])
        self.bucket_force_com = torch.zeros(self.num_envs, 3, device=self.device)
        self.bucket_moment_com = torch.zeros(self.num_envs, 3, device=self.device)
        # non linear limits for the arm
        if self.m545_measurements.arm_joint_ids:
            self.limits = Limits(self)
        else:
            self.limits = None

    def step(self, action: torch.Tensor) -> VecEnvStepReturn:
        # Process actions
        obs, extras = super().step(action)
        # Removed RL-specific returns
        # self.last_actions = self.actions[:]
        self.m545_measurements.update_measurements()
        self.update_derived_measurements()
        return obs, extras

    def update_derived_measurements(self, env_ids=...):
        '''
            Updates measurements that are not needed in inter-decimations step but relevant 
            for rewards, curriculum, terminations and observations computations
        '''
        if env_ids == ...:
            dim0 = self.num_envs
        else:
            dim0 = len(env_ids)

        if dim0 == 0:
            return

        self.m545_measurements.update_derived_measurements(env_ids, dim0)
