from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .soil import Soil

import numpy as np
import time
import torch


class BucketState:
    def __init__(self, soil_model: Soil, cfg):
        self.SM = soil_model
        self.n_envs = self.SM.n_envs
        self.device = self.SM.device
        self.cfg = cfg.bucket

        """
        constants
        """
        # cylinder radius
        self.r = torch.tensor(self.cfg.r, device=self.device)
        # bucket width
        self.b = torch.tensor(self.cfg.b, device=self.device)
        # bottom plate length
        self.a = torch.tensor(self.cfg.a, device=self.device)
        self.a_discretized = torch.arange(0, self.cfg.a + 1e-5, self.cfg.da, device=self.device)
        self.a_discretized_expanded = self.a_discretized.expand(self.n_envs, -1)
        self.max_fill_area = np.pi / 2.0 * torch.square(self.r) + self.a * self.r
        self.max_fill_area_expanded = (np.pi / 2.0 * torch.square(self.r) + self.a * self.r) * torch.ones(
            self.n_envs, 1, device=self.device
        )
        self.zero_vec = torch.zeros(self.n_envs, 1, device=self.device)
        # angle between bottom plate and comlpetely full -top, i.e., max ssp angle
        self.alpha_max = torch.atan2(2.0 * self.r, self.a)

        self.top_width = torch.tensor(self.cfg.edge_top_width, device=self.device)
        self.half_angle = torch.tensor(self.cfg.edge_half_angle, device=self.device)

        # constant COM offset from bucket edge in the bucket frame, x pointing aloing bucket plate, z upwards
        # COM_pos_edge = ee_contact_pos - COM_pos
        # self.COM_pos_edge = torch.tensor([0.527271, 0.75594], device=self.device)
        # self.COM_angle_bp = torch.atan2(self.COM_pos_edge[1], self.COM_pos_edge[0])
        # self.COM_pos_edge_length = torch.linalg.norm(self.COM_pos_edge)

        """
        bucket internal variable buffers
        """
        # is bottom plate in soil?
        self.bp_in_soil = torch.zeros(self.n_envs, 1, device=self.device, dtype=torch.bool)
        # bottom plate length in soil
        self.bp_soil = torch.zeros(self.n_envs, 1, device=self.device)

        self.bp_angle_to_horizon = torch.zeros(self.n_envs, 1, device=self.device)
        self.full_angle_to_horizon = torch.zeros(self.n_envs, 1, device=self.device)

        self.fill_area = torch.zeros(self.n_envs, 1, device=self.device)
        self.swept_area = torch.zeros(self.n_envs, 1, device=self.device)
        self.fill_ratio = torch.zeros(self.n_envs, 1, device=self.device)

        # vertical depth below soil (always positive)
        self.depth = torch.zeros(self.n_envs, 1, device=self.device)
        self.clipped_depth = torch.zeros(self.n_envs, 1, device=self.device)
        self.prev_depth = torch.zeros(self.n_envs, 1, device=self.device)
        self.clipped_prev_depth = torch.zeros(self.n_envs, 1, device=self.device)
        self.clipped_average_depth_bp = torch.zeros(self.n_envs, 1, device=self.device)

        # dot productVelW . bottomPlateVectorW
        self.vel_dot = torch.zeros(self.n_envs, 1, device=self.device)
        # a*b = |a|*|b|*cos(alpha)
        self.vel_cos = torch.zeros(self.n_envs, 1, device=self.device)

    def update(self, idxs=...):
        self._update_geometry()
        # self._update_COM()
        self._update_bp_in_soil()
        self._update_filling(idxs)

    def set_fill_state(self, filling, env_ids=..., is_ratio=True):
        """sets fill ratio (or fill area directly) and swept area
        - clips to max fill volume
        - self.update() continues integrating the fill area, doe snot
        Args:
        filling (n_envs, filling): either area or ratio [0-1]

        """
        if env_ids == ...:
            dim0 = self.num_envs
        else:
            dim0 = len(env_ids)

        if dim0 == 0:
            return

        filling = torch.clip(
            filling,
            torch.zeros(1, device=self.device),
            torch.ones(1, device=self.device),
        )
        if is_ratio:
            self.fill_ratio[env_ids] = filling
            self.fill_area[env_ids] = filling * self.max_fill_area
            self.swept_area[env_ids] = filling * self.max_fill_area
        else:
            self.fill_area[env_ids] = torch.clip(filling, torch.zeros(1, device=self.device), self.max_fill_area)
            self.swept_area[env_ids] = self.fill_area.clone()[env_ids]
            self.fill_ratio[env_ids] = self.fill_area[env_ids] / self.max_fill_area[env_ids]

    # def _update_COM(self):
    #     COM_ang_w = self.bp_angle_to_horizon + self.COM_angle_bp

    #     self.COM_pos_w_x = torch.cos(COM_ang_w) * self.COM_pos_edge_length + self.SM.bucket_pos_w[:, 0].view(-1, 1)
    #     self.COM_pos_w_z = torch.sin(COM_ang_w) * self.COM_pos_edge_length + self.SM.bucket_pos_w[:, 1].view(-1, 1)
    #     self.COM_pos_w = torch.cat((self.COM_pos_w_x, self.COM_pos_w_z), dim=-1)

    def _update_geometry(self):
        self.bp_angle_to_horizon[:] = torch.atan2(
            self.SM.bp_unit_vector_w[:, 1],
            self.SM.bp_unit_vector_w[:, 0],
        ).view(-1, 1)

        self.back_pos_w = self.SM.bucket_pos_w + self.SM.bp_unit_vector_w * (self.a + self.r)

        # no need to wrap because we are around 0
        self.full_angle_to_horizon[:] = (self.bp_angle_to_horizon + self.alpha_max).view(-1, 1)

        # negative depth = outside soil
        self.depth[:] = self.SM.soil_height.get_height(self.SM.bucket_pos_w[:, 0].view(-1, 1)) - self.SM.bucket_pos_w[
            :, 1
        ].view(-1, 1)
        self.clipped_depth[:] = torch.where(self.depth < 0.0, self.zero_vec, self.depth)

        self.prev_depth[:] = self.SM.soil_height.get_height(
            self.SM.prev_bucket_pos_w[:, 0].view(-1, 1)
        ) - self.SM.prev_bucket_pos_w[:, 1].view(-1, 1)

        self.clipped_prev_depth[:] = torch.where(self.prev_depth < 0.0, self.zero_vec, self.prev_depth)

        # a*b = |a||b|cos a
        # dot < 0 -> moving backwards
        # projectio of a onto b, what portion of a points in the direction of b
        self.vel_dot[:] = torch.sum(self.SM.bucket_vel_w * (-self.SM.bp_unit_vector_w), dim=-1, keepdim=True)
        self.vel_cos[:] = self.vel_dot / (
            self.SM.bp_unit_vector_w.norm(dim=-1, keepdim=True) * self.SM.bucket_vel_w.norm(dim=-1, keepdim=True) + 1e-5
        )

    def _update_filling(self, idxs=...):
        # depth assumed to be always positive
        # digging direction in -x direction
        # moving in +x will empty the shovel (no negative soil volume)
        dx = (self.SM.bucket_pos_w[idxs, 0] - self.SM.prev_bucket_pos_w[idxs, 0]).view(-1, 1)

        self.swept_area[idxs] += -dx * (self.clipped_depth[idxs] + self.clipped_prev_depth[idxs]) / 2.0

        # losing all soil if not in soil and pointing downwards
        # angle < 0: closed
        # angle > 0: open
        # depth < 0: out of soil
        losing_soil = torch.logical_and(
            self.bp_angle_to_horizon > 0.0, self.depth < -self.SM.env.curriculum_excavation.curr_spilling_depth_margin
        )
        self.swept_area[losing_soil] = 0.0
        # clip swept area, cannot overfill
        self.fill_area[idxs] = torch.clip(self.swept_area[idxs], self.zero_vec[idxs], self.max_fill_area_expanded[idxs])
        self.fill_ratio[idxs] = self.fill_area[idxs] / self.max_fill_area

    def _update_bp_in_soil(self):
        """computes the length of the bottom plate inside the soil
        - bottom plate is discretized
        - find point on bottom plate that is just HIGHER (or equal) the soil
        - if tip is not in soil a_soil == 0
            - not looking at back
            - can be above soil only in the middle due to uneven soil

        - compute world positions of bottom plate discretized points
        - compute the soil heights at the xy locations of the bottom plate
        - get the soil height at the xy locations and compare it the bottom plate height
        - get the average depth of the part of the shovel that is inside the soil
            - used for soil pressure at bottom plate center
            - cumsum of soil_height - cumsum of bottom plate height at index

        - bottom_plate_unit_vector points from tip towards back
        """

        # n_envs, num_da, 2
        # broadcasting magic, last dimensions must match or one of them has to be 1
        da_pos_w = self.SM.bucket_pos_w.view(self.n_envs, 1, 2) + self.a_discretized.view(
            -1, 1
        ) * self.SM.bp_unit_vector_w.view(self.n_envs, 1, 2)

        da_heights_w = da_pos_w[..., 1]  # n_envs, num_da
        da_soil_heights_w = self.SM.soil_height.get_height(da_pos_w[..., 0])

        # find which da_height is larger than soil height
        # if idx is 0: already tip is not in soil
        # corner case: tip exactly at soil height: considered outside soil
        max_val, max_idxs = torch.max(da_heights_w >= da_soil_heights_w, dim=1, keepdim=True)
        # corner cases
        # bottom plate completely in soil..? old model-> just clipped -> ok
        #   max_val = false (always smaller than soil) returned index is 0 (just first max value)
        # bottom plate outside
        #   max_val = true  and index = 0
        max_idxs = torch.where(max_val == False, self.a_discretized.shape[0] - 1, max_idxs)  # -1 does not work

        self.bp_in_soil[:] = max_idxs != 0
        self.bp_soil[:] = torch.gather(self.a_discretized_expanded, 1, max_idxs)

        # depth =  cumsum soilheight-plateheight at index / (index+1)
        da_heights_w_cumsum = torch.cumsum(da_heights_w, dim=-1)
        da_soil_heights_w_cumsum = torch.cumsum(da_soil_heights_w, dim=-1)

        depth_cumsum = da_soil_heights_w_cumsum - da_heights_w_cumsum
        self.clipped_average_depth_bp[:] = torch.gather(depth_cumsum, 1, max_idxs) / (max_idxs + 1)
        # set negative depths to 0 (if not in soil at all)
        self.clipped_average_depth_bp[torch.logical_not(self.bp_in_soil)] = 0
