from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .soil import Soil

import numpy as np
import time
import torch


class SoilForces:
    def __init__(self, soil_model: Soil, cfg):
        self.SM = soil_model
        self.SP = soil_model.soil_parameters
        self.SSP = soil_model.ssp
        self.bucket = soil_model.bucket_state
        self.n_envs = self.SM.n_envs
        self.device = self.SM.device
        self.cfg = cfg.soil_forces

        """
        INFO
        - fee only valid if SSP.beta in [0,pi]
            - SSP needs to exit soil!! otherwise fee forces are wrong/nonsense
        """

        # 3d tensors for easier handling in env
        self.Rs_unit_vector_w = torch.zeros(self.n_envs, 3, device=self.device)
        self.RM_w = torch.zeros(self.n_envs, 3, device=self.device)
        self.ones_vec = torch.ones(self.n_envs, 1, device=self.device)
        self.zeros_vec = torch.zeros(self.n_envs, 1, device=self.device)
        self.RF_w = torch.zeros(self.n_envs, 3, device=self.device)

    def update(self):
        self._update_fee()
        self._update_penetration()
        self._update_deadload()
        self._compute_resultant_COM()

    def _update_fee(self):
        # "unit test", compared to np implementation
        # self.SSP.beta[:] = np.pi / 2
        # self.SSP.alpha[:] = np.pi / 6
        # self.bucket.alpha_max = torch.tensor([0.96], device=self.device)
        # self.SP.alpha[:] = 0.3
        # d = 0.3
        # self.SSP.L[:] = d / torch.sin(self.SSP.beta)
        # self.SP.K0[:] = 0.5

        # soil failure angle
        self.rho = (np.pi / 4.0 - (self.SP.phi + self.SP.delta) / 2.0) + (np.pi / 4.0 - self.SSP.beta / 2.0)
        # soil wedge surface in contact with shovel
        self.ssp_alpha_bar = self.bucket.alpha_max - self.SSP.alpha
        self.k = np.pi - self.SSP.beta - self.ssp_alpha_bar
        self.AX = torch.sin(self.ssp_alpha_bar) / torch.sin(self.k) * self.SSP.L
        self.ABX = 0.5 * torch.sin(self.SSP.beta) * self.AX * self.SSP.L

        # alternative self.A2 computation (gives the same)
        # i = np.pi - k
        # j = np.pi - i - self.rho
        # BC = torch.sin(self.SSP.beta) / torch.sin(self.rho) * self.SSP.L
        # self.A2_2 = 0.5 * self.SSP.L * BC * torch.sin(self.ssp_alpha_bar + j)
        self.A2 = (
            0.5
            * self.SSP.L
            * self.SSP.L
            * torch.sin(self.SSP.beta)
            * (torch.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / torch.tan(self.rho))
        )
        self.BCX = self.A2 - self.ABX

        # set self.c_start & self.phi_star to 1 if not in soil to avoid nan, R is 0 if L is not in soil (L=0)
        self.c_star = torch.where(
            self.A2 > 1e-5,
            (self.SP.ca * self.ABX + self.SP.c * self.BCX) / self.A2,
            self.ones_vec,
        )
        self.phi_star = torch.where(
            self.A2 > 1e-5,
            (self.SP.delta * self.ABX + self.SP.phi * self.BCX) / self.A2,
            self.ones_vec,
        )

        # self.z1 = (torch.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / torch.tan(self.rho)) * torch.sin(self.SP.alpha)
        # self.z2 = torch.cos(self.SSP.beta - self.SP.alpha) + torch.sin(self.SSP.beta - self.SP.alpha) / torch.tan(
        #     self.SP.alpha + self.rho
        # )

        # self.z3_1 = torch.sin(self.SP.alpha) * (
        #     torch.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / torch.tan(self.rho)
        # )
        # self.z3_2 = torch.sin(self.SSP.beta - self.SP.alpha)

        # self.z3 = self.z3_1 * self.z3_1 - self.z3_2 * self.z3_2
        # self.z4 = torch.sin(self.SSP.beta) * (torch.cos(self.SSP.beta) + torch.sin(self.SSP.beta) / torch.tan(self.rho))
        # # if ssp.beta is 0, z4 is 0 and z is nan -> if in soil -> invalid soil model -> if not in soil -> ok, fee & pen == 0
        # self.z = self.SSP.L * (self.z1 - 1.0 / 3.0 * self.z2 * self.z3 / self.z4)

        # # degenerated case if beta - alpha = 0
        # self.z = torch.where(
        #     (self.SSP.beta - self.SP.alpha) == 0.0,
        #     -1.0 / 3.0 * self.SSP.L * torch.sin(self.SSP.beta + self.rho),
        #     self.z,
        # )

        self.L2 = torch.sin(self.rho + self.SSP.beta) / torch.sin(self.rho) * self.SSP.L
        self.ha = torch.sin(self.SP.alpha) * self.L2

        self.L3 = torch.sin(self.SSP.beta) / torch.sin(self.rho) * self.SSP.L
        self.hc = torch.sin(self.SP.alpha + self.rho) * self.L3
        self.z = (self.ha + self.hc) / 3.0

        self.ADF = self.SP.ca * self.bucket.b * self.SSP.L
        self.W = self.SP.gamma * self.bucket.b * self.A2
        self.CF1 = self.SP.c * self.bucket.b * self.SSP.L * torch.sin(self.SSP.beta) / torch.sin(self.rho)
        self.ACF = self.c_star * self.A2
        self.SF2 = self.SP.K0 * self.SP.gamma * self.z * torch.tan(self.phi_star) * self.A2

        self.Rs1 = -self.ADF * torch.cos(self.SSP.beta + self.rho + self.SP.phi)
        self.Rs2 = self.W * torch.sin(self.SP.alpha + self.rho + self.SP.phi)
        self.Rs3 = self.CF1 * torch.cos(self.SP.phi)
        self.Rs4 = 2.0 * self.ACF * torch.cos(self.SP.phi)
        self.Rs5 = 2.0 * self.SF2 * torch.cos(self.SP.phi)
        self.Rs6 = torch.sin(self.SSP.beta + self.rho + self.SP.delta + self.SP.phi)

        self.Rs = (self.Rs1 + self.Rs2 + self.Rs3 + self.Rs4 + self.Rs5) / self.Rs6
        # set Rs to 0 if not in soil -> should get rid of nan/inf
        self.Rs[self.bucket.depth <= 0.0] = 0.0
        self.Rs *= self.cfg.fee_multiplyer

        # direction of force to be applied to the shovel
        fee_force_angle_w = -(np.pi / 2.0 + self.SP.alpha - self.SSP.beta - self.SP.delta)
        # this is the same
        # fee_force_angle_w = -(np.pi / 2.0 - self.SSP.alpha - self.bucket.bp_angle_to_horizon - self.SP.delta)

        self.Rs_unit_vector_w[:, 0] = torch.cos(fee_force_angle_w).view(-1)
        self.Rs_unit_vector_w[:, 2] = torch.sin(fee_force_angle_w).view(-1)

    def _update_penetration(self):
        # plate
        self.p0_2 = (
            0.5
            * self.SP.gamma
            * self.bucket.clipped_average_depth_bp
            * ((1.0 + self.SP.K0) + (1.0 - self.SP.K0) * torch.cos(2.0 * self.bucket.bp_angle_to_horizon))
        )
        self.plate_Rs = (self.SP.ca + self.p0_2 * torch.tan(self.SP.delta)) * self.bucket.bp_soil * self.bucket.b
        # no need to set to 0 if not in soil, pressure is 0, hence force is 0
        self.plate_Rs *= self.cfg.penetration_plate_multiplyer

        # tip
        # using the clipped depth, because we want 0 force if not in soil
        self.p0 = (
            0.5
            * self.SP.gamma
            * self.bucket.clipped_depth
            * ((1.0 + self.SP.K0) + (1.0 - self.SP.K0) * torch.cos(2.0 * self.bucket.bp_angle_to_horizon))
        )
        self.pe = self.p0 * self.SP.CP
        self.edge_Rs = (
            (self.pe + (self.SP.ca + self.pe * torch.tan(self.SP.delta)) / torch.tan(self.bucket.half_angle))
            * self.bucket.b
            * self.bucket.top_width
        )

        # 0 if moving perpendicular, 1 if moving parallel
        self.edge_Rs *= self.bucket.vel_cos
        # if moving backwards, no penetration
        self.edge_Rs[self.bucket.vel_cos < 0.0] = 0.0
        # set to 0 if not in soil (edge_Rs is not 0 if pressure is 0 (=not in soil))
        self.edge_Rs[self.bucket.depth <= 0.0] = 0.0
        self.edge_Rs *= self.cfg.penetration_edge_multiplyer

    def _update_deadload(self):
        self.deadload_F = self.SP.gamma * self.SSP.B * self.bucket.b
        self.deadload_F *= self.cfg.deadload_multiplyer

    def _compute_resultant_COM(self):
        """
        Compute resultant force and moment at the COM of the bucket
        """
        # force
        Rs_vec = self.Rs * self.Rs_unit_vector_w  # 3D
        penF_vec = (self.edge_Rs + self.plate_Rs) * self.SM.bp_unit_vector_w_3d
        deadloadF_vec = self.deadload_F * torch.tensor([0, 0, -1], device=self.device)
        self.RF_w = Rs_vec + penF_vec + deadloadF_vec

        # moment (pos_F_w - pos_COM_w) x F -> only around y axis, since soil model is in x,z
        Rs_pos_w = self.SM.bucket_pos_w + self.SSP.ssp_unit_vector_w * self.SSP.L / 2.0
        penF_pos_w = self.SM.bucket_pos_w
        deadloadF_pos_w = self.SSP.centroid_pos_w

        Rs_diff_pos = Rs_pos_w - self.SM.bucket_com_pos_w
        penF_diff_pos = penF_pos_w - self.SM.bucket_com_pos_w
        deadload_diff_pos = deadloadF_pos_w - self.SM.bucket_com_pos_w
        M_Rs_y = (Rs_diff_pos[:, 1] * Rs_vec[:, 0] - Rs_diff_pos[:, 0] * Rs_vec[:, 1]).unsqueeze(-1)
        M_pen_y = (penF_diff_pos[:, 1] * penF_vec[:, 0] - penF_diff_pos[:, 0] * penF_vec[:, 1]).unsqueeze(-1)
        M_deadload_y = (
            deadload_diff_pos[:, 1] * deadloadF_vec[:, 0] - deadload_diff_pos[:, 0] * deadloadF_vec[:, 1]
        ).unsqueeze(-1)
        self.RM_w[:, 1] = (M_Rs_y + M_pen_y + M_deadload_y).view(-1)
