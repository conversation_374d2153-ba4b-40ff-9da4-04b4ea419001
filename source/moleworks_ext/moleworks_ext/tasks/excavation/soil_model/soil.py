from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from moleworks_ext.tasks.excavation.excavation_env_3d import ExcavationEnv

# debugging
# import matplotlib.pyplot as plt # Removed as it's no longer used
import numpy as np

# import time # Removed as it's no longer used
import torch

from ..soil_model_base import SoilModelBase
from .bucket_state import BucketState
from .soil_forces import SoilForces
from .soil_height_rbf import SoilHeightRBF
from .soil_parameters import SoilParameters
from .ssp import SSP


class Soil(SoilModelBase):
    """
    environment (only interacts with soil model class:sample soil params, set bucket fill volume, reset, fixed soil params)
            |
            Soil model (contains alls model elements)
            ----------
    soil_height | bucket_state | ssp | soil_forces | soil_parameters
    """

    def __init__(self, cfg):
        self.n_envs = cfg.scene.num_envs
        self.device = cfg.sim.device

        # measurement buffers, updated with data from env
        # measurements in the arm plane (x-z), in a frame that is gravity aligned
        # attached to boom joint
        self.bucket_pos_w = torch.zeros(self.n_envs, 2, device=self.device)
        self.prev_bucket_pos_w = torch.zeros(self.n_envs, 2, device=self.device)
        self.bucket_com_pos_w = torch.zeros(self.n_envs, 2, device=self.device)
        # vector from tip towards bucket
        self.bp_unit_vector_w_3d = torch.zeros(self.n_envs, 3, device=self.device)
        self.bp_unit_vector_w = torch.zeros(self.n_envs, 2, device=self.device)
        self.bucket_vel_w = torch.zeros(self.n_envs, 2, device=self.device)

        # soil model elements
        self.soil_height = SoilHeightRBF(
            cfg, cfg_name="soil_height", num_envs=self.n_envs, device=self.device
        )
        self.max_depth_height = SoilHeightRBF(
            cfg,
            device=self.device,
            num_envs=self.n_envs,
            upper_limit=self.soil_height,
        )
        self.bucket_state = BucketState(self, cfg)
        self.ssp = SSP(self, cfg)
        self.soil_parameters = SoilParameters(self, cfg)
        self.forces = SoilForces(self, cfg)
        self.xz_idx = torch.tensor([0, 2], device=self.device)
        self.false_vec = torch.zeros(
            self.n_envs, 1, dtype=torch.bool, device=self.device
        )

    def post_init(self, env):
        self.env = env
        self.asset = env.scene.articulations["robot"]

    def update(self):
        self.update_1()
        self.update_2()

    def update_1(self, idxs=...):
        self._update_measurements()
        self.bucket_state.update(idxs)

    def update_2(self):
        self.ssp.update()
        self.soil_parameters.update()  # get updated soil angle
        self.forces.update()

    def reset(self, idxs=...):
        self.bucket_state.fill_area[idxs] = 0.0
        self.bucket_state.swept_area[idxs] = 0.0
        self.bucket_state.fill_ratio[idxs] = 0.0

    def sample(self, idxs=...):
        self.soil_height.sample(idxs=idxs)
        self.max_depth_height.sample(idxs=idxs)
        self.soil_parameters.sample(idxs=idxs)

    def is_state_invalid(self, idxs=...):
        # if not in soil, ssp == 0 (ssp is only the part inside the soil)
        too_flat_ssp = self.get_ssp_L()[idxs] > self.get_ssp_L_max_no_bucket()[idxs]
        too_long_ssp = self.get_ssp_L()[idxs] > self.get_ssp_L_max()[idxs]

        # this can be true also if not in soil -> avoid!!
        invalid_ssp_beta = torch.logical_or(
            self.get_ssp_ang_to_soil()[idxs] <= 0.0,
            self.get_ssp_ang_to_soil()[idxs] > np.pi,
        )
        # invalid soil failure angle
        invalid_ssp_rho = self.get_soil_failure_ang()[idxs] <= 0.0
        in_soil = self.bucket_state.depth[idxs] > 0.0
        # ssp state cannot be invalid if not in soil!

        return torch.where(
            in_soil,
            (too_flat_ssp | too_long_ssp | invalid_ssp_beta | invalid_ssp_rho),
            self.false_vec[idxs],
        )

    """
    set soil model properties (env only interacts with soil, not its subclasses)
    """

    def set_bucket_fill_state(self, state, env_ids=None, is_ratio=True):
        self.bucket_state.set_fill_state(state, env_ids, is_ratio)

    """
    get soil model properties (env only interacts with soil, not its subclasses)
    """

    def get_ssp_ang_to_soil(self):
        return self.ssp.beta

    def get_ssp_L(self):
        return self.ssp.L

    def get_ssp_L_max_no_bucket(self):
        return self.ssp.L_max_no_bucket

    def get_ssp_L_max(self):
        return self.ssp.L_max

    # forces
    def get_resultant_force(self):
        return self.forces.RF_w

    def get_rs(self):
        return self.forces.Rs

    def get_edge_rs(self):
        return self.forces.edge_Rs

    def get_plate_rs(self):
        return self.forces.plate_Rs

    def get_deadload(self):
        return self.forces.deadload_F

    def get_resultant_moment(self):
        return self.forces.RM_w

    def get_soil_failure_ang(self):
        return self.forces.rho

    # bucket state
    def get_bucket_depth(self):
        # negative depth = outside soil
        return self.bucket_state.depth

    def get_bucket_com_pos_w(self):
        return self.bucket_state.COM_pos_w

    def get_fill_ratio(self):
        return self.bucket_state.fill_ratio

    def get_max_fill_area(self):
        return self.bucket_state.max_fill_area

    def get_fill_area(self):
        return self.bucket_state.fill_area

    def get_swept_area(self):
        return self.bucket_state.swept_area

    def get_bucket_vel_cos(self):
        return self.bucket_state.vel_cos

    # soil height
    def get_soil_height(self):
        return self.soil_height.x, self.soil_height.z

    def get_min_max_soil_height(self, idx=...):
        min_val, _ = torch.min(self.soil_height.z[idx], dim=-1)
        max_val, _ = torch.max(self.soil_height.z[idx], dim=-1)
        return min_val, max_val

    def get_soil_height_at_pos(self, x_pos, env_ids=...):
        return self.soil_height.get_height(x_pos, env_ids)

    def get_soil_angle_at_pos(self, x_pos, env_ids=...):
        return self.soil_height.get_angle_to_world(x_pos, env_ids)

    def get_max_depth_height(self):
        return self.max_depth_height.x, self.max_depth_height.z

    def get_max_depth_height_at_pos(self, x_pos, env_ids=...):
        return self.max_depth_height.get_height(x_pos, env_ids)

    # bucket state
    def get_bucket_full_angle_w(self):
        return self.bucket_state.full_angle_to_horizon

    def get_bucket_bp_angle_w(self):
        return self.bucket_state.bp_angle_to_horizon

    # soil params
    def get_soil_params(self):
        return self.soil_parameters.params

    def get_n_soil_params_to_sample(self):
        return self.soil_parameters.n_params_to_sample

    # collects necessary measurements from env
    # only this method uses access to env!
    def _update_measurements(self):
        """fetch measurements from env
        - this is all we need from the env
        - the rest is computed internally in the soil model (and subclasses)
        """
        self.bucket_com_pos_w[:] = self.env.m545_measurements.bucket_com_pos_w[
            :, self.xz_idx
        ]
        self.bucket_pos_w[:] = self.env.m545_measurements.bucket_pos_w[:, self.xz_idx]
        self.prev_bucket_pos_w[:] = self.env.m545_measurements.prev_bucket_pos_w[
            :, self.xz_idx
        ]
        self.bp_unit_vector_w_3d[:] = (
            self.env.m545_measurements.bp_unit_vector_w
        )  # needed for 3d force computation
        self.bp_unit_vector_w[:] = self.env.m545_measurements.bp_unit_vector_w[
            :, self.xz_idx
        ]
        self.bucket_vel_w[:] = self.env.m545_measurements.bucket_vel_w[:, self.xz_idx]

    # def plot_state(self, idx, show=True, label=True, ax=None):
    #     Ry = lambda a: np.array([[np.cos(a), -np.sin(a)], [np.sin(a), np.cos(a)]])  # rot mat 2d
    #     if ax is None:
    #         ax = plt.gca()
    #
    #     for i in idx:
    #         # soil height
    #         ax.plot(
    #             self.soil_height.x.cpu(),
    #             self.soil_height.z[i].cpu(),
    #             label="soil env nr.  " + str(i) if label else "",
    #         )
    #         ax.plot(
    #             self.soil_height.x.cpu(),
    #             self.soil_height.z[i].cpu() + self.env.curriculum_excavation.curr_spilling_depth_margin.cpu(),
    #             label="spilling depht margin" if label else "",
    #             linestyle="dotted",
    #             linewidth=0.5,
    #         )
    #         # max_depth
    #         ax.plot(
    #             self.max_depth_height.x.cpu(),
    #             self.max_depth_height.z[i].cpu(),
    #             label="max depth" if label else "",
    #         )
    #         # BUCKET
    #         # bottom plate
    #         tip_x = self.bucket_pos_w[i, 0].item()
    #         tip_z = self.bucket_pos_w[i, 1].item()
    #         end_x = (self.bp_unit_vector_w[i, 0] * self.bucket_state.a).item() + tip_x
    #         end_z = (self.bp_unit_vector_w[i, 1] * self.bucket_state.a).item() + tip_z
    #         ax.plot([tip_x, end_x], [tip_z, end_z], color="k", marker="x", markevery=[0], linewidth=1.0)
    #         # back plate
    #         end_x = (-self.bucket_state.back_unit_vector_w[i, 0] * self.bucket_state.h).item() + self.bucket_state.back_pos_w[i, 0].item() # back_pos - back_vec*h
    #         end_z = (-self.bucket_state.back_unit_vector_w[i, 1] * self.bucket_state.h).item() + self.bucket_state.back_pos_w[i, 1].item()
    #         ax.plot([self.bucket_state.back_pos_w[i, 0].item(), end_x], [self.bucket_state.back_pos_w[i, 1].item(), end_z], color="k", linewidth=1.0)
    #         # top plate
    #         ax.plot([tip_x + (-self.bucket_state.back_unit_vector_w[i, 0] * self.bucket_state.h).item(), end_x], [tip_z + (-self.bucket_state.back_unit_vector_w[i, 1] * self.bucket_state.h).item(), end_z], color="k", linewidth=1.0)
    #
    #         # bucket part in soil
    #         end_x = (self.bp_unit_vector_w[i, 0] * self.bucket_state.bp_soil[i]).item() + tip_x
    #         end_z = (self.bp_unit_vector_w[i, 1] * self.bucket_state.bp_soil[i]).item() + tip_z
    #         ax.plot([tip_x, end_x], [tip_z, end_z], color="r", linewidth=2.5)
    #
    #         # SSP
    #         ssp_end_x = (self.ssp.ssp_unit_vector_w[i, 0] * self.ssp.L_max[i]).item() + tip_x
    #         ssp_end_z = (self.ssp.ssp_unit_vector_w[i, 1] * self.ssp.L_max[i]).item() + tip_z
    #         ax.plot([tip_x, ssp_end_x], [tip_z, ssp_end_z], color="g", linestyle="--", linewidth=1.0)
    #         ssp_end_x = (self.ssp.ssp_unit_vector_w[i, 0] * self.ssp.L[i]).item() + tip_x
    #         ssp_end_z = (self.ssp.ssp_unit_vector_w[i, 1] * self.ssp.L[i]).item() + tip_z
    #         ax.plot([tip_x, ssp_end_x], [tip_z, ssp_end_z], color="g", marker="x", linewidth=1.5)
    #
    #         # centroid
    #         ax.scatter(self.ssp.centroid_w_x[i].item(), self.ssp.centroid_w_z[i].item(), marker="x", s=50)
    #         # com
    #         ax.scatter(self.bucket_com_pos_w[i, 0].item(), self.bucket_com_pos_w[i, 1].item(), marker="p", s=50)
    #         # FEE force direction
    #         ax.plot(
    #             [self.ssp.centroid_w_x[i].item() - self.forces.Rs_unit_vector_w[i, 0].item() * 0.5, self.ssp.centroid_w_x[i].item()],
    #             [self.ssp.centroid_w_z[i].item() - self.forces.Rs_unit_vector_w[i, 2].item() * 0.5, self.ssp.centroid_w_z[i].item()],
    #             color="b",
    #             linewidth=1.5,
    #             marker="<",
    #             markevery=[0],
    #         )
    #
    #         # soil failure wedge
    #         ssp_vec = self.ssp.ssp_unit_vector_w[i, [0,1]].cpu().numpy()
    #         angle_to_rotate_fail = np.pi - self.forces.rho[i].item() - self.ssp.beta[i].item()
    #         rot_mat_fail = Ry(angle_to_rotate_fail)
    #         rotated_ssp_fail = (rot_mat_fail @ ssp_vec).T
    #
    #         L_fail = self.ssp.L[i] * torch.sin(self.ssp.beta[i]) / torch.sin(self.forces.rho[i])
    #         wedge_top_x = tip_x + (rotated_ssp_fail[0] * L_fail).item()
    #         wedge_top_z = tip_z + (rotated_ssp_fail[1] * L_fail).item()
    #
    #         ax.plot([tip_x, wedge_top_x], [tip_z, wedge_top_z], color="C1", linestyle="-", linewidth=1.0)
    #
    #         angle_to_rotate_terrain = -self.ssp.beta[i].item()
    #         rot_mat_terrain = Ry(angle_to_rotate_terrain)
    #         rotated_neg_ssp = (rot_mat_terrain @ -ssp_vec).T
    #
    #         L_terrain = self.ssp.L[i] * (torch.cos(self.ssp.beta[i]) + torch.sin(self.ssp.beta[i]) / torch.tan(self.forces.rho[i]))
    #         lin_terrain_x = self.ssp.L[i].item() * self.ssp.ssp_unit_vector_w[i,0].item() + (rotated_neg_ssp[0] * L_terrain).item() + tip_x
    #         lin_terrain_z = self.ssp.L[i].item() * self.ssp.ssp_unit_vector_w[i,1].item() + (rotated_neg_ssp[1] * L_terrain).item() + tip_z
    #         ax.plot([ssp_end_x, lin_terrain_x], [ssp_end_z, lin_terrain_z], color="C1", linestyle="-", linewidth=1.0)
    #
    #         # wedge centroid depth
    #         centroid_depth_z = wedge_top_z - self.forces.z[i].item()
    #         ax.plot([wedge_top_x - 0.2, wedge_top_x + 0.2], [centroid_depth_z, centroid_depth_z], label="wedge centroid z", linestyle=":", linewidth=1.0)
    #         ax.plot([wedge_top_x, wedge_top_x], [wedge_top_z, centroid_depth_z], linestyle=":", linewidth=1.0)
    #
    #
    #
    #     ax.set_xlabel("x world coordinate [m]")
    #     ax.set_ylabel("z world coordinate [m]")
    #     ax.set_title("Soil Model State")
    #     ax.grid(True)
    #     ax.axis("equal")
    #     if label:
    #         ax.legend(fontsize="xx-small")
    #
    #     if show:
    #         plt.show(block=False)
    #         # Keep plot open until user interaction if blocking is desired
    #         # plt.show(block=True)
    #         try:
    #             input("Press [Enter] in the terminal to close the plot...")
    #             plt.close()
    #         except EOFError: # handle non-interactive environments
    #             plt.close()
