import torch
from typing import TYPE_CHECKING


def action_rate_l2_excavation(env) -> torch.Tensor:
    """Penalize the rate of change of the actions using L2-kernel."""
    return torch.sum(torch.square(env.actions - env.last_actions), dim=1)

def action_penalty(env) -> torch.Tensor:
    """Penalize the size of the actions using L2-kernel """
    return torch.sum(torch.square(env.actions), dim=1)



def const_reward(env):
    return torch.ones(env.num_envs, device=env.device)


def reward_bucket_velocity_penalty(env) -> torch.Tensor:
    """Penalize bucket velocities that exceed the maximum allowed velocity.
    
    The penalty scales linearly by default, with an optional exponential factor
    to increase the penalty severity for higher violations.
    """
    excess_vel = torch.nn.functional.relu(
        env.m545_measurements.bucket_vel_norm - env.cfg.terminations_excavation.max_bucket_vel
    )
    # Use linear scaling with optional exponential factor (e.g., 1.5) for more aggressive penalties
    penalty = -excess_vel * torch.pow(2.0, excess_vel)
    penalty = torch.clamp(penalty, min=-1.0)
    return penalty.squeeze()


def reward_soil_spilling_penalty(env) -> torch.Tensor:
    """Penalize soil loss when bucket velocity exceeds the spilling threshold.
    
    The penalty scales with the amount of soil lost, but only applies 
    when moving above the velocity threshold.
    """
    # Calculate how much soil was lost (clip to only consider losses)
    soil_loss = torch.nn.functional.relu(env.last_fill_ratio - env.soil.get_fill_ratio()).squeeze()
    
    # Check if velocity exceeds threshold
    above_threshold = env.m545_measurements.bucket_vel_norm > env.cfg.terminations_excavation.bucket_vel_spillig_threshold
    
    # Only apply soil loss penalty when above velocity threshold
    penalty = torch.where(above_threshold, -soil_loss, torch.zeros_like(soil_loss))
    penalty = torch.clamp(penalty, min=-1.0)
    return penalty.squeeze()


def reward_bucket_filling(env):
    # if full, 0 reward (because it is the normalized, clipped ratio)
    # if loosing soil, large negative reward 0-prev
    fill_delta = (env.soil.get_fill_ratio() - env.last_fill_ratio).squeeze()
    fill_delta = torch.clip(fill_delta, min=0.0)
    close = env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    # if too close, no more positive reward for filling, allow negative reward for losing
    return torch.where(close, torch.clip(fill_delta, max=0.0), fill_delta)


def reward_max_depth_tracking(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_term_fill_ratio).squeeze()

    err = (
        env.m545_measurements.bucket_pos_w[:, 2].unsqueeze(-1)
        - (
            env.soil.get_max_depth_height_at_pos(env.m545_measurements.bucket_pos_w[:, 0].unsqueeze(-1), env.m545_measurements.bucket_pos_w[:, 1].unsqueeze(-1))
            - env.cfg.rewards_excavation.max_depth_tracking_offset
        )
    ).squeeze()

    rew = torch.exp(-torch.square(err) / env.cfg.rewards_excavation.max_depth_tracking_sigma)

    return torch.where(close | full, torch.zeros(env.num_envs, device=env.device), rew)


def reward_bucket_curl(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_curl_ratio).squeeze()

    # give reward while not curled enough
    not_curled = (env.m545_measurements.bucket_ang_gac > env.cfg.terminations_excavation.max_curl_ang_gac).squeeze()

    return torch.where(
        (close | full) & not_curled,
        env.m545_measurements.j_pitch_ang_vel[:, 1],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_pitch_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_term_fill_ratio).squeeze()

    # give reward while not high enough (negative depth = outside of soil)
    # not_high = (-self.env.soil.get_bucket_depth() < self.env.curr_manager.end_height_above_soil).squeeze()

    # Bucket COM is exactly at top of bucket (it can be that edge is high enough, but pitch not, and then no rew is given)
    # pitch should be at same hight than bucket edge at end (horizontal bucket full level)
    not_high = (
        env.soil.bucket_com_pos_w[:, 1:2]  # only x-z, 2d vec
        - env.soil.get_soil_height_at_pos(env.m545_measurements.bucket_pos_w[:, 0:1], env.m545_measurements.bucket_pos_w[:, 1:2])
        < env.curriculum_excavation.end_height_above_soil
    ).squeeze()

    # return torch.where((close | full) & not_high, self.env.m545_asset.j_pitch_vel[:, 2], self.zero_vec)

    # bottom plate at least horizontal before giving reward for going up
    # it looses all the soil if it out of soil and bp_ang > 0
    curled = (env.soil.get_bucket_bp_angle_w() < 0.0).squeeze()

    return torch.where(
        (close | full) & not_high & curled,
        env.m545_measurements.j_pitch_vel[:, 2],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_bucket_edge_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_term_fill_ratio).squeeze()

    # give reward while not hight enough (negative depth = outside of soil)
    not_high = (-env.soil.bucket_depth < env.curriculum_excavation.end_height_above_soil).squeeze()
    return torch.where(
        (close | full) & not_high,
        env.m545_measurements.bucket_vel_w[:, 2],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_bucket_edge_down(env):
    not_close = (
        env.m545_measurements.bucket_pos_w[:, 0] > env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    not_full = (env.soil.get_fill_ratio() < env.cfg.rewards_excavation.go_down_fill_ratio).squeeze()
    # return torch.where(
    #     # clip to 0 -> learns slower
    #     # not_full & not_close, torch.minimum(self.env.m545_asset.bucket_vel_w[:, 2], self.zero_vec), self.zero_vec
    #     not_full & not_close,
    #     self.env.m545_asset.bucket_vel_w[:, 2],
    #     self.zero_vec,
    # )

    bucket_vel_into_soil = torch.sum(env.m545_measurements.bucket_vel_w * -env.soil_normal_vec, dim=-1)
    return torch.where(
        # clip to 0 -> learns slower
        # not_full & not_close, torch.minimum(self.env.m545_asset.bucket_vel_w[:, 2], self.zero_vec), self.zero_vec
        not_full & not_close,
        -bucket_vel_into_soil,  # reward is negative, because we had in negative z direction before, this is now positive if it is going down
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_power(env):
    not_close = env.m545_measurements.bucket_pos_w[:, 0] > env.pullup_dist  # close
    not_full = (env.soil.get_fill_ratio() < env.curriculum_excavation.curr_term_fill_ratio).squeeze()
    clipped_power = torch.clip(torch.sum(env.m545_measurements.joint_vel * env.torques, dim=-1), min=0.0)

    return torch.where(not_full & not_close, clipped_power, torch.zeros(env.num_envs, device=env.device))


def reward_bucket_curl_and_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_curl_ratio).squeeze()

    # # give reward while not curled enough
    # not_curled = (self.env.soil.get_bucket_full_angle_w() > self.env.min_terminal_curl_angle).squeeze()

    # # give reward while not hight enough (negative depth = outside of soil)
    # not_high = (-self.env.soil.get_bucket_depth() < self.env.curr_manager.end_height_above_soil).squeeze()

    up_vel = env.m545_measurements.bucket_vel_w[:, 2]
    curl_vel = env.m545_measurements.j_pitch_ang_vel[:, 1]

    return torch.where(close | full, up_vel * curl_vel, torch.zeros(env.num_envs, device=env.device))


def termination_reward(env) -> torch.Tensor:
    return env.termination_excavation.compute_reward()
