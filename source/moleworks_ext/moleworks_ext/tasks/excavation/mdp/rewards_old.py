# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
import torch
from typing import TYPE_CHECKING

import isaacsim.core.utils.torch as torch_utils

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import SceneEntityCfg


def action_rate_l2_excavation(env) -> torch.Tensor:
    """Penalize the rate of change of the actions using L2-kernel."""
    return torch.sum(torch.square(env.actions - env.last_actions), dim=1)


def const_reward(env):
    return torch.ones(env.num_envs, device=env.device)


def reward_bucket_filling(env):
    # if full, 0 reward (because it is the normalized, clipped ratio)
    # if loosing soil, large negative reward 0-prev
    fill_delta = (env.soil.get_fill_ratio() - env.last_fill_ratio).squeeze()
    close = env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    # if too close, no more positive reward for filling, allow negative reward for losing
    return torch.where(close, torch.clip(fill_delta, max=0.0), fill_delta)


def reward_max_depth_tracking(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_term_fill_ratio).squeeze()

    err = (
        env.m545_measurements.bucket_pos_w[:, 2].unsqueeze(-1)
        - (
            env.soil.get_max_depth_height_at_pos(env.m545_measurements.bucket_pos_w[:, 0].unsqueeze(-1))
            - env.cfg.rewards_excavation.max_depth_tracking_offset
        )
    ).squeeze()

    rew = torch.exp(-torch.square(err) / env.cfg.rewards_excavation.max_depth_tracking_sigma)

    return torch.where(close | full, torch.zeros(env.num_envs, device=env.device), rew)


def reward_bucket_curl(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_curl_ratio).squeeze()

    # give reward while not curled enough
    not_curled = (env.soil.get_bucket_full_angle_w() > env.cfg.terminations_excavation.min_curl_ang).squeeze()

    return torch.where(
        (close | full) & not_curled,
        env.m545_measurements.j_pitch_ang_vel[:, 1],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_pitch_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_term_fill_ratio).squeeze()

    # give reward while not high enough (negative depth = outside of soil)
    # not_high = (-self.env.soil.get_bucket_depth() < self.env.curr_manager.end_height_above_soil).squeeze()

    # Bucket COM is exactly at top of bucket (it can be that edge is high enough, but pitch not, and then no rew is given)
    # pitch should be at same hight than bucket edge at end (horizontal bucket full level)
    # replace with pitch
    not_high = (
        env.m545_measurements.bucket_com_pos_w[:, 2:3]  # only x-z, 2d vec
        - env.soil.get_soil_height_at_pos(env.m545_measurements.bucket_pos_w[:, 0:1])
        < env.curriculum_excavation.end_height_above_soil
    ).squeeze()

    # return torch.where((close | full) & not_high, self.env.m545_asset.j_pitch_vel[:, 2], self.zero_vec)

    # bottom plate at least horizontal before giving reward for going up
    # it looses all the soil if it out of soil and bp_ang > 0
    curled = (env.soil.get_bucket_bp_angle_w() < 0.0).squeeze()

    return torch.where(
        (close | full) & not_high & curled,
        env.m545_measurements.j_pitch_vel[:, 2],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_bucket_edge_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_term_fill_ratio).squeeze()

    # give reward while not hight enough (negative depth = outside of soil)
    not_high = (-env.soil.get_bucket_depth() < env.curriculum_excavation.end_height_above_soil).squeeze()
    return torch.where(
        (close | full) & not_high,
        env.m545_measurements.bucket_vel_w[:, 2],
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_bucket_edge_down(env):
    not_close = (
        env.m545_measurements.bucket_pos_w[:, 0] > env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    not_full = (env.soil.get_fill_ratio() < env.cfg.rewards_excavation.go_down_fill_ratio).squeeze()
    # return torch.where(
    #     # clip to 0 -> learns slower
    #     # not_full & not_close, torch.minimum(self.env.m545_asset.bucket_vel_w[:, 2], self.zero_vec), self.zero_vec
    #     not_full & not_close,
    #     self.env.m545_asset.bucket_vel_w[:, 2],
    #     self.zero_vec,
    # )

    bucket_vel_into_soil = torch.sum(env.m545_measurements.bucket_vel_w * -env.soil_normal_vec, dim=-1)
    return torch.where(
        # clip to 0 -> learns slower
        # not_full & not_close, torch.minimum(self.env.m545_asset.bucket_vel_w[:, 2], self.zero_vec), self.zero_vec
        not_full & not_close,
        -bucket_vel_into_soil,  # reward is negative, because we had in negative z direction before, this is now positive if it is going down
        torch.zeros(env.num_envs, device=env.device),
    )


def reward_power(env):
    not_close = env.m545_measurements.bucket_pos_w[:, 0] > env.pullup_dist  # close
    not_full = (env.soil.get_fill_ratio() < env.curriculum_excavation.curr_term_fill_ratio).squeeze()
    clipped_power = torch.clip(torch.sum(env.m545_measurements.joint_vel * env.torques, dim=-1), min=0.0)

    return torch.where(not_full & not_close, clipped_power, torch.zeros(env.num_envs, device=env.device))


def reward_bucket_curl_and_up(env):
    close = (
        env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curriculum_excavation.curr_pullup_band
    )  # close
    full = (env.soil.get_fill_ratio() > env.curriculum_excavation.curr_curl_ratio).squeeze()

    # # give reward while not curled enough
    # not_curled = (self.env.soil.get_bucket_full_angle_w() > self.env.min_terminal_curl_angle).squeeze()

    # # give reward while not hight enough (negative depth = outside of soil)
    # not_high = (-self.env.soil.get_bucket_depth() < self.env.curr_manager.end_height_above_soil).squeeze()

    up_vel = env.m545_measurements.bucket_vel_w[:, 2]
    curl_vel = env.m545_measurements.j_pitch_ang_vel[:, 1]

    return torch.where(close | full, up_vel * curl_vel, torch.zeros(env.num_envs, device=env.device))


def termination_reward_digging(env) -> torch.Tensor:
    return env.termination_excavation.compute_reward()
