# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
import torch
from typing import TYPE_CHECKING

from isaaclab.assets import Articulation, RigidObject
from isaaclab.envs import ManagerBasedEnv
from isaaclab.managers import SceneEntityCfg
import isaacsim.core.utils.torch as torch_utils


def dof_pos(env: ManagerBasedEnv, asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_pos
    # print("dof_pos:", result)
    return result


def dof_vel(env: ManagerBasedEnv, asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_vel
    # print("dof_vel:", result)
    return result


def dof_tau(env) -> torch.Tensor:
    result = env.m545_measurements.arm_joint_tau
    # print("dof_tau:", result)
    return result


def last_action_excavation(env) -> torch.Tensor:
    result = env.actions
    # print("last_action_excavation:", result)
    return result


def bucket_lin_gac(env) -> torch.Tensor:
    result = torch.cat(
        (env.m545_measurements.bucket_pos_base[:, [0, 2]], env.m545_measurements.bucket_vel_base[:, [0, 2]]), dim=-1
    )
    # print("bucket_lin_gac:", result)
    return result


def bucket_lin_gac_base(env) -> torch.Tensor:
    return torch.cat(
        (env.m545_measurements.bucket_pos_base[:, [0, 2]], env.m545_measurements.bucket_vel_base[:, [0, 2]]), dim=-1
    )

def bucket_ang_gac(env) -> torch.Tensor:
    result = torch.cat(
        (env.m545_measurements.bucket_ang_gac.unsqueeze(-1), env.m545_measurements.bucket_ang_vel_base[:, 1:2]), dim=-1
    )
    # print("bucket_ang_gac:", result)
    return result


def bucket_lin_vel_norm(env) -> torch.Tensor:
    result = env.m545_measurements.bucket_vel_norm.unsqueeze(-1)
    # print("bucket_lin_vel_norm:", result)
    return result


def base_pitch_gac(env) -> torch.Tensor:
    result = env.m545_measurements.base_pitch_w.unsqueeze(-1)
    # print("base_pitch_gac:", result)
    return result


def fill_ratio_aoa(env) -> torch.Tensor:
    result = torch.cat((env.soil.get_fill_ratio(), env.m545_measurements.bucket_aoa.unsqueeze(-1)), dim=-1)
    # print("fill_ratio_aoa:", result)
    # print("aoa:", env.m545_measurements.bucket_aoa)
    return result


def fill_ratio(env) -> torch.Tensor:
    result = env.soil.get_fill_ratio()
    # print("fill_ratio:", result)
    return result


def aoa(env) -> torch.Tensor:
    result = env.m545_measurements.bucket_aoa.unsqueeze(-1)
    # print("aoa:", result)
    return result


def soil_height(env) -> torch.Tensor:
    # Transform soil height from world to base frame
    world_heights = env.soil_height_futures
    # print("world_heights shape:", world_heights.shape)  # Print the shape of world_heights
    # Ensure the dimensions match by expanding root_pos_w if necessary
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_heights.size(1))
    result = world_heights - root_pos_w_expanded
    # print("soil_height:", result)
    return result


def max_depth(env) -> torch.Tensor:
    # Transform max depth from world to base frame
    world_depths = env.max_depth_futures
    # print("world_depths shape:", world_depths.shape)  # Print the shape of world_depths
    # Ensure the dimensions match by expanding root_pos_w if necessary
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_depths.size(1))
    result = world_depths - root_pos_w_expanded
    # print("max_depth:", result)
    return result


# def bucket_depth(env) -> torch.Tensor:
#     result = env.soil.get_bucket_depth()
#     # print("bucket_depth:", result)
#     return result


def pitch_vel(env) -> torch.Tensor:
    result = env.m545_measurements.j_pitch_vel[:, [0, 2]]
    # print("pitch_vel:", result)
    return result


def pullup_dist(env) -> torch.Tensor:
    result = env.pullup_dist.unsqueeze(-1)
    # print("pullup_dist:", result)
    return result


def soil_parameters(env) -> torch.Tensor:
    res_non_scaled = env.soil.get_soil_params()[:, 0 : env.soil.get_n_soil_params_to_sample()]
    # print("soil_parameters:", res_non_scaled)
    return res_non_scaled

