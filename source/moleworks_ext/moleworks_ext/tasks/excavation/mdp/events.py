# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

import torch
from typing import TYPE_CHECKING

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import EventTermCfg, ManagerTermBase, SceneEntityCfg
from isaaclab.utils.math import quat_from_euler_xyz, sample_uniform

from moleworks_ext.tasks.excavation.excavation_utils.excavation_utils import u_rand

if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedEnv


def rejection_sampling(
    env: ManagerBasedEnv,
    env_ids: torch.Tensor,
    asset_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
):
    # print("start reset_idx")
    """Reset selected environments.
    resetting flag for termination in  step? -> only needed if reset_stepping is enabled, not used in latest version, doublecheck
    Resetting
        - reset pid
        - reset soil/fee

    Soil resampling
        - if enabled: sample soil with height (excavator config is then relative to the soil)
        - if enabled: sample max_depth
        - if enabled: sample pullup distance
        - if enabled: sample soil_obstacles

        - if enabled: randomize orientation (pitch, turn?)
        - if enabled: sample arm config
            if in soil, sample random fill volume
        - set wheel support positions

    Arm Resampling
        - update measurements
        - update fee forces (check if in obstacle and change params accordingly)
        - reset again if forces too large


    Other
        Logs episode info
        Resets selected buffers

    Args:
        env_ids (list[int]): List of environment ids which must be reset
    """
    asset: RigidObject = env.scene[asset_cfg.name]

    if len(env_ids) == 0:
        return
    # env.pid.reset(idxs=env_ids)
    env.soil.reset(idxs=env_ids)
    env.termination_excavation.reset(idxs=env_ids)

    if env.cfg.reset.sample_soil:
        env.soil.sample(idxs=env_ids)  # height & params & max depth, random or fixed, depending on cfg

    if env.cfg.reset.sample_max_depth:
        # TODO, already in soil.sample, maybe take it out three
        pass

    if env.cfg.reset.sample_pullup_dist:
        env.pullup_dist[env_ids] = u_rand(
            len(env_ids), env.cfg.reset.pullup_dist_range[0], env.cfg.reset.pullup_dist_range[1], env.device
        )

    env.m545_measurements.joint_vel[env_ids] = 0.0  # DOF Vel
    env.m545_measurements.root_lin_vel_w[env_ids] = 0.0
    env.m545_measurements.root_ang_vel_w[env_ids] = 0.0
    env.m545_measurements.j_pitch_vel[env_ids] = 0.0
    env.m545_measurements.j_pitch_ang_vel[env_ids] = 0.0

    # rejection "sampling" using cached reset states
    # all envs to reset are rejected at the beginning
    rejected = env_ids.clone()

    # get index bands to sample from
    rejected_sampling_idx_lower, rejected_sampling_idx_upper = env.reset_cache.get_sampling_idx(rejected)

    reset_loop_count = 0
    while len(rejected) > 0:
        if reset_loop_count > 1000:
            raise ValueError("Resetting loop count > 1000")
        reset_loop_count += 1

        env.soil.reset(rejected)  # can be that before it was in soil, fill state was set but rejected

        if not env.cfg.reset.fixed_config:
            sampled = torch.round(
                u_rand(len(rejected), rejected_sampling_idx_lower, rejected_sampling_idx_upper, device=env.device)
            ).to(torch.long)

        else:
            sampled = torch.ones(len(rejected), device=env.device, dtype=torch.long)
            sampled *= env.cfg.reset.configs["idx"]  # env.cfg.reset.configs.__dict__[env.cfg.reset.fixed_config]["idx"]
            print(f"FIXED reset to sample nr. {sampled[0]}")

        env.sampled[rejected] = sampled
        #####
        ## Get Measurements from Cache
        #####
        # when slicing, does not loose reference!
        env.m545_measurements.joint_pos[rejected] = env.reset_cache.data["dof_pos"][sampled]
        env.m545_measurements.root_pos_w[rejected] = env.reset_cache.data["base_pos"][sampled]
        env.m545_measurements.root_quat_w[rejected] = env.reset_cache.data["base_quat"][sampled]

        # for idx, box_asset in enumerate(env.box_asset_list):
        #    box_asset.root_pos_w[rejected] = env.reset_cache.data["box_" + str(idx)][sampled] - 0.016
        env.m545_measurements.mm[rejected] = env.reset_cache.data["mass_matrix"][sampled]
        env.m545_measurements.bucket_jac_lin_T_dof[rejected] = env.reset_cache.data["bucket_jac_lin"][
            sampled
        ].transpose(-2, -1)
        env.m545_measurements.bucket_jac_rot_T_dof[rejected] = env.reset_cache.data["bucket_jac_rot"][
            sampled
        ].transpose(-2, -1)

        env.m545_measurements.gravity_tau[rejected] = env.reset_cache.data["gravity_torques"][sampled]

        env.m545_measurements.bucket_pos_w[rejected] = env.reset_cache.data["bucket_pos"][sampled]
        env.m545_measurements.prev_bucket_pos_w[rejected] = env.reset_cache.data["bucket_pos"][
            sampled
        ]  # prev = current
        env.m545_measurements.bp_unit_vector_w[rejected] = env.reset_cache.data["bp_unit_vec"][sampled]
        env.m545_measurements.bucket_vel_w[rejected, :] = 0.0

        # update all env fee (easier than do it per index)
        env.soil.update_1(
            rejected
        )  # until bucket state, we sample fill volume based on depth and finish the soil update

        # get indices to resample soil fill ratio, if inside soil (depth > 0)
        # only do it for the ones to resample!!!!! -> changes ssp -> might lead to invalid soil config!!!
        rejected_in_soil_idx = torch.where(env.soil.get_bucket_depth()[rejected] > env.zero_scalar)[0]

        env.soil.set_bucket_fill_state(
            torch.rand(len(rejected_in_soil_idx), 1, device=env.device),
            rejected[rejected_in_soil_idx],
            is_ratio=True,
        )

        env.soil.update_2()  # update ssp, soil_params, forces

        # check the conditions
        too_close_x = (
            torch.abs(env.m545_measurements.bucket_pos_w[rejected, 0:1])
            < env.pullup_dist[rejected].unsqueeze(-1) + env.curriculum_excavation.curr_pullup_band
        )

        # these should now never be active anymore, because we sample only in the valid range
        # wrong, its min, max based on all soil heights in env

        too_high_z = env.soil.get_bucket_depth()[rejected] < env.cfg.reset.arm_depth_limits.lower
        too_low_z = env.soil.get_bucket_depth()[rejected] > env.cfg.reset.arm_depth_limits.upper * (
            1.0 - env.cfg.reset.only_above_soil
        )

        too_small_ang = env.soil.get_ssp_ang_to_soil()[rejected] < env.cfg.reset.min_ang_bucket_to_soil
        invalid_soil_model_state = env.soil.is_state_invalid(rejected)

        # too_large_force = (
        #     torch.linalg.norm(env.soil.get_resultant_force()[rejected], dim=-1, keepdim=True)
        #     > env.cfg.reset.max_soil_force
        # )
        # too_large_moment = (
        #     torch.linalg.norm(env.soil.get_resultant_moment()[rejected], dim=-1, keepdim=True)
        #     > env.cfg.reset.max_soil_moment
        # )  # only moment in y

        force_moment = torch.linalg.norm(
            env.soil.get_resultant_force()[rejected], dim=-1, keepdim=True
        ) + torch.linalg.norm(env.soil.get_resultant_moment()[rejected], dim=-1, keepdim=True)

        # max_force = 68.0 - 6.0 * env.m545_asset.bucket_pos_w[rejected, 0:1] #50-20
        # max_force = 74.0 - 8.0 * env.m545_asset.bucket_pos_w[rejected, 0:1]  # 50-10
        # max_force = 58.0 - 6.0 * env.m545_asset.bucket_pos_w[rejected, 0:1]  # 40-10
        # max_force = 42.0 - 4.0 * env.m545_asset.bucket_pos_w[rejected, 0:1]  # 30-10
        # max_force = 26.0 - 2.0 * env.m545_asset.bucket_pos_w[rejected, 0:1]  # 30-10

        too_large_force_moment = force_moment > env.cfg.reset.max_soil_force_and_moment
        # too_large_force = force_moment > max_force * 1000.0
        violating_max_depth = (
            env.m545_measurements.bucket_pos_w[rejected, 2:]
            < env.soil.get_max_depth_height_at_pos(
                env.m545_measurements.bucket_pos_w[rejected, 0:1], 
                env.m545_measurements.bucket_pos_w[rejected, 1:2],
                env_ids=rejected
            )
            - env.cfg.terminations_excavation.max_depth_overshoot
        )

        # check joint torques at reset soil + gravity!!

        env.bucket_force_com[rejected] = env.soil.get_resultant_force()[rejected]
        env.bucket_moment_com[rejected] = env.soil.get_resultant_moment()[rejected]

        ext_f_genco_tau = torch.matmul(
            env.m545_measurements.bucket_jac_lin_T_dof[rejected], env.bucket_force_com[rejected].unsqueeze(-1)
        )
        env.ext_f_tau[rejected] = ext_f_genco_tau.squeeze(-1)[:, -env.m545_asset.num_joints :]

        ext_m_genco_tau = torch.matmul(
            env.m545_measurements.bucket_jac_rot_T_dof[rejected], env.bucket_moment_com[rejected].unsqueeze(-1)
        )
        env.ext_m_tau[rejected] = ext_m_genco_tau.squeeze(-1)[:, -env.m545_asset.num_joints :]

        env.torques[rejected] = (
            -env.m545_measurements.gravity_tau[rejected] - env.ext_f_tau[rejected] - env.ext_m_tau[rejected]
        )

        env.limits.update(env.m545_measurements.joint_pos)

        # if only within limits -> isaac crash -> probably too large forces, this is fine 0.22% done after reset + 1 step
        violating_torque_lower_limits = (
            env.torques[rejected]
            < env.limits.curr_torque_limit_lower[rejected] + env.cfg.reset.max_soil_force_and_moment
        ).any(dim=-1, keepdim=True)
        violating_torque_upper_limits = (
            env.torques[rejected]
            > env.limits.curr_torque_limit_upper[rejected] - env.cfg.reset.max_soil_force_and_moment
        ).any(dim=-1, keepdim=True)

        # narrow resetting for trenching tests
        if env.cfg.reset.narrow.enable:
            # too_small_ang; done above
            too_large_ang = env.soil.get_ssp_ang_to_soil()[rejected] > env.cfg.reset.narrow.max_ang_bucket_to_soil
            # too_small_x; done above
            too_small_x = env.m545_measurements.bucket_pos_w[rejected, 0:1] < env.cfg.reset.narrow.x_min
            too_large_x = env.m545_measurements.bucket_pos_w[rejected, 0:1] > env.cfg.reset.narrow.x_max
            too_small_z = env.m545_measurements.bucket_pos_w[rejected, 2:] < env.cfg.reset.narrow.z_min
            too_large_z = env.m545_measurements.bucket_pos_w[rejected, 2:] > env.cfg.reset.narrow.z_max
        else:
            # set all to false with correct shape
            too_large_ang = torch.zeros_like(too_close_x)
            too_small_x = too_large_ang
            too_large_x = too_large_ang
            too_small_z = too_large_ang
            too_large_z = too_large_ang

        rejected_idx = torch.nonzero(
            (
                too_close_x
                | too_high_z
                | too_low_z
                | too_small_ang
                | invalid_soil_model_state
                # | too_large_force
                # | too_large_moment
                | too_large_force_moment  # worse without
                | violating_max_depth
                | violating_torque_lower_limits  # worse without
                | violating_torque_upper_limits
                # narrow reset
                | too_large_ang
                | too_small_x
                | too_large_x
                | too_small_z
                | too_large_z
            ),
            as_tuple=True,
        )[0]

        rejected = rejected[rejected_idx]  # select which envs need another reset
        rejected_sampling_idx_lower = rejected_sampling_idx_lower[rejected_idx]
        rejected_sampling_idx_upper = rejected_sampling_idx_upper[rejected_idx]

    # Write the new state
    asset.write_root_pose_to_sim(
        torch.cat(
            (
                env.m545_measurements.root_pos_w[env_ids].clone() + env.scene.env_origins[env_ids].clone(),
                env.m545_measurements.root_quat_w[env_ids].clone(),
            ),
            dim=-1,
        ),
        env_ids=env_ids,
    )
    asset.write_root_velocity_to_sim(torch.zeros((env.num_envs, 6), device=env.device)[env_ids], env_ids=env_ids)

    asset.write_joint_state_to_sim(
        position=env.m545_measurements.joint_pos[env_ids].clone(),
        velocity=env.m545_measurements.joint_vel[env_ids].clone(),
        env_ids=env_ids,
    )
    #

    # reset measurements used in obs (and logging)
    env.actions[env_ids] = 0.0  # last action reset in step loop

    # initial torque ob (soil + gravity)
    ext_f_genco_tau = torch.matmul(
        env.m545_measurements.bucket_jac_lin_T_dof[env_ids], env.soil.get_resultant_force()[env_ids].unsqueeze(-1)
    )
    ext_f_tau = ext_f_genco_tau.squeeze(-1)[:, -env.m545_asset.num_joints :]

    ext_m_genco_tau = torch.matmul(
        env.m545_measurements.bucket_jac_rot_T_dof[env_ids], env.soil.get_resultant_moment()[env_ids].unsqueeze(-1)
    )
    ext_m_tau = ext_m_genco_tau.squeeze(-1)[:, -env.m545_asset.num_joints :]

    env.torques[env_ids] = (
        -ext_f_tau
        - ext_m_tau
        - env.m545_measurements.gravity_tau[env_ids]
        # + env.m545_asset.root_physx_view.get_coriolis_and_centrifugal_forces()[env_ids]
    )
    env.ext_f_tau[env_ids] = 0.0
    env.ext_m_tau[env_ids] = 0.0
    env.inertial_tau[env_ids] = 0.0

    env.last_fill_ratio[env_ids] = env.soil.get_fill_ratio()[env_ids]
