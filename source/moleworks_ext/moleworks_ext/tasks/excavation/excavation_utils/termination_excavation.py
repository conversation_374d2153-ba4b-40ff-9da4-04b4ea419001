"""
   As the termination are handled differently then in Orbit, a custom manager for the excavation environment is implemented.
   The main reason is that the termination are separated for postive and negative. Additionaly a reward is computed for
   positive termination. The main manager could be changed to handle this.
"""

from __future__ import annotations

import torch
from copy import deepcopy
from typing import TYPE_CHECKING, Sequence

from isaaclab.utils import class_to_dict

if TYPE_CHECKING:
    from moleworks_ext.tasks.excavation.excavation_env_3d import ExcavationEnv


class Terminations_Excavation:
    def __init__(self, env: ExcavationEnv):
        self.env = env
        self.cfg = env.cfg.terminations_excavation
        self.num_envs = env.num_envs
        self.device = env.device

        # Buffers
        self.pos_terms_cfg = class_to_dict(self.cfg.positive_terminations)
        self.neg_terms_cfg = class_to_dict(self.cfg.negative_terminations)
        self.reset_buf = torch.zeros(self.env.num_envs, device=self.device, dtype=torch.bool)
        self.pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.full_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_full_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.partial_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_partial_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.close_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_close_pos_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.full_term_mean = torch.zeros(1, device=self.device)
        self.full_term_mean_d = torch.zeros(1, device=self.device)
        self.close_term_mean = torch.zeros(1, device=self.device)
        self.partial_term_mean = torch.zeros(1, device=self.device)

        self.last_neg_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.last_timeout_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)

        self.pos_term_rew_buf = torch.zeros(self.num_envs, device=self.device)
        self.neg_term_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)

        self.time_out_buf = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.time_out_count = torch.zeros(self.num_envs, device=self.device)

        # Buffers of terminated env not due to time out
        self.terminated = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        self.stuck_count = torch.zeros(self.num_envs, device=self.device, dtype=torch.int)

        # keep track of the lowest bucket pos_w
        self.ep_min_bucket_height = torch.zeros(self.num_envs, device=self.device)
        self.ep_min_bucket_height[:] = float("nan")

        # Util
        self.false_vec = torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)

        # negative terminations
        self.neg_term_funs = []
        self.neg_term_names = []
        self.neg_term_enable = []
        for name, enable in self.neg_terms_cfg.items():
            self.neg_term_names.append(name)
            self.neg_term_enable.append(enable)
            name = "_negative_termination_" + name
            self.neg_term_funs.append(getattr(self, name))

        # negative termination episode counts
        self.episode_neg_term_counts = {
            name: torch.zeros(
                self.num_envs,
                device=self.device,
            )
            for name in self.neg_terms_cfg.keys()
        }
        self.last_episode_neg_term = {
            name: torch.zeros(self.num_envs, device=self.device, dtype=bool) for name in self.neg_terms_cfg.keys()
        }
        self.episode_neg_term_buf = deepcopy(self.last_episode_neg_term)

        # positive terminations
        self.pos_term_funs = []
        self.pos_term_names = []
        for name, rew in self.pos_terms_cfg.items():
            self.pos_term_names.append(name)
            name = "_positive_termination_" + name
            self.pos_term_funs.append(getattr(self, name))

        # positive termination episode counts
        self.episode_pos_term_counts = {
            name: torch.zeros(
                self.num_envs,
                device=self.device,
            )
            for name in self.pos_terms_cfg.keys()
        }

    def reset(self, idxs=...):
        self.stuck_count[idxs] = 0

    def check_termination(self):
        """Check if environments need to be reset.
        Ensures at most one termination type (neg or pos) per env per episode.
        """
        # Clear buffers
        self.reset_buf[:] = False
        self.neg_term_buf[:] = False
        self.pos_term_buf[:] = False
        self.full_pos_term_buf[:] = False
        self.close_pos_term_buf[:] = False
        self.partial_pos_term_buf[:] = False
        self.terminated[:] = False
        
        for name in self.neg_term_names:
            self.episode_neg_term_buf[name][:] = False

        # ----------------------------------------------------
        # 1) Check all negative conditions (with priority)
        # ----------------------------------------------------
        valid_envs_mask = torch.ones(self.num_envs, device=self.device, dtype=torch.bool)
        if not self.cfg.disable_negative_termination:
            for i in range(len(self.neg_term_funs)):
                name = self.neg_term_names[i]
                # Only evaluate negative funs on envs that haven't yet triggered anything
                term_mask = self.neg_term_funs[i]() & valid_envs_mask
                term_mask &= self.neg_term_enable[i]  # if config says "enable" 
                
                # Update negative buffer for newly triggered envs
                update_mask = ~self.neg_term_buf & term_mask
                self.neg_term_buf |= update_mask
                self.episode_neg_term_counts[name] += update_mask
                self.episode_neg_term_buf[name][:] = update_mask

                # If an env just triggered negative, mark reset
                self.reset_buf |= update_mask
            
            # After finishing *all* negative checks, 
            # exclude negative-triggered envs from further positivity checks
            valid_envs_mask = ~self.reset_buf

        # ----------------------------------------------------
        # 2) Check all positive conditions
        # ----------------------------------------------------
        # Only evaluate positives on envs that did not trigger a negative
        full_mask = torch.zeros_like(valid_envs_mask)
        close_mask = torch.zeros_like(valid_envs_mask)
        partial_mask = torch.zeros_like(valid_envs_mask)

        for i in range(len(self.pos_term_funs)):
            name = self.pos_term_names[i]
            if name == "desired_full":
                # Evaluate only on valid envs
                tm = self.pos_term_funs[i]() & valid_envs_mask  
                full_mask |= tm
            elif name == "desired_close":
                tm = self.pos_term_funs[i]() & valid_envs_mask  
                close_mask |= tm
            elif name == "desired_partial":
                tm = self.pos_term_funs[i]() & valid_envs_mask  
                partial_mask |= tm

        # Hierarchy: full > close > partial
        # Remove "close" from those that triggered "full"
        close_mask &= ~full_mask
        # Remove "partial" from those that triggered "full" or "close"
        partial_mask &= ~(full_mask | close_mask)

        # Combine
        self.full_pos_term_buf[:] = full_mask
        self.close_pos_term_buf[:] = close_mask
        self.partial_pos_term_buf[:] = partial_mask

        self.pos_term_buf |= (full_mask | close_mask | partial_mask)
        self.reset_buf |= self.pos_term_buf
        self.terminated |= self.reset_buf

        # ----------------------------------------------------
        # 3) Check time-outs (if configured)
        # ----------------------------------------------------
        if self.env.cfg.send_timeouts:
            self.time_out_buf[:] = self.env.episode_length_buf > self.env.max_episode_length
            self.time_out_count += self.time_out_buf
            self.reset_buf |= self.time_out_buf

        # Track lowest point in the bucket’s world coordinates
        self.ep_min_bucket_height = torch.where(
            self.ep_min_bucket_height < self.env.m545_measurements.bucket_pos_w[:, 2],
            self.ep_min_bucket_height,
            self.env.m545_measurements.bucket_pos_w[:, 2],
        )
        self.ep_min_bucket_height[self.reset_buf] = float("nan")

        return self.reset_buf


    def compute_reward(self):
        # negative termination reward only: 1) if no timeout
        self.neg_term_buf *= ~self.time_out_buf
        neg_term_rew = self.neg_term_buf * self.cfg.neg_term_rew

        # positive termination reward only: 1) if no timeout and 2) no negative termination
        pos_term_mask = (self.pos_term_buf * ~self.neg_term_buf) * ~self.time_out_buf

        # Update terminations with hierarchy: full > close > partial
        # Full terminations take precedence
        self.full_pos_term_buf *= pos_term_mask
        self.episode_pos_term_counts["desired_full"] += self.full_pos_term_buf

        # Close terminations (excluding full terminations)
        self.close_pos_term_buf *= pos_term_mask & ~self.full_pos_term_buf
        self.episode_pos_term_counts["desired_close"] += self.close_pos_term_buf

        # Partial terminations (excluding both full and close terminations)
        self.partial_pos_term_buf *= pos_term_mask & ~(self.full_pos_term_buf | self.close_pos_term_buf)
        self.episode_pos_term_counts["desired_partial"] += self.partial_pos_term_buf

        # Compute positive termination rewards
        pos_term_rew = (
            self.full_pos_term_buf * self.cfg.positive_terminations.desired_full
            + self.partial_pos_term_buf * self.cfg.positive_terminations.desired_partial
            + self.close_pos_term_buf * self.cfg.positive_terminations.desired_close * (
                1 + torch.min(
                    self.env.soil.get_fill_ratio().squeeze() / self.env.curriculum_excavation.curr_term_fill_ratio,
                    torch.tensor(1.0, device=self.device)
                )
            )
        )

        # logging and curriculum
        term_ids = self.reset_buf.nonzero(as_tuple=False).flatten()
        self.last_full_pos_term_buf[term_ids] = self.full_pos_term_buf[term_ids]
        self.last_close_pos_term_buf[term_ids] = self.close_pos_term_buf[term_ids]
        self.last_partial_pos_term_buf[term_ids] = self.partial_pos_term_buf[term_ids]
        # means for curriculum
        self.full_term_mean_d[:] = torch.mean(self.last_full_pos_term_buf.to(torch.float32)) - self.full_term_mean
        self.full_term_mean[:] = torch.mean(self.last_full_pos_term_buf.to(torch.float32))

        self.close_term_mean[:] = torch.mean(self.last_close_pos_term_buf.to(torch.float32))

        # partial
        self.partial_term_mean[:] = torch.mean(self.last_partial_pos_term_buf.to(torch.float32))

        self.last_timeout_term_buf[term_ids] = self.time_out_buf[term_ids]

        # all negatives (sum)
        self.last_neg_term_buf[term_ids] = self.neg_term_buf[term_ids]

        # individual negatives
        for name in self.neg_term_names:
            self.episode_neg_term_buf[name][term_ids] *= ~self.time_out_buf[term_ids]
            self.last_episode_neg_term[name][term_ids] = self.episode_neg_term_buf[name][term_ids]

        return neg_term_rew + pos_term_rew

    """
    Termination Conditions
    """
    def _negative_termination_torque_limits(self):
        tau_normalized = (self.env.m545_measurements.arm_joint_tau - self.env.limits.curr_torque_limit_lower) / (self.env.limits.curr_torque_limit_upper - self.env.limits.curr_torque_limit_lower)
        mask = torch.any((tau_normalized > 1.0) | (tau_normalized < -1.0), dim=-1)
        return mask

    def _negative_termination_bucket_vel(self):
        # return torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        mask = self.env.m545_measurements.bucket_vel_norm > self.cfg.termination_bucket_vel
        # print('_negative_termination_bucket_vel', mask, 'Bucket vel', self.env.m545_measurements.bucket_vel_norm)
        return mask

    def _negative_termination_bucket_aoa(self):
        bad_aoa = self.env.m545_measurements.bucket_aoa < 0.0
        fast_enough = self.env.m545_measurements.bucket_vel_norm > self.cfg.bucket_vel_aoa_threshold  # 0.075
        in_soil = (self.env.soil.get_bucket_depth() > 0.05).squeeze()
        mask = torch.logical_and(bad_aoa, torch.logical_and(fast_enough, in_soil))
        # print('_negative_termination_bucket_aoa', mask)
        return mask

    def _negative_termination_base_vel(self):  # Might be Always true
        # return torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        base_vel = self.env.scene.articulations["robot"].data.root_lin_vel_w
        base_vel_excluding_y = torch.stack((base_vel[:, 0], base_vel[:, 2]), dim=-1)
        mask = torch.linalg.norm(base_vel_excluding_y, dim=-1) > self.cfg.max_base_vel  # 0.1
        # print('_negative_termination_base_vel', mask, 'Base_vel :', torch.linalg.norm(base_vel, dim=-1))
        return mask

    def _negative_termination_joint_vel(self):
        mask = torch.abs(self.env.scene.articulations["robot"].data.joint_vel).any(dim=-1) > self.cfg.max_joint_vel
        # print('_negative_termination_joint_vel', mask)
        return mask

    def _negative_termination_bucket_height(self):  # Might be Always true
        # return torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        cond_a = self.env.soil.get_fill_ratio() <= self.cfg.term_above_soil_fill_ratio
        cond_b = -self.env.soil.get_bucket_depth() > self.cfg.max_bucket_height_above_soil
        mask = torch.logical_and(cond_a, cond_b).squeeze()
        # print('_negative_termination_bucket_height', mask)
        # print('_negative_termination_bucket_height conda a', cond_a)
        # print('_negative_termination_bucket_height conda b', cond_b)
        return mask

    def _negative_termination_invalid_soil_model(self):
        # already checked at the end of inter decimation loop
        # return torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        mask = self.env.inter_decimation_soil_model_invalid.squeeze()
        # print('_negative_termination_invalid_soil_model', mask)
        return mask

    def _negative_termination_self_collision(self):
        # only bucket with base/boom can collide
        # return torch.linalg.norm(self.env.m545_asset.bucket_collision_f, dim=-1) > 1.0
        mask = self.env.inter_decimation_self_collision.squeeze()
        # print('_negative_termination_self_collision', mask)
        return mask

    def _negative_termination_max_depth(self):
        mask = (
            self.env.m545_measurements.bucket_pos_w[:, 2].unsqueeze(-1)
            < (
                self.env.soil.get_max_depth_height_at_pos(self.env.m545_measurements.bucket_pos_w[:, 0].unsqueeze(-1), self.env.m545_measurements.bucket_pos_w[:, 1].unsqueeze(-1))
                - self.cfg.max_depth_overshoot
            )
        ).squeeze()
        # print('_negative_termination_max_depth', mask)
        return mask

    def _negative_termination_pullup(self):
        # return  torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        # mask = (self.env.scene.env_origins[:, 0]-self.env.m545_measurements.bucket_pos_w[:, 0] < self.env.pullup_dist).squeeze() # Or rotate
        mask = (torch.abs(self.env.m545_measurements.bucket_pos_w[:, 0]) < self.env.pullup_dist).squeeze()  # Or rotate

        # print('_negative_termination_pullup', mask)
        return mask

    def _negative_termination_spilling_soil(self):
        # return  torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        cond_a = (self.env.soil.get_fill_ratio() < self.env.last_fill_ratio).squeeze()
        cond_b = (self.env.m545_measurements.bucket_vel_norm > self.cfg.bucket_vel_spillig_threshold).squeeze()
        mask = torch.logical_and(cond_a, cond_b)
        # print('_negative_termination_spilling_soil', mask)
        return mask

    def _negative_termination_stuck(self):
        # return  torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        # count consecutive too slow
        too_slow = self.env.m545_measurements.bucket_vel_norm < 0.1
        self.stuck_count += too_slow
        # if not too slow, reset
        self.stuck_count *= too_slow

        mask = self.stuck_count > 3
        # print'_negative_termination_stuck', mask)
        return mask

    def _positive_termination_desired_close(self):
        close = (
            torch.abs(self.env.m545_measurements.bucket_pos_w[:, 0])
            < self.env.pullup_dist + self.env.curriculum_excavation.curr_pullup_band
        )

        high = torch.where(
            torch.isnan(self.ep_min_bucket_height),
            self.false_vec,
            (self.env.m545_measurements.bucket_pos_w[:, 2] - self.ep_min_bucket_height)
            > self.env.curriculum_excavation.curr_term_height_above_soil,  # dist to max depth
        ).squeeze()

        curl = (self.env.m545_measurements.bucket_ang_gac < self.cfg.max_curl_ang_gac).squeeze()
        curl &= (self.env.m545_measurements.bucket_ang_gac > self.cfg.min_curl_ang_gac).squeeze()

        # filling
        a_bit_full = (self.env.soil.get_fill_ratio() > self.cfg.term_above_soil_fill_ratio).squeeze()

        mask = close
        mask &= high
        mask &= curl
        mask &= a_bit_full

        return mask

    def _positive_termination_desired_full(self):
        # return  torch.zeros(self.num_envs, device=self.device, dtype=torch.bool)
        full = (self.env.soil.get_fill_ratio() > self.env.curriculum_excavation.curr_term_fill_ratio).squeeze()

        high = torch.where(
            torch.isnan(self.ep_min_bucket_height),
            self.false_vec,
            (self.env.m545_measurements.bucket_pos_w[:, 2] - self.ep_min_bucket_height)
            > self.env.curriculum_excavation.curr_term_height_above_soil,  # dist to max depth
        ).squeeze()

        curl = (self.env.m545_measurements.bucket_ang_gac < self.cfg.max_curl_ang_gac).squeeze()
        curl &= (self.env.m545_measurements.bucket_ang_gac > self.cfg.min_curl_ang_gac).squeeze()

        mask = full
        mask &= high
        mask &= curl
        # print('_positive_termination_desired_full', mask)

        return mask

    def _positive_termination_desired_partial(self):
        """Similar to full termination but with partial filling of the bucket."""
        # Check if bucket is partially filled (above minimum threshold but below full threshold)
        a_bit_full = (self.env.soil.get_fill_ratio() > self.cfg.term_above_soil_fill_ratio).squeeze()
        
        # Check height condition (same as full termination)
        high = torch.where(
            torch.isnan(self.ep_min_bucket_height),
            self.false_vec,
            (self.env.m545_measurements.bucket_pos_w[:, 2] - self.ep_min_bucket_height)
            > self.env.curriculum_excavation.curr_term_height_above_soil,
        ).squeeze()

        # Check curl angle (same as full termination)
        curl = (self.env.m545_measurements.bucket_ang_gac < self.cfg.max_curl_ang_gac).squeeze()
        curl &= (self.env.m545_measurements.bucket_ang_gac > self.cfg.min_curl_ang_gac).squeeze()

        mask = a_bit_full
        mask &= high
        mask &= curl
        return mask
    
    def reset_excavation(self, env_ids: Sequence[int] | None = None) -> dict[str, torch.Tensor]:
        """Returns the episodic counts of episode_neg_term_counts_by_assetindividual termination terms.
            Args:
            env_ids: The environment ids. Defaults to None, in which case
                all environments are considered.

        Returns:
            Dictionary of episodic sum of individual reward terms.
        """
        # resolve environment ids
        if env_ids is None:
            env_ids = slice(None)

        extras = {}

        forword = "Episode Termination/"
        extras[forword + "last_timeout"] = torch.mean(self.last_timeout_term_buf.to(torch.float32)).item()
        extras[forword + "last_total_negative"] = torch.mean(self.last_neg_term_buf.to(torch.float32)).item()

        for name in self.neg_term_names:
            extras[forword + "last_negative_" + name] = torch.mean(
                self.last_episode_neg_term[name].to(torch.float32)
            ).item()

        extras[forword + "last_full"] = self.full_term_mean.item()
        extras[forword + "last_close"] = self.close_term_mean.item()
        # Add last_partial
        extras[forword + "last_partial"] = torch.mean(self.last_partial_pos_term_buf.to(torch.float32)).item()

        # Epidode lengths
        forword = "Episode length/"
        ids = (self.full_pos_term_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "full"] = mean.item() if torch.isfinite(mean) else 0.0

        ids = (self.close_pos_term_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "close"] = mean.item() if torch.isfinite(mean) else 0.0

        # Add partial termination length
        ids = (self.partial_pos_term_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "partial"] = mean.item() if torch.isfinite(mean) else 0.0

        ids = (self.time_out_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "timeout"] = mean.item() if torch.isfinite(mean) else 0.0

        ids = (self.neg_term_buf > 0).nonzero(as_tuple=False)
        mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
        extras[forword + "tot_negative"] = mean.item() if torch.isfinite(mean) else 0.0

        for name in self.neg_term_names:
            ids = (self.episode_neg_term_buf[name] > 0).nonzero(as_tuple=False)
            mean = torch.mean(self.env.episode_length_buf[ids].to(torch.float32))
            extras[forword + "neg_" + name] = mean.item() if torch.isfinite(mean) else 0.0

        return extras