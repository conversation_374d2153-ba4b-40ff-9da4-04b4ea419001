from __future__ import annotations

import torch
from collections.abc import Sequence
from typing import TYPE_CHECKING

from isaaclab.managers import RewardManager

if TYPE_CHECKING:
    from moleworks_ext.tasks.excavation.excavation_env_3d import ExcavationEnv


class ExcavationRewardManager(RewardManager):
    """Specialized excavation reward manager"""

    def __init__(self, cfg: object, env: ExcavationEnv):
        super().__init__(cfg, env)
        self.zero_vec = torch.zeros(env.num_envs, device=self.device)
        self.one_step_rew = dict.fromkeys(self.active_terms)
        for key in self.one_step_rew.keys():
            self.one_step_rew[key] = torch.zeros(env.num_envs, device=self.device)

    def compute(self, dt: float) -> torch.Tensor:
        """Computes the reward signal as a weighted sum of individual terms.

        This function calls each reward term managed by the class and adds them to compute the net
        reward signal. It also updates the episodic sums corresponding to individual reward terms.

        Args:
            dt: The time-step interval of the environment.

        Returns:
            The net reward signal of shape (num_envs,).
        """
        # reset computation
        self._reward_buf[:] = 0.0
        # iterate over all the reward terms
        for name, term_cfg in zip(self._term_names, self._term_cfgs):
            # skip if weight is zero (kind of a micro-optimization)
            if term_cfg.weight == 0.0:
                continue
            # compute term's value
            value = term_cfg.func(self._env, **term_cfg.params) * term_cfg.weight  # * dt
            # update total reward
            self._reward_buf += value
            # update episodic sum
            self._episode_sums[name] += value
            self.one_step_rew[name] = value

        return self._reward_buf

    def reset_excavation(self, env_ids: Sequence[int] | None = None) -> dict[str, torch.Tensor]:
        """Returns the episodic sum of reward per episode and termination type

        Args:
            env_ids: The environment ids for which the episodic sum of
                individual reward terms is to be returned. Defaults to all the environment ids.

        Returns:
            Dictionary of episodic sum of individual reward terms.
        """
        # resolve environment ids
        if env_ids is None:
            env_ids = slice(None)
        # store information
        extras = {}

        # reward per episode and termination type
        ids = (self._env.termination_excavation.full_pos_term_buf > 0).nonzero(as_tuple=False)
        self._add_rews_to_log_excavation(ids, "full", extras)

        ids = (self._env.termination_excavation.close_pos_term_buf > 0).nonzero(as_tuple=False)
        self._add_rews_to_log_excavation(ids, "close", extras)

        ids = (self._env.termination_excavation.time_out_buf > 0).nonzero(as_tuple=False)
        self._add_rews_to_log_excavation(ids, "timeout", extras)

        ids = (self._env.termination_excavation.neg_term_buf > 0).nonzero(as_tuple=False)
        self._add_rews_to_log_excavation(ids, "tot_negative", extras)

        for key in self._episode_sums.keys():
            # Reset episode sums to 0
            self._episode_sums[key][env_ids] = 0.0
        # reset all the reward terms
        for term_cfg in self._class_term_cfgs:
            term_cfg.func.reset(env_ids=env_ids)

        return extras

    def _add_rews_to_log_excavation(self, ids, log_name, extras):

        for key in self._episode_sums.keys():
            per_episode_reward = torch.where(
                self._env.episode_length_buf[ids] > 0,
                self._episode_sums[key][ids] / self._env.episode_length_buf[ids],
                self.zero_vec[ids],
            )
            mean = torch.mean(per_episode_reward)
            extras["Episode Reward/" + log_name + "_" + key] = mean.item() if torch.isfinite(mean) else 0.0
