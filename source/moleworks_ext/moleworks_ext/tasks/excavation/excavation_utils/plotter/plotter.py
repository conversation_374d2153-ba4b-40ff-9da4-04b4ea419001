# Script designed to plot data saved from the Logger to CSV

import matplotlib.pyplot as plt
import numpy as np

import pandas as pd

# Set global parameters for all plots
plt.rcParams["axes.labelsize"] = 14  # For x and y labels
plt.rcParams["axes.titlesize"] = 16  # For title
plt.rcParams["xtick.labelsize"] = 12  # For x tick labels
plt.rcParams["ytick.labelsize"] = 12  # For y tick labels

# Load the data
csv_file_name = "isaac_sim.csv"
df = pd.read_csv(csv_file_name)

# Remove '.csv' from the file name to use in the title
csv_title = csv_file_name[:-4]

# Timestep
dt = 0.04
decimation = 4
# Number of joints (assuming you know this or can derive it from the DataFrame)
num_joints = 4


# ------------- Calculate manual differentiation for each dof_pos to get dof_vel_manual
for i in range(num_joints):
    pos_data = df[f"dof_pos_{i}"].to_numpy()
    vel_manual = np.zeros_like(pos_data)
    for j in range(1, pos_data.shape[0]):  # starting from 1 to avoid index -1 in calculation
        vel_manual[j] = (pos_data[j] - pos_data[j - 1]) / (dt * decimation)
    df[f"dof_vel_manual_{i}"] = vel_manual

# Calculate manual differentiation for bucket

pos_data = df["bucket_pos_x"].to_numpy()
vel_manual = np.zeros_like(pos_data)
for j in range(1, pos_data.shape[0]):  # starting from 1 to avoid index -1 in calculation
    vel_manual[j] = (pos_data[j] - pos_data[j - 1]) / (dt * decimation)
df[f"bucket_manual_vel_x"] = vel_manual

pos_data = df["bucket_pos_y"].to_numpy()
vel_manual = np.zeros_like(pos_data)
for j in range(1, pos_data.shape[0]):  # starting from 1 to avoid index -1 in calculation
    vel_manual[j] = (pos_data[j] - pos_data[j - 1]) / (dt * decimation)
df[f"bucket_manual_vel_y"] = vel_manual

pos_data = df["bucket_pos_z"].to_numpy()
vel_manual = np.zeros_like(pos_data)
for j in range(1, pos_data.shape[0]):  # starting from 1 to avoid index -1 in calculation
    vel_manual[j] = (pos_data[j] - pos_data[j - 1]) / (dt * decimation)
df[f"bucket_manual_vel_z"] = vel_manual

# Calculate manual differentiation for roto base body

pos_data = df["j_pitch_pos_x"].to_numpy()
vel_manual = np.zeros_like(pos_data)
for j in range(1, pos_data.shape[0]):  # starting from 1 to avoid index -1 in calculation
    vel_manual[j] = (pos_data[j] - pos_data[j - 1]) / (dt * decimation)
df[f"j_pitch_vel_manual_x"] = vel_manual

pos_data = df["j_pitch_pos_y"].to_numpy()
vel_manual = np.zeros_like(pos_data)
for j in range(1, pos_data.shape[0]):  # starting from 1 to avoid index -1 in calculation
    vel_manual[j] = (pos_data[j] - pos_data[j - 1]) / (dt * decimation)
df[f"j_pitch_vel_manual_y"] = vel_manual

pos_data = df["j_pitch_pos_z"].to_numpy()
vel_manual = np.zeros_like(pos_data)
for j in range(1, pos_data.shape[0]):  # starting from 1 to avoid index -1 in calculation
    vel_manual[j] = (pos_data[j] - pos_data[j - 1]) / (dt * decimation)
df[f"j_pitch_vel_manual_z"] = vel_manual


# --- Quuaternion finite difference
def quaternion_to_angular_velocity(q, delta_t):
    # Ensure the quaternion is normalized
    q = q / np.linalg.norm(q)
    w, x, y, z = q
    # Calculate the angle
    angle = 2 * np.arccos(w)
    # Avoid division by zero and ensure angle is not too small
    if angle > 1e-6:
        scale = angle / (delta_t * np.sqrt(1 - w**2))
        omega = scale * np.array([x, y, z])
    else:
        omega = np.array([0, 0, 0])
    return omega


# Example quaternions (q_i and q_{i+1})
q_i = np.array([0.9238795, 0, 0, 0.3826834])  # Quaternion at time t_i
q_next = np.array([0.7071068, 0, 0, 0.7071068])  # Quaternion at time t_{i+1}

# Calculate the relative quaternion q_delta
q_delta = q_next * np.array([q_i[0], -q_i[1], -q_i[2], -q_i[3]])  # Assuming q_i is normalized

# Assume Delta t
delta_t = 1  # Time interval between samples

# Calculate angular velocity
omega = quaternion_to_angular_velocity(q_delta, delta_t)

print("Angular Velocity (rad/s):", omega)


# ----------------- Plot

# bucket

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_lin_vel_w_z"], label="j_pitch_lin_vel_w_z")
plt.plot(df["j_pitch_ang_vel_x"] * df["w_r_pe_y"] - df["j_pitch_ang_vel_y"] * df["w_r_pe_x"], label="Cross product z")
plt.plot(df["bucket_vel_z"], label="bucket_vel_z")

# plt.plot(df['j_pitch_vel_manual_z'], label='j_pitch_vel_manual_z', linestyle='--')
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket  vel z contrib")
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()

plt.show()


# ---------- J pitch ang vel


plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_ang_vel_x"], label="j_pitch_ang_vel_x")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch_ang_vel_ x Velocity Comparison")
# plt.ylim([-1,1])
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_ang_vel_y"], label="j_pitch_ang_vel_y")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch_ang_vel_ y Velocity Comparison")
# plt.ylim([-1,1])
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_ang_vel_z"], label="j_pitch_ang_vel_z")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch_ang_vel_ z Velocity Comparison")
# plt.ylim([-1,1])
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()

# w_r_pe

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["w_r_pe_x"], label="w_r_pe_x")
# Include the CSV title in the plot title
plt.title(f"{csv_title} w_r_pe_x")
# plt.ylim([-1,1])
plt.xlabel("Timestep")
plt.ylabel("value")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["w_r_pe_y"], label="w_r_pe_y")
# Include the CSV title in the plot title
plt.title(f"{csv_title} w_r_pe_y")
# plt.ylim([-1,1])
plt.xlabel("Timestep")
plt.ylabel("value")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["w_r_pe_z"], label="w_r_pe_z")
# Include the CSV title in the plot title
plt.title(f"{csv_title} w_r_pe_z ")
# plt.ylim([-1,1])
plt.xlabel("Timestep")
plt.ylabel("value")
plt.legend()


plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_ang_vel_x"] * df["w_r_pe_y"] - df["j_pitch_ang_vel_y"] * df["w_r_pe_x"], label="Cross product z")
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket vel Cross product z componant ")
# plt.ylim([-1,1])
plt.xlabel("Timestep")
plt.ylabel("value")
plt.legend()


plt.show()

# ---------- J pitch lin vel

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_lin_vel_w_x"], label="j_pitch_lin_vel_w_x")
plt.plot(df["j_pitch_vel_manual_x"], label="j_pitch_vel_manual_x", linestyle="--")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch lin x Velocity Comparison")
plt.ylim([-1, 1])
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_lin_vel_w_y"], label="j_pitch_lin_vel_w_y")
plt.plot(df["j_pitch_vel_manual_y"], label="j_pitch_vel_manual_y", linestyle="--")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch lin Velocity Comparison")
plt.ylim([-1, 1])
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()


plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_lin_vel_w_z"], label="j_pitch_lin_vel_w_z")
plt.plot(df["j_pitch_vel_manual_z"], label="j_pitch_vel_manual_z", linestyle="--")
plt.ylim([-1, 1])
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch lin Velocity Comparison")
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()


plt.show()


# ----- Plot dof_vel_manual vs dof_vel for each joint
for i in range(num_joints):
    plt.figure()  # Initialize a new figure for each plot
    plt.plot(df[f"dof_vel_{i}"], label=f"dof_vel_{i}")
    plt.plot(df[f"dof_vel_manual_{i}"], label=f"dof_vel_manual_{i}", linestyle="--")
    # Include the CSV title in the plot title
    plt.title(f"{csv_title} - Joint {i} Velocity Comparison")
    plt.xlabel("Timestep")
    plt.ylabel("Velocity")
    plt.legend()
plt.show()

# ----- Bucket pos and vel and manual vel

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["bucket_pos_x"], label="bucket_pos_x")
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket x pos")
plt.xlabel("Timestep")
plt.ylabel("Pos")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["bucket_pos_y"], label="bucket_pos_y")
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket y pos")
plt.xlabel("Timestep")
plt.ylabel("Pos")
plt.legend()


plt.figure()  # Initialize a new figure for each plot
plt.plot(df["bucket_pos_z"], label="bucket_pos_z")
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket z pos")
plt.xlabel("Timestep")
plt.ylabel("Pos")
plt.legend()


plt.show()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["bucket_vel_x"], label="bucket_vel_x")
plt.plot(df["bucket_manual_vel_x"], label="bucket_manual_vel_x", linestyle="--")
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket x Velocity Comparison")
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["bucket_vel_y"], label="bucket_vel_y")
plt.plot(df["bucket_manual_vel_y"], label="bucket_manual_vel_y", linestyle="--")
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket y Velocity Comparison")
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()


plt.figure()  # Initialize a new figure for each plot
plt.plot(df["bucket_vel_z"], label="bucket_vel_z")
plt.plot(df["bucket_manual_vel_z"], label="bucket_manual_vel_z", linestyle="--")
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket z Velocity Comparison")
plt.xlabel("Timestep")
plt.ylabel("Velocity")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["bucket_vel_norm"], label="bucket_vel_norm")
# Include the CSV title in the plot title
plt.title(f"{csv_title} Bucket vel norm")
plt.xlabel("Timestep")
plt.ylabel("Pos")
plt.legend()


plt.show()

# ---------- J pitch pos


plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_pos_x"], label="j_pitch_pos_x")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch x pos")
plt.xlabel("Timestep")
plt.ylabel("Pos")
plt.legend()

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_pos_y"], label="j_pitch_pos_y")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch y pos")
plt.xlabel("Timestep")
plt.ylabel("Pos")
plt.legend()


plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_pos_z"], label="j_pitch_pos_z")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch z pos")
plt.xlabel("Timestep")
plt.ylabel("Pos")
plt.legend()


plt.show()


# ---------- j_pitch_quat

plt.figure()  # Initialize a new figure for each plot
plt.plot(df["j_pitch_quat_w"], label="w")
plt.plot(df["j_pitch_quat_x"], label="x")
plt.plot(df["j_pitch_quat_y"], label="y")
plt.plot(df["j_pitch_quat_z"], label="z")
# Include the CSV title in the plot title
plt.title(f"{csv_title} j_pitch_quat")
# plt.ylim([-1,1])
plt.xlabel("Timestep")
plt.ylabel("angle")
plt.legend()

plt.show()
