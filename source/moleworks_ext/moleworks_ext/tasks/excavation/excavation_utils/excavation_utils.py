import matplotlib.pyplot as plt
import numpy as np
import torch
from multiprocessing.sharedctypes import Value


def u_rand(size, min, max, device):
    return min + (max - min) * torch.rand(size, device=device)


def deg2rad(deg):
    return deg / 180.0 * np.pi


def plot_hist(data, label, filename):
    plt.figure()
    plt.hist(data.view(-1).cpu().numpy(), bins=100, label=label)
    plt.legend()
    plt.grid()
    # Save the plot as a file
    save_path = os.path.join(DESKTOP_PATH, filename)
    plt.savefig(save_path)
    # Close the plot to free up memory, as the figure is no longer needed after saving
    plt.close()


def plot_hist_double(data1, data2, label1, label2, filename):
    plt.figure()
    plt.hist(data1.view(-1).cpu().numpy(), bins=100, label=label1)
    plt.hist(data2.view(-1).cpu().numpy(), bins=100, label=label2)
    plt.legend()
    plt.grid()
    # Save the plot as a file
    save_path = os.path.join(DESKTOP_PATH, filename)
    plt.savefig(save_path)
    # Close the plot to free up memory, as the figure is no longer needed after saving
    plt.close()
