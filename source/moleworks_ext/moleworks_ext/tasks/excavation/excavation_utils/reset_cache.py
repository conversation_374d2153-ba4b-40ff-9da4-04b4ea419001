import numpy as np
import os
import pathlib
import pickle as pkl
import torch

from moleworks_ext import MOLEWORKS_RSC_DIR

""" class to reset to fixed pose
- dof
- gravity_torques: to not require jacobians for all links
- mass matrix: for first control input (!= 0)
- bucket lin&ang jac: for soil model force compensation
- base pos world, quat
- 4x box pos world
- bucket pos world: for soil model
- bucket bottom plate unit vector world: for soil model
"""


class ResetCache:
    def __init__(self, excavation_env):
        self.env = excavation_env
        self.device = self.env.device
        # No Pitch randomization here
        reset_pickle_file = os.path.join(
            MOLEWORKS_RSC_DIR, "excavation/model/m545_arm_spread_merged/cached_reset/cached_reset_deep_flat_sim_unit_quaternion.pkl"
        )  # cached_reset_deep_flat

        with open(reset_pickle_file, "rb") as f:
            self.data = pkl.load(f)

        for key, value in self.data.items():
            self.data[key] = torch.tensor(value, device=self.device)

        self.num_data = self.data["dof_pos"].shape[0]

        # sort wrt bucket height
        _, sorting_idx = torch.sort(self.data["bucket_pos"][:, 2], dim=0)
        for key, value in self.data.items():
            self.data[key] = value[sorting_idx]

        self.bucket_height_cont = self.data["bucket_pos"][:, 2].contiguous()  # for searchsorted

    def get_sampling_idx(self, rejected):
        """computes lower and upper index to sample from per env
        - gets min and max soil height
        - computes min and max allowed bucket height
        - finds upper and lower indices in sorted bucket height array
        """

        if self.env.cfg.reset.narrow.enable:
            bucket_height_min = self.env.cfg.reset.narrow.z_min * torch.ones(len(rejected), device=self.device)
            bucket_height_max = self.env.cfg.reset.narrow.z_max * torch.ones(len(rejected), device=self.device)
        else:
            min_soil_height, max_soil_height = self.env.soil.get_min_max_soil_height(rejected)
            bucket_height_min = min_soil_height - self.env.cfg.reset.arm_depth_limits.upper * (
                1.0 - self.env.cfg.reset.only_above_soil
            )
            bucket_height_max = max_soil_height - self.env.cfg.reset.arm_depth_limits.lower
        # searchsorted returns 1 past the array if not found
        lower = torch.clip(
            torch.searchsorted(self.bucket_height_cont, bucket_height_min, side="left"), max=self.num_data - 1
        )
        upper = torch.clip(
            torch.searchsorted(self.bucket_height_cont, bucket_height_max, side="right"), max=self.num_data - 1
        )

        return lower, upper

    def generate_random_idxs(self, num_samples):
        """
        - generate random indices
        - select rows in data matrix (n_samples x num_data)
        - select is the same as slicing, returns a view of the tensor
        """
        # high: 1 above the highest integer to be drawn
        return torch.randint(0, self.num_data, (num_samples,), device=self.device)
