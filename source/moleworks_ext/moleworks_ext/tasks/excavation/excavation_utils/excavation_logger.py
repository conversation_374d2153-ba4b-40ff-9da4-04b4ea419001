"""
Logger designed to log data of 1 environment when played.
"""

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from moleworks_ext.tasks.excavation.excavation_env_3d import ExcavationEnv

import csv
import matplotlib.pyplot as plt
import numpy as np
import os
import pickle as pkl
import subprocess  # For opening files
import sys  # For platform check
from collections import defaultdict
from matplotlib.gridspec import GridSpec
from cycler import cycler  # Import cycler for color palettes

from moleworks_ext.common.utils.utils import get_log_dict, multipage

# Configure Matplotlib settings
plt.rcParams["figure.max_open_warning"] = 0
plt.rcParams["figure.figsize"] = [10, 5]  # Default figure size

# Define and set a color-blind friendly palette
colorblind_palette = [
    '#0072B2',  # Blue
    '#E69F00',  # Orange
    '#56B4E9',  # Sky Blue
    '#F0E442',  # Yellow
    '#009E73',  # Bluish Green
    '#D55E00',  # Vermillion
    '#CC79A7',  # Purple
    '#999999'   # Grey
]
plt.rcParams['axes.prop_cycle'] = cycler(color=colorblind_palette)


class Logger:
    def __init__(
        self,
        env: ExcavationEnv,
        num_steps,
        robot_idx=0,
        use_custom_bucket_state_axes=False,
        title=None,
    ):
        self.env = env
        self.N = num_steps
        self.idx = robot_idx
        self.num_episodes = 0

        self.episode_step = 0
        self.episode_steps = np.zeros(self.N) * np.nan
        self.reset_indices = []  # Track steps where a reset occurred

        self.rew_log = defaultdict(list)

        self.joint_names = ["boom", "STICK", "tele", "pitch"]

        self.bucket_pos_w_x = np.zeros(self.N) * np.nan
        self.bucket_pos_w_y = np.zeros(self.N) * np.nan
        self.bucket_pos_w_z = np.zeros(self.N) * np.nan

        self.bucket_top_ang_to_hori = np.zeros(self.N) * np.nan
        self.bucket_aoa = np.zeros(self.N) * np.nan
        self.bp_angle_w = np.zeros(self.N) * np.nan

        self.pullup_dist = np.zeros(self.N) * np.nan
        self.pullup_band = np.zeros(self.N) * np.nan

        self.bucket_max_fill_area = np.zeros(self.N) * np.nan
        self.bucket_fill_area = np.zeros(self.N) * np.nan
        self.bucket_swept_area = np.zeros(self.N) * np.nan

        self.soil_height_w = np.zeros(self.N) * np.nan
        self.max_depth_height_w = np.zeros(self.N) * np.nan
        self.soil_rs = np.zeros(self.N) * np.nan
        self.soil_edge_rs = np.zeros(self.N) * np.nan
        self.soil_plate_rs = np.zeros(self.N) * np.nan
        self.soil_deadload = np.zeros(self.N) * np.nan
        self.soil_bucket_vel_cos = np.zeros(self.N) * np.nan

        self.bp_unit_vector_w = np.zeros((self.N, 3)) * np.nan

        self.bucket_lin_vel_w_x = np.zeros(self.N) * np.nan
        self.bucket_lin_vel_w_y = np.zeros(self.N) * np.nan
        self.bucket_lin_vel_w_z = np.zeros(self.N) * np.nan

        self.j_pitch_pos_x = np.zeros(self.N) * np.nan
        self.j_pitch_pos_y = np.zeros(self.N) * np.nan
        self.j_pitch_pos_z = np.zeros(self.N) * np.nan

        self.j_pitch_lin_vel_w_x = np.zeros(self.N) * np.nan
        self.j_pitch_lin_vel_w_y = np.zeros(self.N) * np.nan
        self.j_pitch_lin_vel_w_z = np.zeros(self.N) * np.nan

        self.j_pitch_quat_w = np.zeros(self.N) * np.nan
        self.j_pitch_quat_x = np.zeros(self.N) * np.nan
        self.j_pitch_quat_y = np.zeros(self.N) * np.nan
        self.j_pitch_quat_z = np.zeros(self.N) * np.nan

        self.j_pitch_ang_vel_x = np.zeros(self.N) * np.nan
        self.j_pitch_ang_vel_y = np.zeros(self.N) * np.nan
        self.j_pitch_ang_vel_z = np.zeros(self.N) * np.nan

        self.bucket_lin_vel_norm = np.zeros(self.N) * np.nan
        self.bucket_ang_vel_w_y = np.zeros(self.N) * np.nan

        self.base_lin_vel_w_x = np.zeros(self.N) * np.nan
        self.base_lin_vel_w_y = np.zeros(self.N) * np.nan
        self.base_lin_vel_w_z = np.zeros(self.N) * np.nan
        self.base_ang_vel_w_x = np.zeros(self.N) * np.nan
        self.base_ang_vel_w_y = np.zeros(self.N) * np.nan
        self.base_ang_vel_w_z = np.zeros(self.N) * np.nan

        self.dof_pos = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.dof_vel = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.actions = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.actions_clipped_scaled = np.zeros((self.N, len(self.joint_names))) * np.nan

        self.dof_torque = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.dof_gravity = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.dof_inertial = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.dof_soil = np.zeros((self.N, len(self.joint_names))) * np.nan

        self.torque_limit_lower = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.torque_limit_upper = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.vel_limit_lower = np.zeros((self.N, len(self.joint_names))) * np.nan
        self.vel_limit_upper = np.zeros((self.N, len(self.joint_names))) * np.nan

        self.w_r_pe = np.zeros((self.N, 3)) * np.nan

        self.obs = get_log_dict(self.env.observation_manager.policy_ob_dict, self.N)
        self.rews = get_log_dict(self.env.reward_manager.one_step_rew, self.N)

        # only log latest, as its already summed up in env
        self.terms_neg = get_log_dict(
            self.env.termination_excavation.episode_neg_term_counts, 1, dim=1
        )
        self.terms_pos = get_log_dict(
            self.env.termination_excavation.episode_pos_term_counts, 1, dim=1
        )

        # bucket state axes
        self.bucket_state_ax = None
        if use_custom_bucket_state_axes:
            plt.rcParams["figure.figsize"] = [20, 10]
            fig = plt.figure(constrained_layout=True)  # Use constrained_layout for better spacing
            if title:
                plt.suptitle(title)
            self.gs = GridSpec(4, 2, figure=fig)
            self.bucket_state_ax = plt.subplot(
                self.gs.new_subplotspec((0, 0), colspan=1, rowspan=3)
            )

    def log_states(self, i, obs_policy):
        # keep track of step in episode
        if self.env.reset_buf[self.idx]:
            self.episode_step = 0
            self.reset_indices.append(i)  # Log step number where reset happens
        else:
            self.episode_step += 1

        self.episode_steps[i] = self.episode_step

        # Only plot soil state at the beginning and end of episodes
        # This reduces clutter while still showing the initial and final states
        if i == 0 or (self.env.reset_buf[self.idx] and i > 0):
            self.env.soil.plot_state(
                [self.idx], show=False, label=(i == 0), ax=self.bucket_state_ax
            )

        self.bucket_pos_w_x[i] = self.env.m545_measurements.bucket_pos_w[
            self.idx, 0
        ].item()
        self.bucket_pos_w_y[i] = self.env.m545_measurements.bucket_pos_w[
            self.idx, 1
        ].item()
        self.bucket_pos_w_z[i] = self.env.m545_measurements.bucket_pos_w[
            self.idx, 2
        ].item()

        self.pullup_dist[i] = self.env.pullup_dist[self.idx].item()
        self.pullup_band[i] = self.env.curriculum_excavation.curr_pullup_band.item()

        self.bucket_top_ang_to_hori[i] = self.env.soil.get_bucket_full_angle_w()[
            self.idx
        ].item()
        self.bucket_aoa[i] = self.env.m545_measurements.bucket_aoa[self.idx].item()
        self.bp_angle_w[i] = self.env.soil.get_bucket_bp_angle_w()[self.idx].item()

        self.bucket_max_fill_area[i] = self.env.soil.get_max_fill_area().item()
        self.bucket_fill_area[i] = self.env.soil.get_fill_area()[self.idx].item()
        self.bucket_swept_area[i] = self.env.soil.get_swept_area()[self.idx].item()

        # soil
        bucket_x_pos = self.env.m545_measurements.bucket_pos_w[self.idx, 0].view(1, 1)
        y_pos_zero = torch.zeros_like(
            bucket_x_pos
        )  # Create y=0 tensor with same shape and device
        self.soil_height_w[i] = self.env.soil.get_soil_height_at_pos(
            bucket_x_pos, y_pos_zero, env_ids=[self.idx]
        ).item()
        self.max_depth_height_w[i] = self.env.soil.get_max_depth_height_at_pos(
            bucket_x_pos, y_pos_zero, env_ids=[self.idx]
        ).item()

        # Check if soil and forces objects exist before accessing attributes
        if self.env.soil and hasattr(self.env.soil, "forces") and self.env.soil.forces:
            self.soil_rs[i] = self.env.soil.forces.Rs[self.idx].item()
            self.soil_plate_rs[i] = self.env.soil.forces.plate_Rs[self.idx].item()
            self.soil_edge_rs[i] = self.env.soil.forces.edge_Rs[self.idx].item()
            self.soil_deadload[i] = self.env.soil.forces.deadload_F[self.idx].item()
        else:
            # Log default values if forces object is not available
            self.soil_rs[i] = 0.0
            self.soil_plate_rs[i] = 0.0
            self.soil_edge_rs[i] = 0.0
            self.soil_deadload[i] = 0.0

        # Check for bucket velocity cosine
        vel_cos_value = 0.0  # Default value
        if self.env.soil:
            try:
                vel_cos_tensor = self.env.soil.get_bucket_vel_cos()
                # Check if tensor is valid and index exists
                if vel_cos_tensor is not None and vel_cos_tensor.numel() > self.idx:
                    vel_cos_value = vel_cos_tensor[self.idx].item()
            except AttributeError:
                # Handle cases where get_bucket_vel_cos or intermediate attributes might be missing
                pass  # Keep default value 0.0

        self.soil_bucket_vel_cos[i] = vel_cos_value

        # bucket
        self.bp_unit_vector_w[i] = (
            self.env.m545_measurements.bp_unit_vector_w[self.idx].cpu().numpy()
        )
        self.bucket_lin_vel_w_x[i] = self.env.m545_measurements.bucket_vel_w[
            self.idx, 0
        ].item()
        self.bucket_lin_vel_w_y[i] = self.env.m545_measurements.bucket_vel_w[
            self.idx, 1
        ].item()
        self.bucket_lin_vel_w_z[i] = self.env.m545_measurements.bucket_vel_w[
            self.idx, 2
        ].item()
        self.bucket_lin_vel_norm[i] = self.env.m545_measurements.bucket_vel_norm[
            self.idx
        ].item()

        self.bucket_ang_vel_w_y[i] = self.env.m545_measurements.j_pitch_ang_vel[
            self.idx, 1
        ].item()

        self.j_pitch_pos_x[i] = self.env.m545_measurements.j_pitch_pos[
            self.idx, 0
        ].item()
        self.j_pitch_pos_y[i] = self.env.m545_measurements.j_pitch_pos[
            self.idx, 1
        ].item()
        self.j_pitch_pos_z[i] = self.env.m545_measurements.j_pitch_pos[
            self.idx, 2
        ].item()

        self.j_pitch_quat_w[i] = self.env.m545_measurements.j_pitch_quat[
            self.idx, 0
        ].item()
        self.j_pitch_quat_x[i] = self.env.m545_measurements.j_pitch_quat[
            self.idx, 1
        ].item()
        self.j_pitch_quat_y[i] = self.env.m545_measurements.j_pitch_quat[
            self.idx, 2
        ].item()
        self.j_pitch_quat_z[i] = self.env.m545_measurements.j_pitch_quat[
            self.idx, 3
        ].item()

        self.j_pitch_ang_vel_x[i] = self.env.m545_measurements.j_pitch_ang_vel[
            self.idx, 0
        ].item()
        self.j_pitch_ang_vel_y[i] = self.env.m545_measurements.j_pitch_ang_vel[
            self.idx, 1
        ].item()
        self.j_pitch_ang_vel_z[i] = self.env.m545_measurements.j_pitch_ang_vel[
            self.idx, 2
        ].item()

        self.j_pitch_lin_vel_w_x[i] = self.env.m545_measurements.j_pitch_vel[
            self.idx, 0
        ].item()
        self.j_pitch_lin_vel_w_y[i] = self.env.m545_measurements.j_pitch_vel[
            self.idx, 1
        ].item()
        self.j_pitch_lin_vel_w_z[i] = self.env.m545_measurements.j_pitch_vel[
            self.idx, 2
        ].item()

        # base state
        self.base_lin_vel_w_x[i] = self.env.m545_measurements.root_lin_vel_w[
            self.idx, 0
        ].item()
        self.base_lin_vel_w_y[i] = self.env.m545_measurements.root_lin_vel_w[
            self.idx, 1
        ].item()
        self.base_lin_vel_w_z[i] = self.env.m545_measurements.root_lin_vel_w[
            self.idx, 2
        ].item()
        self.base_ang_vel_w_x[i] = self.env.m545_measurements.root_ang_vel_w[
            self.idx, 0
        ].item()
        self.base_ang_vel_w_y[i] = self.env.m545_measurements.root_ang_vel_w[
            self.idx, 1
        ].item()
        self.base_ang_vel_w_z[i] = self.env.m545_measurements.root_ang_vel_w[
            self.idx, 2
        ].item()

        # dofs
        self.dof_pos[i] = self.env.m545_measurements.joint_pos[self.idx].cpu().numpy()
        self.dof_vel[i] = self.env.m545_measurements.joint_vel[self.idx].cpu().numpy()
        self.actions[i] = self.env.actions[self.idx].cpu().numpy()
        self.actions_clipped_scaled[i] = (
            self.env.clipped_scaled_actions[self.idx].cpu().numpy()
        )

        self.dof_torque[i] = self.env.torques[self.idx].cpu().numpy()
        self.dof_gravity[i] = (
            self.env.m545_measurements.gravity_tau[self.idx].cpu().numpy()
        )
        self.dof_inertial[i] = self.env.inertial_tau[self.idx].cpu().numpy()
        self.dof_soil[i] = (
            (self.env.ext_f_tau[self.idx] + self.env.ext_m_tau[self.idx]).cpu().numpy()
        )

        self.torque_limit_lower[i] = (
            self.env.limits.curr_torque_limit_lower[self.idx].cpu().numpy()
        )
        self.torque_limit_upper[i] = (
            self.env.limits.curr_torque_limit_upper[self.idx].cpu().numpy()
        )
        self.vel_limit_lower[i] = (
            self.env.limits.curr_vel_limit_lower[self.idx].cpu().numpy()
        )
        self.vel_limit_upper[i] = (
            self.env.limits.curr_vel_limit_upper[self.idx].cpu().numpy()
        )

        self.w_r_pe[i] = self.env.m545_measurements.w_r_pe[self.idx].cpu().numpy()

        # rew
        for key in self.rews:
            reward_tensor = self.env.reward_manager.one_step_rew[key]
            # Check if the tensor is 0-dim (scalar) for single env case
            if reward_tensor.ndim == 0:
                reward_value = reward_tensor
            else:
                reward_value = reward_tensor[self.idx]
            self.rews[key][i] = reward_value.cpu().numpy()

        # obs: Use the passed obs_policy tensor
        for name_idx, ob_name in enumerate(
            self.env.observation_manager.active_terms["policy"]
        ):
            # Get the slice corresponding to the current observation term
            obs_slice = obs_policy[
                ...,  # Handle potential env dimension
                self.env.observation_manager.dims_cumsum[
                    name_idx
                ] : self.env.observation_manager.dims_cumsum[name_idx + 1],
            ]

            # If obs_policy was 2D (from multiple envs originally), select the correct env
            if obs_slice.ndim > 1:
                current_obs = obs_slice[self.idx]
            else:  # Otherwise it's already 1D for the single env case
                current_obs = obs_slice

            self.obs[ob_name][i] = current_obs.cpu().numpy()

    def save_state_to_csv(self, file_path):
        """Saves the logged state history to a CSV file."""
        try:
            with open(file_path, mode="w", newline="") as csv_file:
                csv_writer = csv.writer(csv_file)

                # Create headers dynamically
                headers = (
                    [f"dof_pos_{i}" for i in range(len(self.joint_names))]
                    + [f"dof_vel_{i}" for i in range(len(self.joint_names))]
                    + [
                        "bucket_pos_x",
                        "bucket_pos_y",
                        "bucket_pos_z",
                        "bucket_vel_x",
                        "bucket_vel_y",
                        "bucket_vel_z",
                        "bucket_vel_norm",
                        "bucket_ang_vel_y", "bp_angle_w", "bucket_aoa", "bucket_top_ang_to_hori",
                    ]
                    + ["j_pitch_pos_x", "j_pitch_pos_y", "j_pitch_pos_z"]
                    + ["j_pitch_lin_vel_w_x", "j_pitch_lin_vel_w_y", "j_pitch_lin_vel_w_z"]
                    + [
                        "j_pitch_quat_w",
                        "j_pitch_quat_x",
                        "j_pitch_quat_y",
                        "j_pitch_quat_z",
                    ]
                    + ["j_pitch_ang_vel_x", "j_pitch_ang_vel_y", "j_pitch_ang_vel_z"]
                    + ["w_r_pe_x", "w_r_pe_y", "w_r_pe_z"]
                    + [f"action_cipped_scaled{i}" for i in range(len(self.joint_names))]
                    + [f"tau_{i}" for i in range(len(self.joint_names))]
                    + [f"inertial_tau{i}" for i in range(len(self.joint_names))]
                    + [f"gravity_tau{i}" for i in range(len(self.joint_names))]
                    + [f"soil_tau{i}" for i in range(len(self.joint_names))]
                    + ["episode_step"]  # Added episode step
                )
                csv_writer.writerow(headers)

                # Write data rows
                for i in range(self.N):
                    row = (
                        [self.dof_pos[i, j] for j in range(len(self.joint_names))]
                        + [self.dof_vel[i, j] for j in range(len(self.joint_names))]
                        + [
                            self.bucket_pos_w_x[i],
                            self.bucket_pos_w_y[i],
                            self.bucket_pos_w_z[i],
                            self.bucket_lin_vel_w_x[i],
                            self.bucket_lin_vel_w_y[i],
                            self.bucket_lin_vel_w_z[i],
                            self.bucket_lin_vel_norm[i],
                            self.bucket_ang_vel_w_y[i], self.bp_angle_w[i], self.bucket_aoa[i], self.bucket_top_ang_to_hori[i],
                        ]
                        + [
                            self.j_pitch_pos_x[i],
                            self.j_pitch_pos_y[i],
                            self.j_pitch_pos_z[i],
                        ]
                        + [
                            self.j_pitch_lin_vel_w_x[i],
                            self.j_pitch_lin_vel_w_y[i],
                            self.j_pitch_lin_vel_w_z[i],
                        ]
                        + [
                            self.j_pitch_quat_w[i],
                            self.j_pitch_quat_x[i],
                            self.j_pitch_quat_y[i],
                            self.j_pitch_quat_z[i],
                        ]
                        + [
                            self.j_pitch_ang_vel_x[i],
                            self.j_pitch_ang_vel_y[i],
                            self.j_pitch_ang_vel_z[i],
                        ]
                        + [self.w_r_pe[i, 0], self.w_r_pe[i, 1], self.w_r_pe[i, 2]]
                        + [
                            self.actions_clipped_scaled[i, j]
                            for j in range(len(self.joint_names))
                        ]
                        + [self.dof_torque[i, j] for j in range(len(self.joint_names))]
                        + [self.dof_inertial[i, j] for j in range(len(self.joint_names))]
                        + [self.dof_gravity[i, j] for j in range(len(self.joint_names))]
                        + [self.dof_soil[i, j] for j in range(len(self.joint_names))]
                        + [self.episode_steps[i]]  # Added episode step
                    )
                    # Replace potential NaNs with empty strings for cleaner CSV
                    row_str = [str(x) if not np.isnan(x) else '' for x in row]
                    csv_writer.writerow(row_str)

            print(f"Data saved to {file_path}")
        except Exception as e:
            print(f"Error saving state data to CSV: {e}")

    def log_dones(self):
        # terminations, only log latest, env sums it already
        for key in self.terms_neg:
            self.terms_neg[key][0] = (
                self.env.termination_excavation.episode_neg_term_counts[key][self.idx]
                .cpu()
                .numpy()
            )
        for key in self.terms_pos:
            self.terms_pos[key][0] = (
                self.env.termination_excavation.episode_pos_term_counts[key][self.idx]
                .cpu()
                .numpy()
            )

    def plot_states(self, show=False):
        self._plot_bucket_state()  # must be called first, because it adds to the env.soil._plot_state
        self._plot_rewards()
        self._plot_terminations()
        self._plot_fill_state()
        self._plot_soil_forces()
        self._plot_bucket_vel()
        self._plot_base_vel()
        self._plot_obs()
        self._plot_dof_vel()
        self._plot_dof_pos()
        self._plot_dof_torques()
        self._plot_episode_step()  # Plot episode steps

        if show:
            plt.show(block=False)  # Use False to keep script running

    def plot_states_multi_tester(self, show=False):
        self._plot_bucket_state()  # must be called first, because it adds to the env.soil._plot_state
        self._plot_fill_state(make_subplot=True)
        self._plot_dof_torques(make_subplot=True)

        if show:
            plt.show(block=False)

    def log_rewards(self, dict, num_episodes):
        for key, value in dict.items():
            if "rew" in key:
                self.rew_log[key].append(value.item() * num_episodes)
        self.num_episodes += num_episodes

    def print_rewards(self):
        print("Average rewards per episode:")
        total_sum_rew = 0
        for key, values in self.rew_log.items():
            if values:  # Ensure list is not empty
                mean = np.sum(np.array(values)) / self.num_episodes
                print(f" - {key}: {mean:.4f}")
                if "sum" not in key.lower() and "total" not in key.lower():
                    total_sum_rew += mean
            else:
                print(f" - {key}: No data")

        # Check if a total/sum reward key exists
        total_key = next((k for k in self.rew_log if "total" in k.lower() or "sum" in k.lower()), None)
        if total_key and self.rew_log[total_key]:
            mean_total = np.sum(np.array(self.rew_log[total_key])) / self.num_episodes
            print(f"Average {total_key}: {mean_total:.4f}")
        else:
            print(f"Average total reward (sum of terms): {total_sum_rew:.4f}")

        print(f"Total number of episodes: {self.num_episodes}")

    def save_plots(self, save_path, file_name="plots.pdf", open_file=False):
        filename = os.path.join(save_path, file_name)
        try:
            # Ensure the save directory exists
            os.makedirs(save_path, exist_ok=True)
            # Use the multipage utility to save all open figures
            multipage(filename)
            print(f"Plots saved to {filename}")
            if open_file:
                try:
                    # Try to open the file using the default system viewer
                    if sys.platform == 'win32':  # Windows
                        os.startfile(filename)
                    elif sys.platform == 'darwin':  # macOS
                        subprocess.call(('open', filename))
                    else:  # Linux/other Unix
                        subprocess.call(('xdg-open', filename))
                except Exception as e:
                    print(f"Could not open file '{filename}': {e}")
        except Exception as e:
            print(f"Error saving plots to PDF: {e}")
        finally:
            # Close all figures to free memory
            plt.close("all")

    def save_for_ros_plotter(self, save_path):
        data = {}
        data["control_steps"] = self.episode_steps
        # data["time_stamps"] =  time_stamps
        # data["ob_dim"] =  ob_dim,

        data["obs"] = np.empty(
            (self.N, self.env.observation_manager.dims_cumsum[-1]), dtype=np.float64
        )

        policy_obs_names = self.env.observation_manager.active_terms["policy"]
        for name_idx, ob_name in enumerate(policy_obs_names):
            data["obs"][
                :,
                self.env.observation_manager.dims_cumsum[
                    name_idx
                ] : self.env.observation_manager.dims_cumsum[name_idx + 1],
            ] = self.obs[ob_name]

        # Joint data
        data["joint_pos"] = self.dof_pos
        data["joint_vel"] = self.dof_vel
        data["joint_vel_limits_lower"] = self.vel_limit_lower
        data["joint_vel_limits_upper"] = self.vel_limit_upper
        data["joint_torque"] = self.dof_torque
        data["joint_torque_limits_lower"] = self.torque_limit_lower
        data["joint_torque_limits_upper"] = self.torque_limit_upper

        # Bucket data
        bucket_pos_xz = np.hstack((self.bucket_pos_w_x.reshape(-1, 1),
                                  self.bucket_pos_w_z.reshape(-1, 1)))
        data["bucket_pos_w"] = np.insert(bucket_pos_xz, 1, 0.0, axis=1)
        data["bucket_pos_gac"] = data["bucket_pos_w"]  # Assuming same for now

        bucket_vel_xz = np.hstack((self.bucket_lin_vel_w_x.reshape(-1, 1),
                                  self.bucket_lin_vel_w_z.reshape(-1, 1)))
        data["bucket_vel_w"] = np.insert(bucket_vel_xz, 1, 0.0, axis=1)
        data["bucket_vel_gac"] = data["bucket_vel_w"]  # Assuming same

        data["bucket_ang_vel_gac"] = self.bucket_ang_vel_w_y  # Ang vel around Y

        # Other data
        data["cabin_pitch_rate"] = self.base_ang_vel_w_y  # Base angular vel around Y
        data["vel_command"] = self.actions_clipped_scaled  # Processed actions
        data["action"] = self.actions  # Raw actions
        data["swept_area"] = self.bucket_swept_area
        data["fill_area"] = self.bucket_fill_area
        data["max_fill_area"] = self.bucket_max_fill_area
        data["bucket_ang_vel_diff"] = self.bucket_aoa  # Angle of attack
        data["soil_height_w"] = self.soil_height_w  # At bucket x-pos
        data["soil_height_base"] = self.soil_height_w  # Assuming same frame
        data["bottom_plate_unit_vecor_w"] = self.bp_unit_vector_w  # Already (N, 3)
        data["bottom_plate_unit_vecor_gac"] = self.bp_unit_vector_w  # Assuming same
        data["max_depth_w"] = self.max_depth_height_w  # At bucket x-pos
        data["max_depth_base"] = self.max_depth_height_w  # Assuming same

        # Save to file
        os.makedirs(save_path, exist_ok=True)
        output_filepath = os.path.join(save_path, "data.pkl")
        try:
            with open(output_filepath, "wb") as f:
                pkl.dump(data, f)
            print(f"Data saved for ROS plotter: {output_filepath}")
        except Exception as e:
            print(f"Error saving data for ROS plotter: {e}")

    """
    plots
    """

    def _add_reset_lines(self, ax=None):
        """Adds vertical dashed lines to indicate episode resets on the given axes."""
        if ax is None:
            ax = plt.gca()  # Get current axes if none provided
        # Get current plot limits to set line height appropriately
        ylim = ax.get_ylim()
        # Check if ylim is valid (not NaN) before plotting
        if not np.isnan(ylim).any():
            for reset_idx in self.reset_indices:
                ax.plot([reset_idx, reset_idx], ylim, linestyle='--', color='k', alpha=0.6, linewidth=1)
            ax.set_ylim(ylim)  # Restore original y-limits
        else:
            # Fallback if limits are NaN
            for reset_idx in self.reset_indices:
                ax.axvline(reset_idx, linestyle='--', color='k', alpha=0.6, linewidth=1)

    def _plot_bucket_state(self):
        """Plot the bucket tip path and key states.

        This method plots the bucket tip path in the World X-Z plane, along with
        velocity and angle information. The x-axis represents the World X dimension
        rather than time steps.
        """
        if self.bucket_state_ax is None:
            plt.figure(figsize=(10, 6))  # Larger figure for better visibility
            # soil plotted into current axes
            ax = plt.gca()
            ax.set_title("Bucket Tip Path & Key States", fontsize=14)
            ax.set_xlabel("World X Position (m)", fontsize=12)
            ax.set_ylabel("World Z Position (m) / Other Values", fontsize=12)
        else:
            ax = self.bucket_state_ax
            # Title is likely set outside if using custom axes

        # Get soil model's x_min and x_max for setting axis limits
        try:
            x_min = self.env.cfg.soil_height.x_min
            x_max = self.env.cfg.soil_height.x_max
        except (AttributeError, KeyError):
            # Fallback to default values if not available
            x_min, x_max = 0, 8

        # Set x-axis limits to match the World X dimension
        ax.set_xlim(x_min, x_max)

        # Create segments for each episode (between resets)
        reset_indices = [0] + self.reset_indices + [len(self.bucket_pos_w_x)]
        reset_indices = sorted(list(set(reset_indices)))  # Remove duplicates and sort

        # Plot each episode segment with a different color/style
        episode_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

        for i in range(len(reset_indices) - 1):
            start_idx = reset_indices[i]
            end_idx = reset_indices[i+1]

            if start_idx >= end_idx or start_idx >= len(self.bucket_pos_w_x):
                continue

            # Get segment data
            x_segment = self.bucket_pos_w_x[start_idx:end_idx]
            z_segment = self.bucket_pos_w_z[start_idx:end_idx]

            # Plot segment path
            color = episode_colors[i % len(episode_colors)]
            ax.plot(
                x_segment,
                z_segment,
                label=f"Episode {i+1} Path" if i == 0 else None,  # Only label first episode
                color=color,
                linewidth=2,
            )

            # Mark start and end points
            ax.scatter(
                x_segment[0],
                z_segment[0],
                marker="o",
                s=80,
                color=color,
                label="Start Point" if i == 0 else None,
                zorder=5,
                edgecolor='black',
            )

            ax.scatter(
                x_segment[-1],
                z_segment[-1],
                marker="x",
                s=80,
                color=color,
                label="End Point" if i == 0 else None,
                zorder=5,
                linewidth=2,
            )

        # Secondary axis for other states vs X position
        ax2 = ax.twinx()
        ax2.set_ylabel("Velocity (m/s) / Angle (rad)", fontsize=12)

        # Plot velocity with transparency to avoid cluttering
        ax2.plot(
            self.bucket_pos_w_x,
            self.bucket_lin_vel_norm,
            label="Tip Linear Velocity (m/s)",
            color='#ff7f0e',  # Orange
            linestyle="--",
            alpha=0.7,
            linewidth=1.5,
        )

        # Threshold velocity
        vel_threshold = self.env.cfg.terminations_excavation.max_bucket_vel
        ax2.axhline(
            vel_threshold,
            label=f"Max Velocity Threshold ({vel_threshold:.2f} m/s)",
            color='#ff7f0e',
            linestyle=":",
            linewidth=1.5,
        )

        # Plot angles with less visual emphasis
        ax2.plot(
            self.bucket_pos_w_x,
            self.bucket_top_ang_to_hori,
            label="Bucket Angle to Horizontal (rad)",
            color='#2ca02c',  # Green
            linestyle="-.",
            alpha=0.6,
            linewidth=1,
        )

        # Add reset markers as vertical lines at the reset x-positions
        for reset_idx in self.reset_indices:
            if reset_idx < len(self.bucket_pos_w_x):
                reset_x_pos = self.bucket_pos_w_x[reset_idx]
                ax.axvline(
                    reset_x_pos,
                    linestyle='--',
                    color='k',
                    alpha=0.4,
                    linewidth=1,
                    label="Episode Reset" if reset_idx == self.reset_indices[0] else None
                )

        # Add soil height profile if available
        try:
            # Get soil height at current timestep
            soil_x = self.env.soil.soil_height.x.cpu().numpy()
            soil_z = self.env.soil.soil_height.z[self.idx].cpu().numpy()

            # Plot soil profile
            ax.plot(
                soil_x,
                soil_z,
                label="Soil Surface",
                color='#8c6d3f',  # Brown
                linewidth=2,
                alpha=0.7,
            )

            # Plot max depth profile
            max_depth_x = self.env.soil.max_depth_height.x.cpu().numpy()
            max_depth_z = self.env.soil.max_depth_height.z[self.idx].cpu().numpy()

            ax.plot(
                max_depth_x,
                max_depth_z,
                label="Max Digging Depth",
                color='#d95f02',  # Orange
                linewidth=2,
                alpha=0.7,
            )
        except (AttributeError, IndexError):
            # Skip if soil data is not available
            pass

        # Combine legends
        lines, labels = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines + lines2, labels + labels2, loc='upper left', fontsize='small')

        # Add grid for better readability
        ax.grid(True, linestyle='--', alpha=0.6)

    def _plot_fill_state(self, make_subplot=False):
        if make_subplot:
            ax = plt.subplot(self.gs.new_subplotspec((3, 0), colspan=1, rowspan=1))
            ax.set_title("Bucket Fill State", fontsize=9)
            ax.tick_params(axis="both", which="major", labelsize=8)
        else:
            plt.figure()
            ax = plt.gca()
            ax.set_title("Bucket Fill State Over Time")
            ax.set_xlabel("Time Step")
            ax.set_ylabel("Area (m^2)")  # Assuming area units

        ax.plot(self.bucket_max_fill_area, label="Max Fill Area Potential")
        ax.plot(self.bucket_fill_area, label="Current Fill Area")
        ax.plot(self.bucket_swept_area, label="Swept Soil Area")

        ax.legend(fontsize="small")
        ax.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines(ax)

    def _plot_soil_forces(self):
        plt.figure()
        plt.title("Estimated Soil Resistance Forces")
        plt.xlabel("Time Step")
        plt.ylabel("Scaled Force / Cosine Angle")

        # Determine scale based on max resultant force
        max_rs = np.nanmax(np.abs(self.soil_rs))
        if max_rs > 0 and max_rs > 1000:  # Only scale if forces are large
            force_scale = 10**(-np.floor(np.log10(max_rs)))  # e.g., 1e-4, 1e-3
        else:
            force_scale = 1.0

        label_suffix = f" (x{force_scale:.0e})" if force_scale != 1.0 else ""

        plt.plot(self.soil_rs * force_scale, label=f"Total Resistance{label_suffix}")
        plt.plot(self.soil_edge_rs * force_scale, label=f"Edge Resistance{label_suffix}")
        plt.plot(self.soil_plate_rs * force_scale, label=f"Plate Resistance{label_suffix}")
        plt.plot(self.soil_deadload * force_scale, label=f"Deadload Force{label_suffix}")
        plt.plot(self.soil_bucket_vel_cos, label="Bucket Vel Cosine Angle")

        plt.grid(True, linestyle='--', alpha=0.6)
        plt.legend(fontsize="small")
        self._add_reset_lines()

    def _plot_bucket_vel(self):
        plt.figure()
        plt.title("Bucket & Pitch Joint Velocities")
        plt.xlabel("Time Step")
        plt.ylabel("Velocity (m/s or rad/s)")
        plt.plot(self.bucket_lin_vel_norm, label="Bucket Tip Linear Vel Norm (m/s)")
        plt.plot(
            self.bucket_lin_vel_w_x, label="Bucket Tip Linear Vel X (m/s)", alpha=0.7
        )
        plt.plot(
            self.bucket_lin_vel_w_z, label="Bucket Tip Linear Vel Z (m/s)", alpha=0.7
        )
        plt.plot(
            self.j_pitch_lin_vel_w_x,
            label="Pitch Joint Linear Vel X (m/s)",
            linestyle=":",
            alpha=0.7,
        )
        plt.plot(
            self.j_pitch_lin_vel_w_z,
            label="Pitch Joint Linear Vel Z (m/s)",
            linestyle=":",
            alpha=0.7,
        )
        plt.plot(self.bucket_ang_vel_w_y, label="Bucket Pitch Angular Vel Y (rad/s)")
        plt.legend(fontsize="small")
        plt.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines()

    def _plot_base_vel(self):
        plt.figure()
        plt.title("Robot Base Velocities (World Frame)")
        plt.xlabel("Time Step")
        plt.ylabel("Velocity (m/s or rad/s)")
        plt.plot(self.base_lin_vel_w_x, label="Base Linear Vel X (m/s)")
        plt.plot(self.base_lin_vel_w_y, label="Base Linear Vel Y (m/s)")
        plt.plot(self.base_lin_vel_w_z, label="Base Linear Vel Z (m/s)")
        plt.plot(self.base_ang_vel_w_x, label="Base Angular Vel X (rad/s)")
        plt.plot(self.base_ang_vel_w_y, label="Base Angular Vel Y (rad/s)")
        plt.plot(self.base_ang_vel_w_z, label="Base Angular Vel Z (rad/s)")
        plt.legend(fontsize="small")
        plt.grid(True)

    def _plot_rewards(self):
        plt.figure(figsize=(12, 6))
        plt.title("Reward Components Over Time")
        plt.xlabel("Time Step")
        plt.ylabel("Reward Value")

        # Mapping for better reward labels
        reward_labels = {
            "bucket_velocity_penalty": "Bucket Velocity Penalty",
            "soil_spilling_penalty": "Soil Spilling Penalty",
            "action_rate": "Action Rate",
            "constant": "Constant",
            "bucket_filling": "Bucket Filling",
            "max_depth_tracking": "Max Depth Tracking",
            "bucket_curl": "Bucket Curl",
            "pitch_up": "Pitch Up",
            "bucket_edge_up": "Bucket Edge Up",
            "bucket_edge_down": "Bucket Edge Down",
            "power": "Power",
            "bucket_curl_and_up": "Bucket Curl and Up",
            "termination_reward": "Termination Reward",
        }

        total_reward = np.zeros(self.N)

        for key, value in self.rews.items():
            # Use mapped label if available, otherwise use the key
            label = reward_labels.get(key, f"rew: {key}")

            # Handle different reward shapes
            if isinstance(value, np.ndarray):
                # Check shape and reshape if needed
                if value.ndim == 2:
                    if value.shape[0] == self.N and value.shape[1] == 1:
                        # Shape (N, 1) - flatten to (N,)
                        plot_value = value.flatten()
                    elif value.shape[0] == self.N and value.shape[1] > 1:
                        # Shape (N, M) - use first column
                        print(f"[Warning] Reward '{key}' has shape {value.shape}. Using first column for plotting.")
                        plot_value = value[:, 0]
                    else:
                        # Other shapes - try to adapt
                        print(f"[Warning] Reward '{key}' has unexpected shape {value.shape}. Attempting to reshape.")
                        try:
                            # Try to reshape to match N
                            if value.size >= self.N:
                                plot_value = value.flatten()[:self.N]
                            else:
                                # Pad with zeros if too small
                                plot_value = np.pad(value.flatten(), (0, self.N - value.size), 'constant')
                        except Exception as e:
                            print(f"[Error] Failed to reshape reward '{key}': {e}")
                            continue
                else:
                    # 1D array
                    if len(value) == self.N:
                        plot_value = value
                    else:
                        # Try to adapt
                        print(f"[Warning] Reward '{key}' has length {len(value)}, expected {self.N}. Attempting to adapt.")
                        try:
                            if len(value) > self.N:
                                plot_value = value[:self.N]
                            else:
                                plot_value = np.pad(value, (0, self.N - len(value)), 'constant')
                        except Exception as e:
                            print(f"[Error] Failed to adapt reward '{key}': {e}")
                            continue
            else:
                # Not an array - skip
                print(f"[Warning] Reward '{key}' is not an array. Skipping.")
                continue

            # Plot the reward
            plt.plot(plot_value, label=label)

            # Add to total reward
            total_reward += np.nan_to_num(plot_value)

        # Plot the sum
        plt.plot(
            total_reward,
            label="Total Reward (Sum)",
            linestyle="--",
            linewidth=2,
            color="black",
        )

        plt.grid(True, linestyle='--', alpha=0.6)
        plt.legend(fontsize="small", loc="upper left")
        self._add_reset_lines()

    def _plot_terminations(self):
        values = []
        labels = []
        colors = []  # For color-coding positive vs negative terminations

        # Mapping for better termination labels
        term_labels = {
            "timeout": "Timeout",
            "max_depth": "Max Depth Reached",
            "min_height": "Min Height Reached",
            "velocity": "Velocity Limit Exceeded",
            "joint_limit": "Joint Limit Reached",
            "torque_limit": "Torque Limit Reached",
            # Add others as needed
        }

        for key, value in self.terms_neg.items():
            values.append(value.item())  # value is likely a 1-element array
            labels.append(f"Neg: {term_labels.get(key, key)}")
            colors.append(colorblind_palette[5])  # Vermillion for negative

        for key, value in self.terms_pos.items():
            values.append(value.item())  # value is likely a 1-element array
            labels.append(f"Pos: {term_labels.get(key, key)}")
            colors.append(colorblind_palette[4])  # Bluish Green for positive

        fig, ax = plt.subplots()
        ax.set_title("Termination Counts by Reason (Last Episode)")
        ax.set_ylabel("Count")
        # Apply general tick parameters
        ax.tick_params(axis="x", which="major", labelsize=8)
        # Create bar chart with colors
        ax.bar(np.arange(len(values)), values, tick_label=labels, color=colors)
        # Now set rotation and alignment for the generated x-tick labels
        ax.set_xticklabels(labels, rotation=45, ha="right")
        ax.grid(axis="y")  # Grid only on y-axis for bar chart
        fig.tight_layout()  # Adjust layout

    def _plot_obs(self):
        for key, value in self.obs.items():
            plt.figure()
            # Check if value is multi-dimensional (e.g., joint data)
            if value.ndim > 1 and value.shape[1] > 1:
                num_dims = value.shape[1]
                plt.title(
                    f"Observation: {key.replace('_', ' ').title()} (per Dimension)"
                )
                plt.xlabel("Time Step")
                plt.ylabel("Observation Value")
                for i in range(num_dims):
                    # Try to get a specific label if possible (e.g., joint names)
                    dim_label = f"Dim {i}"
                    if key == "joint_pos" and i < len(self.joint_names):
                        dim_label = f"{self.joint_names[i]} Pos"
                    elif key == "joint_vel" and i < len(self.joint_names):
                        dim_label = f"{self.joint_names[i]} Vel"
                    # Add other specific labels if needed
                    plt.plot(value[:, i], label=dim_label)
                plt.legend(fontsize="small")
            else:
                # Single dimension observation
                plt.title(f"Observation: {key.replace('_', ' ').title()}")
                plt.xlabel("Time Step")
                plt.ylabel("Observation Value")
                plt.plot(value, label=key)

            plt.grid(True, linestyle='--', alpha=0.6)
            self._add_reset_lines()

    def _plot_dof_pos(self):
        for i, name in enumerate(self.joint_names):
            plt.figure()
            plt.title(f"DOF Position: {name.title()}")
            plt.xlabel("Time Step")
            plt.ylabel("Position (rad or m)")  # Units depend on joint type
            plt.plot(self.dof_pos[:, i], label="Measured Position")
            # Plotting limits from env.limits (runtime attribute)
            lower_limit = self.env.limits.pos_limits_lower[i].item()
            upper_limit = self.env.limits.pos_limits_upper[i].item()
            plt.plot(
                np.linspace(0, self.N - 1, self.N),  # Create x-axis array
                np.full(self.N, lower_limit),  # Create array of lower limits
                label=f"Lower Limit ({lower_limit:.2f})",
                color="red",
                linestyle="--",
                marker="x",
                markersize=3,
                markevery=max(1, self.N // 20),  # Mark every ~20 steps
            )
            plt.plot(
                np.linspace(0, self.N - 1, self.N),
                np.full(self.N, upper_limit),
                label=f"Upper Limit ({upper_limit:.2f})",
                color="red",
                linestyle="--",
                marker="x",
                markersize=3,
                markevery=max(1, self.N // 20),
            )
            plt.legend(fontsize="small")
            plt.grid(True, linestyle='--', alpha=0.6)
            self._add_reset_lines()

    def _plot_dof_vel(self):
        for i, name in enumerate(self.joint_names):
            plt.figure()
            plt.title(f"DOF Velocity & Action: {name.title()}")
            plt.xlabel("Time Step")
            plt.ylabel("Velocity (rad/s or m/s) / Action")
            plt.plot(self.dof_vel[:, i], label="Measured Velocity")
            # Plotting limits from env.limits (runtime attribute)
            lower_vel_limit = self.env.vel_limits_lower[i].item()
            upper_vel_limit = self.env.vel_limits_upper[i].item()
            plt.plot(
                np.linspace(0, self.N - 1, self.N),
                np.full(self.N, lower_vel_limit),
                label=f"Lower Vel Limit ({lower_vel_limit:.2f})",
                color="red",
                linestyle="--",
                marker="x",
                markersize=3,
                markevery=max(1, self.N // 20),
            )
            plt.plot(
                np.linspace(0, self.N - 1, self.N),
                np.full(self.N, upper_vel_limit),
                label=f"Upper Vel Limit ({upper_vel_limit:.2f})",
                color="red",
                linestyle="--",
                marker="x",
                markersize=3,
                markevery=max(1, self.N // 20),
            )
            # Plotting dynamic limits logged during simulation
            plt.plot(self.vel_limit_lower[:, i], label="Dyn. Lower Limit", linestyle=":", color="orange")
            plt.plot(self.vel_limit_upper[:, i], label="Dyn. Upper Limit", linestyle=":", color="orange")
            plt.plot(self.actions[:, i], label="Raw Action", alpha=0.7, linestyle=":")
            plt.plot(
                self.actions_clipped_scaled[:, i],
                label="Scaled Action",
                alpha=0.7,
                linestyle="-",
            )
            plt.legend(fontsize="small")
            plt.grid(True, linestyle='--', alpha=0.6)
            self._add_reset_lines()

    def _plot_dof_torques(self, make_subplot=False):
        for i, name in enumerate(self.joint_names):
            if make_subplot:
                # Create subplot within the grid defined in __init__
                ax = plt.subplot(
                    self.gs.new_subplotspec((i % 4, 1), colspan=1, rowspan=1)
                )
                ax.set_title(f"DOF Torque: {name.title()}", fontsize=9)
                ax.tick_params(axis="both", which="major", labelsize=8)
            else:
                plt.figure()
                ax = plt.gca()
                ax.set_title(f"DOF Torque Components: {name.title()}")
                ax.set_xlabel("Time Step")
                ax.set_ylabel("Torque (Nm)")

            ax.plot(self.dof_torque[:, i], label="Applied Torque")
            # Plot dynamic limits logged during simulation
            ax.plot(
                self.torque_limit_lower[:, i],
                label="Dyn. Lower Limit",
                linestyle=":",
                color="red",
            )
            ax.plot(
                self.torque_limit_upper[:, i],
                label="Dyn. Upper Limit",
                linestyle=":",
                color="red",
            )
            # Plotting components (negated as they represent resistance)
            ax.plot(-self.dof_gravity[:, i], label="Gravity Comp. (-)", alpha=0.7)
            ax.plot(-self.dof_inertial[:, i], label="Inertial Comp. (-)", alpha=0.7)
            ax.plot(-self.dof_soil[:, i], label="Soil Comp. (-)", alpha=0.7)
            ax.legend(fontsize="small")
            ax.grid(True, linestyle='--', alpha=0.6)
            self._add_reset_lines(ax)

    def _plot_episode_step(self):
        plt.figure()
        plt.title("Episode Step Counter")
        plt.xlabel("Simulation Time Step")
        plt.ylabel("Current Episode Step")
        plt.plot(self.episode_steps, label="Steps within current episode")
        plt.legend(fontsize="small")
        plt.grid(True, linestyle='--', alpha=0.6)
        self._add_reset_lines()
