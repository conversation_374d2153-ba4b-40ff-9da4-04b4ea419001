from __future__ import annotations

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .soil_3d import Soil3D

import numpy as np
import torch

class SSP3D:
    """Secondary Separation Plate (SSP) in 3D.

    This class represents the secondary separation plate in 3D, which is the
    surface along which soil separates during excavation.
    """

    def __init__(self, soil_model: Soil3D, cfg):
        """Initialize the 3D SSP.

        Args:
            soil_model: The parent soil model
            cfg: Configuration object
        """
        self.SM = soil_model
        self.n_envs = self.SM.n_envs
        self.device = self.SM.device
        self.cfg = cfg.ssp
        self.bucket = soil_model.bucket_state

        """
        Constants
        """
        self.alpha_poly_coeffs = torch.tensor(
            self.cfg.ssp_angle_poly_coeffs, device=self.device
        )
        self.dL_max = torch.tensor(self.cfg.dL_max, device=self.device)
        self.L_over_max = torch.tensor(self.cfg.L_over_max, device=self.device)
        self.L_over_max_discretized = torch.arange(
            0, self.L_over_max + 1e-5, self.dL_max, device=self.device
        )
        self.L_over_max_discretized_expanded = self.L_over_max_discretized.expand(
            self.n_envs, -1
        )
        self.L_min = torch.zeros(self.n_envs, 1, device=self.device)
        self.zero_vec = torch.zeros(self.n_envs, 1, device=self.device)

        """
        SSP geometry
        """
        # SSP angle to bottom plate
        self.alpha = torch.zeros(self.n_envs, 1, device=self.device)

        # Angle to soil surface
        self.beta = torch.zeros(self.n_envs, 1, device=self.device)

        # Max length to the back of the bucket
        self.L_max = torch.zeros(self.n_envs, 1, device=self.device)

        # Length in contact with soil
        self.L = torch.zeros(self.n_envs, 1, device=self.device)

        # Max length for valid c_star / phi_start computation
        self.L_max_no_bucket = torch.zeros(self.n_envs, 1, device=self.device)

        # Centroid from shovel edge
        self.centroid_w_x = torch.zeros(self.n_envs, 1, device=self.device)
        self.centroid_w_y = torch.zeros(self.n_envs, 1, device=self.device)
        self.centroid_w_z = torch.zeros(self.n_envs, 1, device=self.device)
        self.centroid_pos_w = torch.zeros(self.n_envs, 3, device=self.device)

        # Helper variables for geometry calculations
        self.gamma = torch.zeros(self.n_envs, 1, device=self.device)
        self.beta2 = torch.zeros(self.n_envs, 1, device=self.device)
        self.x = torch.zeros(self.n_envs, 1, device=self.device)
        self.b = torch.zeros(self.n_envs, 1, device=self.device)
        self.delta = torch.zeros(self.n_envs, 1, device=self.device)
        self.n = torch.zeros(self.n_envs, 1, device=self.device)
        self.m = torch.zeros(self.n_envs, 1, device=self.device)
        self.h = torch.zeros(self.n_envs, 1, device=self.device)
        self.A1 = torch.zeros(self.n_envs, 1, device=self.device)
        self.B1 = torch.zeros(self.n_envs, 1, device=self.device)
        self.B2 = torch.zeros(self.n_envs, 1, device=self.device)
        self.B = torch.zeros(self.n_envs, 1, device=self.device)

        # Angle variables
        self.ssp_angle_to_horizon = torch.zeros(self.n_envs, 1, device=self.device)
        self.ssp_unit_vector_w = torch.zeros(self.n_envs, 3, device=self.device)
        self.soil_alpha = torch.zeros(self.n_envs, 1, device=self.device)
        self.soil_alpha_y = torch.zeros(self.n_envs, 1, device=self.device)

        # Initial update
        self.update()

    def update(self):
        """Update the SSP state."""
        with torch.profiler.record_function("SSP3D.update"):
            self._update_ssp_angles(self.bucket.fill_area)
            self._update_helper_vars()
            self._update_max_L()
            self._update_L_and_angle_to_soil()
            self._update_centroid()

    def _update_centroid(self):
        """Compute the centroid of the soil in the bucket.

        The bucket is decomposed into 3 different shapes.
        It's computed first in the bucket bottom plate (frame in the bucket edge
        pointing x along the plate).
        """
        y = torch.where(
            self.h > 1e-5,
            2.0 * self.bucket.r * torch.sin(self.h / 2.0) / (1.5 * self.h),
            self.zero_vec,
        )
        # Shape 1 is a circular sector
        x1 = self.bucket.a + torch.sin(self.h / 2.0) * y
        z1 = self.bucket.r - torch.cos(self.h / 2.0) * y
        x2 = 2.0 * self.bucket.a / 3.0
        z2 = self.x / 3.0
        hc = torch.sin(self.h) * self.bucket.r
        xi = torch.cos(self.h) * self.bucket.r
        x3 = self.bucket.a + hc / 3.0
        z3 = self.bucket.r - (xi + self.b) / 3.0

        # The centroid is computed by a weighted average of the centroids of the 3 shapes
        centroid_bucket_x = torch.where(
            self.B > 1.0e-4,
            (x1 * self.B2 - x3 * self.B1 + x2 * self.A1) / self.B,
            self.zero_vec,
        )
        centroid_bucket_z = torch.where(
            self.B > 1.0e-4,
            (z1 * self.B2 - z3 * self.B1 + z2 * self.A1) / self.B,
            self.zero_vec,
        )
        # Set y-coordinate to 0 in bucket frame (assuming symmetric bucket)
        centroid_bucket_y = torch.zeros_like(centroid_bucket_x)

        # Rotate from bucket into world frame
        bucket_cos = torch.cos(self.bucket.bp_angle_to_horizon)
        bucket_sin = torch.sin(self.bucket.bp_angle_to_horizon)

        # Use 3D bucket position
        bucket_pos_w = self.SM.bucket_pos_w

        # X and Z components rotation and translation
        self.centroid_w_x = (
            bucket_cos * centroid_bucket_x -
            bucket_sin * centroid_bucket_z +
            bucket_pos_w[:, 0].unsqueeze(-1)
        )
        self.centroid_w_z = (
            bucket_sin * centroid_bucket_x +
            bucket_cos * centroid_bucket_z +
            bucket_pos_w[:, 2].unsqueeze(-1)
        )
        # Y component just gets translated (no rotation in XZ plane)
        self.centroid_w_y = centroid_bucket_y + bucket_pos_w[:, 1].unsqueeze(-1)

        # Combine into 3D position
        self.centroid_pos_w = torch.cat(
            (self.centroid_w_x, self.centroid_w_y, self.centroid_w_z), dim=-1
        )

    def _update_L_and_angle_to_soil(self):
        """Update the SSP length and angle to soil.

        This method:
        1. Discretizes L by searching along a line longer than max SSP
        2. Finds intersection with soil
        3. Finds slope of soil at the intersection
        """
        with torch.profiler.record_function("SSP3D._update_L_and_angle_to_soil"):
            # Get bucket position in 3D
            bucket_pos_w = self.SM.bucket_pos_w

            # Generate unit vector in 3D
            # For now, we'll assume the SSP extends in the XZ plane only
            ssp_unit_vector_3d = torch.zeros(self.n_envs, 3, device=self.device)
            ssp_unit_vector_3d[:, 0] = torch.cos(self.ssp_angle_to_horizon).squeeze(-1)
            ssp_unit_vector_3d[:, 2] = torch.sin(self.ssp_angle_to_horizon).squeeze(-1)

            # Generate positions along the SSP
            dL_pos_w = bucket_pos_w.view(self.n_envs, 1, 3) + self.L_over_max_discretized.view(
                -1, 1
            ) * ssp_unit_vector_3d.view(self.n_envs, 1, 3)

            # Extract components of discretized points
            dL_x_w = dL_pos_w[..., 0]  # Shape: [n_envs, n_points]
            dL_y_w = dL_pos_w[..., 1]  # Shape: [n_envs, n_points]
            dL_z_w = dL_pos_w[..., 2]  # Height in 3D space, shape: [n_envs, n_points]

            # Get soil heights at discretized positions - fully vectorized approach
            # Use the SM.get_soil_height_at_point method directly - it handles tensors of shape [n_envs, n_points]
            dL_soil_heights_w = self.SM.get_soil_height_at_pos(dL_x_w, dL_y_w)

            # Find where the SSP intersects with the soil surface
            # Create a mask where dL_z_w is above soil height (dL_z_w >= dL_soil_heights_w)
            above_soil_mask = dL_z_w >= dL_soil_heights_w

            # Find the first point where the SSP exits the soil (transitions from below to above)
            # We need to handle cases where:
            # 1. SSP starts below soil and exits (normal case)
            # 2. SSP starts above soil (not in soil at all)
            # 3. SSP stays below soil for all sampled points (need to extend sampling)

            # For case 1: Find the first True in each row
            # For case 2: max_idxs will be 0
            # For case 3: max_val will be False/0
            max_val, max_idxs = torch.max(above_soil_mask, dim=1, keepdim=True)

            # Handle corner cases
            # If max_val is 0 (False), SSP never exits soil within our sampling range
            # In this case, use the maximum L value as a fallback
            max_idxs = torch.where(
                max_val == 0,  # Using 0 instead of False to avoid comparison to boolean
                self.L_over_max_discretized.shape[0] - 1,
                max_idxs
            )

            # Special case: If the first point is already above soil (not in soil at all)
            # Check if the first point is already above soil
            first_point_above = above_soil_mask[:, 0].unsqueeze(1)
            # If first point is above soil, set max_idxs to 0 (minimum L)
            max_idxs = torch.where(first_point_above, torch.zeros_like(max_idxs), max_idxs)

            self.L = torch.gather(self.L_over_max_discretized_expanded, 1, max_idxs)

            # Clip to actually feasible max L
            self.L = torch.clip(self.L, self.L_min, self.L_max)

            # Get coordinates where SSP exits soil
            x_exit = torch.gather(dL_x_w, 1, max_idxs)
            y_exit = torch.gather(dL_y_w, 1, max_idxs)

            # Get soil angle where SSP exits soil
            soil_angles_x, soil_angles_y = self.SM.get_soil_angle_at_pos(x_exit, y_exit)

            # In a true 3D model, we should consider both x and y components of the soil angle
            # For now, we primarily use the x angle component but store both for future use
            self.soil_alpha = soil_angles_x

            # Store y-component for potential future use
            # This could be used to adjust forces in the y-direction
            self.soil_alpha_y = soil_angles_y

            self.beta = self.ssp_angle_to_horizon - self.soil_alpha

    def _update_max_L(self):
        """Update the maximum SSP length."""
        with torch.profiler.record_function("SSP3D._update_max_L"):
            # L max, if shovel was only triangle
            self.L_max_no_bucket = self.bucket.a / torch.cos(self.alpha)

            # L max, with cylindrical part
            self.L_max = self.L_max_no_bucket + self.bucket.r * torch.sin(
                self.h
            ) / torch.sin(self.m)

    def _update_helper_vars(self):
        """Update helper variables for centroid calculation."""
        with torch.profiler.record_function("SSP3D._update_helper_vars"):
            self.gamma = np.pi / 2.0 - self.alpha
            self.beta2 = np.pi - self.gamma
            self.x = self.bucket.a * torch.tan(self.alpha)
            self.b = self.bucket.r - self.x
            self.delta = torch.asin(
                torch.clamp(self.b / self.bucket.r * torch.sin(self.beta2), -1, 1)
            )
            self.n = np.pi - self.gamma
            self.m = np.pi - self.n
            self.h = np.pi - self.m - self.delta

            # Areas for deadload centroid calculation
            self.A1 = 0.5 * self.x * self.bucket.a
            self.B1 = 0.5 * self.b * self.bucket.r * torch.sin(self.h)
            self.B2 = 0.5 * self.bucket.r * self.bucket.r * self.h
            self.B = self.A1 + self.B2 - self.B1

    def _update_ssp_angles(self, bucket_fill_area):
        """Update the SSP angle to bottom plate.

        Uses Horner's rule with highest degree polynomial coefficient first.
        """
        with torch.profiler.record_function("SSP3D._update_ssp_angles"):
            self.alpha[:] = 0
            for i in range(len(self.alpha_poly_coeffs) - 1):
                self.alpha = bucket_fill_area * (self.alpha + self.alpha_poly_coeffs[i])
            self.alpha += self.alpha_poly_coeffs[i + 1]

            # Update SSP angle to world and get unit vector (for soil intersection computation)
            self.ssp_angle_to_horizon = self.bucket.bp_angle_to_horizon + self.alpha

            # Create 3D unit vector
            cos_angle = torch.cos(self.ssp_angle_to_horizon)
            sin_angle = torch.sin(self.ssp_angle_to_horizon)
            zero = torch.zeros_like(cos_angle)

            self.ssp_unit_vector_w = torch.cat(
                (cos_angle, zero, sin_angle), dim=-1
            )