# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from .soil import Soil

import torch

#from omni.isaac.orbit_tasks.gep.excavation_utils.helpers import sample_params_within_limits_skewed


class SoilParameters:
    """
    - sample random soil parameters (including idxed version)
    - set fixed soil parameters (for all the same)
    """

    def __init__(self, soil_model: "Soil", cfg):
        self.SM = soil_model
        self.n_envs = self.SM.n_envs
        self.device = self.SM.device
        self.cfg = cfg.soil_parameters
        """
        soil params:
        0: c, 1: ca_f, 2: phi, 3: gamma, 4: delta, 5: CP


        from soil:
        6: alpha

        derived soil params:
        7: K0 = 1-sin(phi)
        8: ca = ca_f * c

        """
        self.n_params = 9
        self.n_params_to_sample = 6
        self.params = torch.zeros(self.n_envs, self.n_params, device=self.device)
        self.limits = torch.tensor(
            [
                [0.0, 0.0, 0.3, 1.7e4, 0.19, 0.0],
                [1.0e5, 1.0, 0.8, 2.2e4, 0.38, 300.0],
            ],
            device=self.device,
        )

        # references
        self.c = self.params[:, 0].unsqueeze(-1)
        self.ca_f = self.params[:, 1].unsqueeze(-1)
        self.phi = self.params[:, 2].unsqueeze(-1)
        self.gamma = self.params[:, 3].unsqueeze(-1)
        self.delta = self.params[:, 4].unsqueeze(-1)
        self.CP = self.params[:, 5].unsqueeze(-1)

        self.alpha = self.params[:, 6].unsqueeze(-1)
        self.K0 = self.params[:, 7].unsqueeze(-1)
        self.ca = self.params[:, 8].unsqueeze(-1)  # ca can only be as large as c, therefore we sample factor of c [0,1]

        self.sample()

    def update(self):
        # account for flipped/mirrored soil model implementation
        with torch.profiler.record_function("SoilParameters.update"):
            self.alpha[:] = -self.SM.ssp.soil_alpha

    def sample(self, idxs=...):
        if self.cfg.type == "random":
            self._sample_random(idxs)
        elif self.cfg.type == "skewed":
            self._sample_skeewed(idxs)
        else:
            soil_type = eval("self.cfg." + self.cfg.type)
            self.c[idxs] = getattr(soil_type, 'c', 0)
            self.ca_f[idxs] = getattr(soil_type, 'ca_f', 0)
            self.phi[idxs] = getattr(soil_type, 'phi', 0.55)
            self.gamma[idxs] = getattr(soil_type, 'gamma', 19500)
            self.delta[idxs] = getattr(soil_type, 'delta', 0.4)
            self.CP[idxs] = getattr(soil_type, 'CP', 1)
        # derived soil params, always from terrain

        # soil alpha is computed in ssp, because it is the angle of the soil where ssp exits soil

        self.params[idxs, self.n_params_to_sample + 1] = (1.0 - torch.sin(self.phi[idxs])).squeeze()
        self.params[idxs, self.n_params_to_sample + 2] = (self.ca_f[idxs] * self.c[idxs]).squeeze()

    def _sample_random(self, idxs):
        if idxs == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(idxs)
        # sample random soil params within limits
        self.params[idxs, : self.n_params_to_sample] = self.limits[0] + (self.limits[1] - self.limits[0]) * torch.rand(
            dim0, self.n_params_to_sample, device=self.device
        )
"""
    def _sample_skeewed(self, idxs):
        if idxs == ...:
            dim0 = self.n_envs
        else:
            dim0 = len(idxs)

        self.params[idxs, self.n_params_to_sample + 1] = (1.0 - torch.sin(self.phi[idxs])).squeeze()
        self.params[idxs, self.n_params_to_sample + 2] = (self.ca_f[idxs] * self.c[idxs]).squeeze()
        # sample random soil params within limits
        self.params[idxs, : self.n_params_to_sample] = sample_params_within_limits_skewed(
            self.limits[0].unsqueeze(0),
            self.limits[1].unsqueeze(0),
            dim0,
            self.cfg.alpha,
            self.cfg.beta,
            device=self.device,
        )
        # print("sampled params: ", self.params[idxs, : self.n_params_to_sample])
"""