# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
    This script is designed to analyse how the excavation agent reacts when spawned (decoupled from the env).
    It is *not* intented to test terminations

"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="This script demonstrates how to simulate a bipedal robot.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments

args_cli = parser.parse_args()
# Override command line arguments
args_cli.task = 'Isaac-m545-digging'
args_cli.headless = False
# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import torch
import traceback

import carb

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation

##
# Pre-defined configs
##
from isaaclab.scene import InteractiveScene
from isaaclab.sim import SimulationContext

from moleworks_ext.tasks.excavation.env_cfg.m545_env_cfg import ExcavationSceneCfg



def main():
    """Zero actions agent with Orbit environment."""
    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device="cuda")
    sim = SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view([12, 12, 12], [0.0, 0.0, 0.0])

    # -- Spawn things into stage
    # Lights-1
    # -- Spawn things into stage, use Scence from Cfg
    scene_cfg = ExcavationSceneCfg(num_envs=1, env_spacing=10)
    scene = InteractiveScene(scene_cfg)
    robot = scene["robot"]
    physics_dt = sim.get_physics_dt()
    # Play the simulator
    sim.reset()

    # Now we are ready!
    print("[INFO]: Setup complete...")
    ep_step_count = 0
    # Simulate physics
    while simulation_app.is_running():
        # If simulation is stopped, then exit.
        if sim.is_stopped():
            break
        # If simulation is paused, then skip.
        if not sim.is_playing():
            sim.step(render=app_launcher.RENDER)
            continue
        else:
            if ep_step_count == 0:
                robot.reset()

            # Example of a force that can be applied
            const = 1e5
            force_tensor_base = torch.tensor(
                [
                    [0, 0, 0],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0, 0.0, 0],
                    [0, const, 0.0],
                ],
                device=robot.device,
            ).reshape(1, 5, 3)
            force_tensor = torch.tensor(
                [
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [0.0000e00, 0.0000e00, 0.0000e00],
                    [2.1418e04, 0.0000e00, 7.1892e03],
                ],
                device=robot.device,
            ).reshape(1, 5, 3)
            moment_tensor = torch.tensor(
                [
                    [0.0000, 0.0000, 0.0000],
                    [0.0000, 0.0000, 0.0000],
                    [0.0000, 0.0000, 0.0000],
                    [0.0000, 0.0000, 0.0000],
                    [0.0000, -16739.1543, 0.0000],
                ],
                device=robot.device,
            ).reshape(1, 5, 3)
            force_tensor_0 = torch.zeros_like(force_tensor)
            moment_tensor_0 = torch.zeros_like(moment_tensor)

            # Apply a force since this ep_step_count
            if ep_step_count > 300:
                robot.set_external_force_and_torque(force_tensor_base, moment_tensor_0)

            joint_pos, joint_vel = robot.data.default_joint_pos, robot.data.default_joint_vel
            robot.write_joint_state_to_sim(joint_pos, joint_vel)
            robot.write_data_to_sim()
            sim.step()
            robot.update(physics_dt)

            ep_step_count += 1


if __name__ == "__main__":
    main()
    simulation_app.close()
