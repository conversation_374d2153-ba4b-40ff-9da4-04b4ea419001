# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022, NVIDIA CORPORATION & AFFILIATES, ETH Zurich, and University of Toronto
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
    This script is designed to tune the PID gains in the inverse dynamics controller for the excavation agent.
    A lot of data are logged but most of them are very useful so those are left.
"""

"""Launch Isaac Sim Simulator first."""

import argparse
import matplotlib.pyplot as plt

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(
    description="This script demonstrates how to simulate a mobile manipulator with dummy joints."
)
parser.add_argument("--headless", action="store_true", default=False, help="Force display off at all times.")
parser.add_argument("--robot", type=str, default="Isaac-m545-digging", help="Name of the robot.")
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(headless=args_cli.headless)
simulation_app = app_launcher.app


import numpy as np
import torch

import isaacsim.core.utils.torch as torch_utils

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation, ArticulationCfg, AssetBase, AssetBaseCfg, RigidObject
from isaaclab.sim import SimulationContext
from isaaclab.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg, UsdFileCfg

from moleworks_ext.common.assets.articulation_ext_force_global import Articulation_ext_force_global
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsController, InverseDynamicsControllerCfg
from moleworks_ext.rsc.excavation.m545.m545 import M545_DOF_ARM_CFG
from moleworks_ext.rsc.single_boulder_excavation.m545.m545 import M545_DOF_ARM_W_COLLISION_SHOVEL_CFG
from moleworks_ext.tasks.excavation.excavation_env import ExcavationEnvCfg

joint_vel_limits = np.zeros((4, 2))
joint_vel_limits[0] = np.array([-0.5, 0.5])
joint_vel_limits[1] = np.array([-0.6, 0.6])
joint_vel_limits[2] = np.array([-0.4, 0.4])
joint_vel_limits[3] = np.array([-0.8, 0.8])
joint_efforts_limits = np.zeros((4, 2))
joint_efforts_limits[0] = np.array([-2e6, 2e6])
joint_efforts_limits[1] = np.array([-1e6, 1e6])
joint_efforts_limits[2] = np.array([-1e6, 1e6])
joint_efforts_limits[3] = np.array([-1e6, 1e6])


from isaaclab.terrains import TerrainImporter, TerrainImporterCfg

# Config
INV_DYN_CFG = InverseDynamicsControllerCfg(
    command_type="vel",
    k_p=[0, 0, 0, 0],
    k_d=[25, 30, 20, 20],  #  manual mass matrix: k_d=
    # 25,30,40,20, Gym: 25,30,20,20
    dof_limits=joint_vel_limits,
    dof_efforts_limits=joint_efforts_limits,
)


def main():
    """Main function."""
    sim, robot = set_simulation()

    """
    Initial pose = torch.tensor([[-0.78, 1.57, 0.009, 0.0]], device = robot.device)
            "J_BOOM": -0.78,
            "J_STICK": 1.57,
            "J_TELE": 0.009,
            "J_EE_PITCH": 0.,
    joint limit:
        joint_pos_limits[0] = np.array([-1.34, 0.44])
        joint_pos_limits[1] = np.array([0.58, 2.78])
        joint_pos_limits[2] = np.array([0.0, 1.8])
        joint_pos_limits[3] = np.array([-0.35, 2.28])

        limit_target = torch.tensor([[-1.34,  2.78,  1.8,  2.28]], device = robot.device)

        "J_BOOM": 0,
        "J_STICK": 1,
        "J_TELE": 2,
        "J_EE_PITCH": 3

    """
    # Buffer for velocity target
    veloctiy_target = torch.zeros_like(robot.data.default_joint_pos)
    # Decide wheter we follow 0 velocity or a sinus
    follow_sinus = True
    # Duration [s]
    duration = 12
    # Choose which joint to follow a sinusoid. Others will follow 0 velocity target
    joints_to_tune = ["J_BOOM", "J_DIPPER", "J_TELE", "J_EE_PITCH"]
    # Do the simulation
    do_simulation(
        sim,
        robot,
        duration,
        controller_type="inverse_dynamics",
        inverse_dyn_target_vel=veloctiy_target,
        t_oscill=100,
        t_ext_force=300,
        joints_to_tune=joints_to_tune,
        follow_sinus=follow_sinus,
    )


def set_simulation():
    # Load kit helper
    sim = SimulationContext(
        sim_utils.SimulationCfg(
            device="cuda",
            dt=0.04,
            physx=sim_utils.PhysxCfg(
                max_position_iteration_count=64,
                min_position_iteration_count=64,
                max_velocity_iteration_count=64,
                min_velocity_iteration_count=64,
            ),
        )
    )
    # Set main camera
    sim.set_camera_view([12, 12, 12], [0.0, 0.0, 0.0])

    # -- Spawn things into stage
    # Lights-1
    cfg = sim_utils.DistantLightCfg(intensity=600.0, color=(0.75, 0.75, 0.75))
    cfg.func("/World/Light/greyLight", cfg, translation=(4.5, 3.5, 10.0))
    # Robot Cfg
    robot_cfg = M545_DOF_ARM_CFG
    robot = Articulation_ext_force_global(cfg=robot_cfg.replace(prim_path="/World/Robot"))
    # Terrain
    terrain_cfg = TerrainImporterCfg(
        prim_path="/World/ground",
        terrain_type="plane",
        terrain_generator=None,
        max_init_terrain_level=5,
        collision_group=-1,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # should be 0.8
            dynamic_friction=0.8,
            restitution=0.8,
        ),
        debug_vis=False,
        env_spacing=10,
        num_envs=1,
    )
    terrain = TerrainImporter(cfg=terrain_cfg.replace(prim_path="/World/Ground"))

    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    return sim, robot


def do_simulation(
    sim,
    robot,
    duration,
    controller_type,
    inverse_dyn_target_vel,
    joints_to_tune,
    t_oscill,
    t_ext_force,
    follow_sinus=True,
):
    """
    Do the simulation. Params
        controller_type: controller type we want to use, inverse dynamics is now implememtd but another could be used here
        inverse_dyn_target_vel: Target veloctiy
        joints_to_tune: List with name of joints to tune
        t_oscill: When oscillation starts
        t_ext_force: When external force is applied
        follow_sinus: If we want the joints to follow inverse_dyn_target_vel or a sinus

    """
    # joint ids
    joint_ids, _ = robot.find_joints(joint_names)
    joint_ids = torch.tensor(joint_ids, device=robot.device)
    joint_ids_shifted = joint_ids + 6

    # dummy action
    actions_inv_dyn = torch.zeros(robot.root_physx_view.count, robot.num_joints, device=robot.device)

    # Set up controll
    inverse_dyn_controller = InverseDynamicsController(INV_DYN_CFG, robot.root_physx_view.count, robot.device)

    # Define simulation stepping
    physics_dt = sim.get_physics_dt()
    # episode counter
    sim_time = 0.0
    ep_step_count = 0
    frequency_id = 0
    decimation = 1
    num_steps_per_episode = int(duration / physics_dt / decimation)

    # Buffer
    manual_joint_vel = torch.zeros_like(robot.data.joint_pos, device=robot.device)
    previous_arm_joint_pos = torch.zeros_like(robot.data.joint_pos, device=robot.device)
    actuation_forces = robot.root_physx_view.get_dof_actuation_forces()
    arm_joint_vel = robot.data.joint_vel

    # Logging
    data_history = torch.zeros((int(duration / physics_dt), 3, len(joint_ids)), device=robot.device)
 
    # Define joint names
    arm_joint_names = ["J_BOOM", "J_DIPPER", "J_TELE", "J_EE_PITCH"]
    arm_joint_ids = robot.find_joints(arm_joint_names)[0]
    bucket_body_idx = robot.find_bodies("ROTO_BASE")
    # Simulate physics
    while simulation_app.is_running():
        # If simulation is stopped, then exit.
        if sim.is_stopped():
            break
        else:
            if ep_step_count == 0:
                joint_pos, joint_vel = robot.data.default_joint_pos, robot.data.default_joint_vel
                robot.write_joint_state_to_sim(joint_pos, joint_vel)
                root_state = robot.data.default_root_state
                robot.write_root_state_to_sim(root_state)

            else:
                if ep_step_count % num_steps_per_episode == 0:
                    frequency_id += 1
                    # Tracking error for inverse_dynamics
                    if controller_type == "inverse_dynamics" or controller_type == "set_dof_velocity_targets":
                        plot_trajectories(data_history[:, 0], data_history[:, 1])
                    # Show the plots-
                    plt.show()
                    if frequency_id == len(frequencies):
                        frequency_id = 0
                        actions_inv_dyn = robot.data.default_joint_pos
                if follow_sinus:
                    if ep_step_count < t_oscill:
                        actions_inv_dyn[:, :] = 0
                    else:
                        actions_inv_dyn[:, :] = 0
                        for i in range(len(joints_to_tune)):
                            actions_inv_dyn[:, joint_dict[joints_to_tune[i]]] = (
                                np.sin((sim_time - 100 * physics_dt) * frequencies[frequency_id] * 2 * np.pi) * 0.2
                            )
                else:
                    actions_inv_dyn[:, :] = inverse_dyn_target_vel

            # Track target position
            des_dof_pos = actions_inv_dyn[:, joint_ids]*physics_dt
            inverse_dyn_controller.set_command(actions_inv_dyn[:, joint_ids], pos_command=des_dof_pos)

            # Jacobians
            jac_arm_joint_ids = [joint_id + 6 for joint_id in arm_joint_ids]
            jacobian = robot.root_physx_view.get_jacobians()
            jac_lin = jacobian[:, :, 0:3, :]
            jac_lin_T = jac_lin.transpose(-2, -1)
            jac_rot = jacobian[:, :, 3:6, :]
            jac_rot_T = jac_rot.transpose(-2, -1)
            bucket_jac_lin_T_dof = jacobian[:, bucket_body_idx[0], 0:3, -4:].transpose(-2, -1)
            bucket_jac_rot_T_dof = jacobian[:, bucket_body_idx[0], 3:6, -4:].transpose(-2, -1)
            gravity = torch.tensor([0, 0, -9.81], device=robot.device)
            gravity_expanded = gravity.view(1, 3)
            masses = robot.root_physx_view.get_masses()[0, :].to(robot.device)
            masses_expanded = masses.view(robot.num_bodies, 1)
            gravity_forces = masses_expanded * gravity_expanded
            inertias = robot.root_physx_view.get_inertias().to(robot.device)
            inertias = torch.reshape(inertias[0], (robot.num_bodies, 3, 3))
            gravity_tau = torch.sum(
                torch.matmul(
                    jac_lin_T[:, :, jac_arm_joint_ids, :],
                    gravity_forces.unsqueeze(-1),
                ),
                dim=1,
            ).squeeze()

            mm = torch.sum(
                jac_lin_T[:, :, jac_arm_joint_ids, :].matmul(
                    masses.view(-1, 1, 1) * jac_lin[:, :, :, jac_arm_joint_ids]
                ) + jac_rot_T[:, :, jac_arm_joint_ids, :].matmul(
                    inertias.matmul(jac_rot[:, :, :, jac_arm_joint_ids])
                ),
                dim=1,
            )            
            # Coriolis and centrifugal forces
            coriolis_centrifugal_force = robot.root_physx_view.get_coriolis_and_centrifugal_forces()

            # ---------------- Force Compensation, not at step 0 -----------------------
            if ep_step_count < t_ext_force:
                # No force applied and no force compensation at the beginning
                if controller_type == "inverse_dynamics":
                    id_torques = inverse_dyn_controller.compute(
                        robot.data.joint_pos[:, joint_ids],
                        arm_joint_vel,
                        mm,
                        gravity_tau,
                        (coriolis_centrifugal_force),
                    )
                    actuation_forces[:, joint_ids] = id_torques

                    robot.set_joint_effort_target((actuation_forces), joint_ids)
            else:
                # Apply the force and compensate for it after
                print("External Force applied")
                # Force
                # Apply the force and the moment
                force_tensor = torch.tensor(
                    [
                        [0.0000e00, 0.0000e00, 0.0000e00],
                        [0.0000e00, 0.0000e00, 0.0000e00],
                        [0.0000e00, 0.0000e00, 0.0000e00],
                        [0.0000e00, 0.0000e00, 0.0000e00],
                        [2.1418e04, 0.0000e00, 7.1892e03],
                    ],
                    device=robot.device,
                ).reshape(1, 5, 3)
                moment_tensor = torch.tensor(
                    [
                        [0.0000, 0.0000, 0.0000],
                        [0.0000, 0.0000, 0.0000],
                        [0.0000, 0.0000, 0.0000],
                        [0.0000, 0.0000, 0.0000],
                        [0.0000, -16739.1543, 0.0000],
                    ],
                    device=robot.device,
                ).reshape(1, 5, 3)
                force_tensor_0 = torch.zeros_like(force_tensor)
                moment_tensor_0 = torch.zeros_like(moment_tensor)
                force_tensor_idx = torch.tensor([[2.1418e04, 0.0000e00, 7.1892e03]], device=robot.device).reshape(
                    1, 1, 3
                )
                moment_tensor_idx = torch.tensor([[0.0000, -16739.1543, 0.0000]], device=robot.device).reshape(1, 1, 3)

                robot.set_external_force_and_torque(force_tensor_idx, moment_tensor_idx, body_ids=[4])
                
                ext_f_genco_tau = torch.matmul(bucket_jac_lin_T_dof, force_tensor[0, 4].reshape([1, 3, 1]))
                external_force_comp = ext_f_genco_tau.squeeze(-1)[:, -robot.num_joints :]
                ext_m_genco_tau = torch.matmul(bucket_jac_rot_T_dof, moment_tensor[0, 4].reshape([1, 3, 1]))
                external_moment_comp = ext_m_genco_tau.squeeze(-1)[:, -robot.num_joints :]

                if controller_type == "inverse_dynamics":
                    id_torques = inverse_dyn_controller.compute(
                        robot.data.joint_pos[:, joint_ids],
                        arm_joint_vel,
                        mm,
                        gravity_tau,
                        (coriolis_centrifugal_force),
                        external_force=external_force_comp.reshape(1,4),
                        external_moment=external_moment_comp.reshape(1,4),
                    )
                    actuation_forces[:, joint_ids] = id_torques

                    print("arm torques inv dyn", id_torques)
                    robot.set_joint_effort_target((actuation_forces), joint_ids)
            # write commands to sim
            if controller_type == "set_dof_velocity_targets":
                robot.root_physx_view.set_dof_velocity_targets(actions_inv_dyn, joint_ids)
                print("set_dof_velocity_targets")

            # Write buffers in the sim
            robot.write_data_to_sim()
            # Physics stepping
            sim.step()
            # Update robot
            robot.update(physics_dt)

            # Compute velocite and chosse which one îs used
            sim_vel = robot.data.joint_vel.clone()
            manual_joint_vel = (robot.data.joint_pos - previous_arm_joint_pos) / physics_dt
            arm_joint_vel = sim_vel  # manual_joint_vel
            previous_arm_joint_pos = robot.data.joint_pos[:, joint_ids]

            data_history[ep_step_count % num_steps_per_episode, 0, :] = actions_inv_dyn[:, joint_ids].clone()
            data_history[ep_step_count % num_steps_per_episode, 1, :] = arm_joint_vel
            data_history[ep_step_count % num_steps_per_episode, 2, :] = robot.data.joint_pos[:, joint_ids].clone()

            print("Joint pos", robot.data.joint_pos)

            sim_time += physics_dt
            ep_step_count += 1

def plot_trajectories(commanded_velocities: torch.Tensor, achieved_velocities: torch.Tensor):
    """
    commanded_velocities: tensor of shape (num_steps, num_joints)
    achieved_velocities: tensor of shape (num_steps, num_joints)
    """
    fig, axs = plt.subplots(4, 1, figsize=(10, 10))
    for i in range(4):
        axs[i].plot(commanded_velocities.cpu().numpy()[:, i], label="Commanded")
        axs[i].plot(achieved_velocities.cpu().numpy()[:, i], label="Achieved")
        axs[i].set_ylim([-1, 1])
        axs[i].legend()


if __name__ == "__main__":
    frequencies = [0.5, 1, 2]  # [0.5, 1, 2]
    joint_names = ["J_BOOM", "J_DIPPER", "J_TELE", "J_EE_PITCH"]
    joint_dict = {"J_BOOM": 0, "J_DIPPER": 1, "J_TELE": 2, "J_EE_PITCH": 3}

    main()
    simulation_app.close()