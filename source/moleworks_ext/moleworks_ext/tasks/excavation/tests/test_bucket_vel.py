



# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")

parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-digging"
args_cli.num_envs = 1
args_cli.headless = True

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback

import carb

import isaaclab_tasks  # noqa: F401
from isaaclab_tasks.utils import parse_env_cfg

from moleworks_ext.tasks.excavation.excavation_utils.excavation_utils import plot_hist, plot_hist_double

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""
    # parse env configuration
    env_cfg = parse_env_cfg("Isaac-m545-digging", num_envs=args_cli.num_envs)
    env_cfg.sim.dt = 0.04
    dt = env_cfg.sim.dt
    env_cfg.reset.only_above_soil = True
    num_steps = 1000


    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)

    robot = env.unwrapped.scene["robot"]

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")

    # reset environment
    env.unwrapped.curriculum_excavation.set_level_and_update(torch.tensor([2000], device=env.unwrapped.device))
    env.reset()

    # Now we are ready!
    print("[INFO]: Setup complete...")
    print("Root state: ", robot.data.default_root_state[:, :7])

    # Simulate physics
    manual_bucket_vel_norm = torch.norm(
        (env.m545_measurements.bucket_pos_w - env.m545_measurements.prev_bucket_pos_w) / dt
    )

    # Log data
    error = []
    bucket_vel_list = []
    manual_bucket_vel_list = []

    # Disable termination for longer tracking
    env.cfg.terminations_excavation.disable_negative_termination = True

    with torch.inference_mode():
        # compute zero actions
        for i in range(num_steps):
            actions = torch.zeros(env.action_space.shape, device=env.unwrapped.device)
            env.step(actions)
            # print(env.torques)
            manual_bucket_vel_norm = torch.norm(
                (env.unwrapped.m545_measurements.bucket_pos_w[0] - env.unwrapped.m545_measurements.prev_bucket_pos_w[0])
                / dt
            )

            # Log data
            bucket_vel_list.append(env.unwrapped.m545_measurements.bucket_vel_norm[0])
            manual_bucket_vel_list.append(manual_bucket_vel_norm)
            error.append(env.unwrapped.m545_measurements.bucket_vel_norm[0] - manual_bucket_vel_norm)

    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
