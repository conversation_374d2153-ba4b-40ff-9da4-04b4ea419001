# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-digging"
args_cli.num_envs = 2
args_cli.headless = False

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback

import carb

import isaaclab_tasks  # noqa: F401
from isaaclab_tasks.utils import parse_env_cfg

from moleworks_ext.tasks.excavation.excavation_utils.excavation_utils import plot_hist, plot_hist_double

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""
    # parse env configuration
    env_cfg = parse_env_cfg(args_cli.task, num_envs=args_cli.num_envs)
    env_cfg.sim.dt = 0.04
    env_cfg.reset.only_above_soil = True

    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")

    # reset environment
    level = torch.tensor([0], device=env.device)
    env.unwrapped.curriculum_excavation.set_level_and_update(level)
    env.reset()

    # Num steps and num_reset
    num_steps = 1000
    actions = torch.zeros(env.unwrapped.num_envs, 4)

    print("INERTIA", env.m545_asset.root_physx_view.get_inertias().to(env.device)[0])
    print("Masse", env.m545_asset.root_physx_view.get_masses().to(env.device)[0])
    # run everything in inference mode
    with torch.inference_mode():

        for i in range(num_steps):
            # Reset the env
            if i % 100 == 0:
                env.reset()
                print("Reset: ", i)

            # actions[:, 0] = 0.1
            obs, rewards, dones, timeouts, infos = env.step(actions)

    # close the simulator
    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
