# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import gymnasium as gym
import builtins
import math
import numpy as np
import torch
from typing import Any, ClassVar, Dict, Sequence, Tuple

from isaacsim.core.version import get_version


from isaaclab.envs.manager_based_rl_env import ManagerBasedRLEnv, ManagerBasedRLEnvCfg
from .env_cfg.excavation_env_cfg import ExcavationEnvCfg

from isaaclab.envs.ui import ViewportCameraController

from moleworks_ext.common.utils.m545_measurements import M545Measurements
from .excavation_utils.termination_excavation import Terminations_Excavation
from .excavation_utils.curriculum_excavation import Curriculum_Excavation
from .excavation_utils.reset_cache import ResetCache
from .excavation_utils.limits import Limits

from isaaclab.utils.timer import Timer
from isaaclab.scene import InteractiveScene
from isaaclab.sim import SimulationContext

from isaaclab.envs import VecEnvStepReturn

# Replace direct soil import with factory
# from moleworks_ext.tasks.excavation.soil_model.soil import Soil
from .soil_model_factory import create_soil_model

from moleworks_ext.tasks.excavation.excavation_utils.excavation_reward_manager import ExcavationRewardManager
from isaaclab.managers.action_manager import ActionManager
from moleworks_ext.common.managers.observations.obs_with_mean import ObservationManagerWithMean


class ExcavationEnv(ManagerBasedRLEnv):
    """The class for reinforcement learning-based environments for excavation.

    This class inherits from :class:`RLTaskEnv` and implements the core functionality for
    reinforcement learning-based environments for an excavation agent. It is designed to be used with any RL
    library. This class differs from RLTaskEnv in the sense it has a pre-physics and post physics step.
    Additional measurements buffers concerning are updated and a custom soil model is used.

    Note:
        For vectorized environments, it is recommended to **only** call the :meth:`reset`
        method once before the first call to :meth:`step`, i.e. after the environment is created.
        After that, the :meth:`step` function handles the reset of terminated sub-environments.
        This is because the simulator does not support resetting individual sub-environments
        in a vectorized environment.
    """

    is_vector_env: ClassVar[bool] = True
    """Whether the environment is a vectorized environment."""
    metadata: ClassVar[dict[str, Any]] = {
        "render_modes": [None, "human", "rgb_array"],
        "isaac_sim_version": get_version(),
    }
    """Metadata for the environment."""

    cfg: ExcavationEnvCfg
    """Configuration for the environment."""

    def __init__(self, cfg: ExcavationEnvCfg, render_mode: str | None = None, **kwargs):
        '''self.m545_measurements = M545Measurements(
            num_joints=len(cfg.scene.robot.init_state.joint_pos), num_envs=cfg.scene.num_envs, device=cfg.sim.device
        )'''
        self.m545_measurements = M545Measurements(cfg=cfg,
            num_joints=len(cfg.scene.robot.init_state.joint_pos), num_envs=cfg.scene.num_envs, device=cfg.sim.device, env=self
        )
        # Environment buffers
        self.init_env_buffers(
            num_envs=cfg.scene.num_envs,
            num_joints=len(cfg.arm_joints_names),
            device=cfg.sim.device,
            cfg=cfg,
        )
        # Soil - use factory to create appropriate model
        self.soil = create_soil_model(cfg)
        # it initializes all the managers, needs to be after init_env_buffers
        super().__init__(cfg)
        # needs to be after super().__init__(cfg)
        self.m545_asset = self.scene.articulations["robot"]
        self.m545_measurements.initialize_asset()
        # load a different reward manager
        self.reward_manager = ExcavationRewardManager(self.cfg.rewards, self)
        print("[INFO] Overriding Default New Reward Manager: ", self.reward_manager)
        self.action_manager = ActionManager(self.cfg.actions, self)
        print("[INFO] Overriding Default New Reward Manager: ", self.action_manager)
        self.observation_manager = ObservationManagerWithMean(self.cfg.observations, self)
        print("[INFO] Overriding Default New Reward Manager: ", self.observation_manager)
        # Post init now that we have access to the asset and the env
        self.soil.post_init(self)
        #self.m545_measurements.post_init(self)
        # Update Static measuremetns
        self.joint_ids, self._joint_names = self.m545_asset.find_joints([".*"])

        self.reset_cache = ResetCache(self)
        self.limits = Limits(self)
        # Custom managers
        self.termination_excavation = Terminations_Excavation(self)
        self.curriculum_excavation = Curriculum_Excavation(self)

        # Initialize the markers and drawers needed for the visualization
        if self.sim.has_gui():
            # Import packages. Done here since omni_debug_draw cannot be imported if headless
            import isaacsim.util.debug_draw._debug_draw as omni_debug_draw
            from .excavation_utils.visualization_excavation import define_markers

            # Drawing tools
            self.excavation_visualizer = define_markers()
            self.excavation_draw_interface = omni_debug_draw.acquire_debug_draw_interface()

        self.reset()

    def inter_decim_step(self) -> None:
        """
        Executes one inter decimation step. This is used for debugging and not for training.
        """
        # Pre-physics step
        self.pre_physics_step()
        # set actions into buffers, both are doing the same, one is lab fashion and other Gym fashion
        self.action_manager.apply_action()
        # set actions into simulator
        self.scene.write_data_to_sim()
        # simulate
        self.sim.step(render=False)
        # Post-physics step
        self.post_physics_step()

    def step(self, action: torch.Tensor) -> VecEnvStepReturn:
        """Execute one time-step of the environment's dynamics and reset terminated environments.

        Unlike the :class:`RL_Task:Env.step` class, the function performs the following operations:

        0. Pr-Physics step
        1. Process the actions.
        2. Perform physics stepping.
        3. Perform rendering if gui is enabled.
        4. Update the environment counters and compute the rewards and terminations.
        5. Reset the environments that terminated.
        6. Compute the observations.
        7. Return the observations, rewards, resets and extras.

        Args:
            action: The actions to apply on the environment. Shape is (num_envs, action_dim).

        Returns:
            A tuple containing the observations, rewards, resets (terminated and truncated) and extras.
        """
        # Process actions
        self.actions[:] = action  # Logging
        self.action_manager.process_action(action)
        # Pre-decimation. Soil has been updated in initial reset()
        self.last_fill_ratio[:] = self.soil.get_fill_ratio()
        self.inter_decimation_soil_model_invalid[:] = False
        self.inter_decimation_self_collision[:] = False

        # check if we need to do rendering within the physics loop
        # note: checked here once to avoid multiple checks within the loop
        is_rendering = self.sim.has_gui() or self.sim.has_rtx_sensors()
        # perform physics stepping
        for _ in range(self.cfg.decimation):
            # Pre-physics step
            self.pre_physics_step()
            # set actions into buffers,
            self.action_manager.apply_action()
            # set actions into simulator
            self.scene.write_data_to_sim()
            # simulate
            self.sim.step(render=False)
            self._sim_step_counter += 1
            # render between steps only if the GUI or an RTX sensor needs it
            # note: we assume the render interval to be the shortest accepted rendering interval.
            #    If a camera needs rendering at a faster frequency, this will lead to unexpected behavior.
            if self._sim_step_counter % self.cfg.sim.render_interval == 0 and is_rendering:
                #print("sim step counter: ", self._sim_step_counter)
                #print("render interval: ", self.cfg.sim.render_interval)
                self.draw_debug_vis()
                self.sim.render()
            # Post-physics step
            self.post_physics_step()
        # Measurements updates for the one that are not needed in inter steps
        self.update_derived_measurements()


        # post-step:
        # -- update env counters (used for curriculum generation)
        self.episode_length_buf += 1  # step in current episode (per env)
        self.common_step_counter += 1  # total step (common for all envs)
        # -- check terminations
        self.reset_buf = self.termination_excavation.check_termination()
        self.reset_terminated = self.termination_excavation.terminated
        self.reset_time_outs = self.termination_excavation.time_out_buf
        # DEFAULT
        #self.reset_buf = self.termination_manager.compute()
        #self.reset_terminated = self.termination_manager.terminated
        #self.reset_time_outs = self.termination_manager.time_outs
        # -- reward computation
        self.reward_buf = self.reward_manager.compute(dt=self.step_dt)

        # -- reset envs that terminated/timed-out and log the episode information
        reset_env_ids = self.reset_buf.nonzero(as_tuple=False).squeeze(-1)
        # Invalid soils of env before reset
        not_reset_env_ids = (~self.reset_buf).nonzero(as_tuple=False).flatten()
        invalid_not_resetting_before = self.soil.is_state_invalid(not_reset_env_ids)

        # -- reset envs that terminated/timed-out and log the episode information
        if len(reset_env_ids) > 0:
            self._reset_idx(reset_env_ids)
            # if sensors are added to the scene, make sure we render to reflect changes in reset
            if self.sim.has_rtx_sensors() and self.cfg.rerender_on_reset:
                self.sim.render()
        # Check if episode_length_buf are fine
        if torch.any(self.episode_length_buf > self.common_step_counter):
            print("An element in episode_length_buf is greater than common_step_counter")
        # NOTE: Command manager not used
        # Invalid soils of env after reset
        invalid_not_resetting_after = self.soil.is_state_invalid(not_reset_env_ids)
        if (invalid_not_resetting_before != invalid_not_resetting_after).any():
            raise ValueError(print("reset of others made not resetted invalid"))
        # -- step interval randomization, not used in Excavation v0
        if "interval" in self.event_manager.available_modes:
            self.event_manager.apply(mode="interval", dt=self.step_dt)
        # Update derived measurements for resetted envs
        self.update_derived_measurements(reset_env_ids)
        # -- compute observations
        # note: done after reset to get the correct observations for reset envs
        self.obs_buf = self.observation_manager.compute()

        # Not used by lab reward
        self.last_actions = self.actions[:]
        self.last_actions[reset_env_ids] = 0

        # return observations, rewards, resets and extras
        return self.obs_buf, self.reward_buf, self.reset_terminated, self.reset_time_outs, self.extras

    def pre_physics_step(self):
        """
        Update buffers before physics step
        """
        self.set_soil_forces()

    def post_physics_step(self):
        """
        Update buffers after physics step
        """
        # Update buffers at sim dt
        self.scene.update(dt=self.physics_dt)
        # Update measurements
        self.m545_measurements.update_measurements()
        # Update the soil
        self.soil.update()
        # Check is the soil is valid
        self.inter_decimation_soil_model_invalid |= self.soil.is_state_invalid()
        # # Check collisions
        #self.inter_decimation_self_collision |= (
        #    torch.linalg.norm(self.m545_measurements.bucket_collision_f, dim=-1, keepdim=True) > 1.0
        #)

    def update_derived_measurements(self, env_ids=...):
        """
        Updates measurements that are not needed in inter-decimations step but relevant
        for rewards, curriculum, terminations and observations computations
        """
        if env_ids == ...:
            dim0 = self.num_envs
        else:
            dim0 = len(env_ids)

        if dim0 == 0:
            return

        self.m545_measurements.update_derived_measurements(env_ids, dim0)
        # TODO: modify for general turn 
        # moving in negative x direction: x, x-d, x-2d
        x_points = self.m545_measurements.bucket_pos_w[env_ids, 0:1] - self.soil_height_futures_spacing
        self.soil_height_futures[env_ids] = self.soil.get_soil_height_at_pos(x_points, env_ids)
        # print("soil_height_futures:", self.soil_height_futures)
        # print("x_points:", x_points)

        x_points = self.m545_measurements.bucket_pos_w[env_ids, 0:1] - self.max_depth_futures_spacing
        self.max_depth_futures[env_ids] = self.soil.get_max_depth_height_at_pos(x_points, env_ids)

        x_points = self.m545_measurements.bucket_pos_w[env_ids, 0:1] - self.soil_normal_futures_spacing
        self.soil_normal_futures[env_ids] = self.soil.get_soil_angle_at_pos(x_points, env_ids)

        soil_ang = self.soil.get_soil_angle_at_pos(self.m545_measurements.bucket_pos_w[env_ids, 0:1], env_ids)
        self.soil_normal_vec[env_ids, 0] = torch.cos(soil_ang + np.pi / 2.0).squeeze()
        self.soil_normal_vec[env_ids, 2] = torch.sin(soil_ang + np.pi / 2.0).squeeze()

    def init_env_buffers(self, num_envs, num_joints, device, cfg):
        """
        Initializes env buffers
        """

        # super constructor from baseenv
        self.reset_buf = torch.ones(num_envs, device=device, dtype=torch.long)

        self.max_depth_futures = torch.zeros(num_envs, cfg.observations_excavation.num_max_depth_futures, device=device)
        self.max_depth_futures_spacing = (
            torch.arange(0, cfg.observations_excavation.num_max_depth_futures, device=device)
            * cfg.observations_excavation.max_depth_futures_spacing
        )
        self.soil_height_futures = torch.zeros(
            num_envs, cfg.observations_excavation.num_soil_height_futures, device=device
        )
        self.soil_height_futures_spacing = (
            torch.arange(0, cfg.observations_excavation.num_soil_height_futures, device=device)
            * cfg.observations_excavation.soil_height_futures_spacing
        )

        self.soil_normal_futures = torch.zeros(
            num_envs, cfg.observations_excavation.num_soil_normal_futures, device=device
        )
        self.soil_normal_futures_spacing = (
            torch.arange(0, cfg.observations_excavation.num_soil_normal_futures, device=device)
            * cfg.observations_excavation.max_soil_normal_futures_spacing
        )

        self.soil_normal_vec = torch.zeros(num_envs, 3, device=device)

        # Reset/ Termination
        self.pullup_dist = torch.zeros(num_envs, device=device)
        self.pullup_dist[:] = cfg.reset.pullup_dist
        self.inter_decimation_soil_model_invalid = torch.zeros(num_envs, 1, dtype=torch.bool, device=device)
        self.inter_decimation_self_collision = torch.zeros(num_envs, 1, dtype=torch.bool, device=device)

        # m545
        # self.m545_num_rb = len(self.m545_asset.body_names)
        self.vel_limits_lower = torch.tensor(cfg.limits.velocity.lower, device=device)
        self.vel_limits_upper = torch.tensor(cfg.limits.velocity.upper, device=device)
        # this is wrong
        # self.external_force = torch.zeros(num_envs, self.m545_num_rb, 3, device=device)
        # self.bucket_force_com = self.external_force[:, self.m545_measurements.bucket_body_idx, :]
        # self.external_moment = torch.zeros(num_envs, self.m545_num_rb, 3, device=device)
        # self.bucket_moment_com = self.external_moment[:, self.m545_measurements.bucket_body_idx, :]
        self.bucket_force_com = torch.zeros(num_envs, 3, device=device)
        self.bucket_moment_com = torch.zeros(num_envs, 3, device=device)

        self.actions = torch.zeros(num_envs, num_joints, device=device)
        self.des_dof_vel = torch.zeros(num_envs, num_joints, device=device)
        self.des_dof_pos = torch.zeros(num_envs, num_joints, device=device)
        # todo: torques should be updated here
        self.torques = torch.zeros(num_envs, num_joints, device=device)
        self.inertial_tau = torch.zeros(num_envs, num_joints, device=device)
        self.ext_f_tau = torch.zeros(num_envs, num_joints, device=device)  # soil
        self.ext_m_tau = torch.zeros(num_envs, num_joints, device=device)

        # RL
        self.actions = torch.zeros(num_envs, num_joints, device=device)
        self.last_actions = torch.zeros(num_envs, num_joints, device=device)
        self.last_fill_ratio = torch.zeros(num_envs, 1, device=device)
        self.clipped_scaled_actions = torch.zeros(num_envs, num_joints, device=device)
        # Helpers
        self.zero_scalar = torch.zeros(1, device=device)
        self.zero_vec = torch.zeros(num_envs, 1, device=device)
        # Log indices from reset cache of all envs
        self.sampled = torch.zeros(num_envs, device=device).long()
        self.des_dof_vel = torch.zeros(num_envs, num_joints, device=device)
        self.des_dof_pos = torch.zeros(num_envs, num_joints, device=device)

    def set_soil_forces(self):
        self.bucket_force_com[:] = self.soil.get_resultant_force()
        self.bucket_moment_com[:] = self.soil.get_resultant_moment()

        self.bucket_force_com[self.inter_decimation_soil_model_invalid.squeeze()] = 0.0
        self.bucket_moment_com[self.inter_decimation_soil_model_invalid.squeeze()] = 0.0

        self.bucket_force_com[:] = torch.clip(self.bucket_force_com, -200000.0, 200000.0)
        self.bucket_moment_com[:] = torch.clip(self.bucket_moment_com, -200000.0, 200000.0)

        if not torch.isfinite(self.bucket_force_com).all():
            print("force nan")
        if not torch.isfinite(self.bucket_moment_com).all():
            print("moment nan")

        # Reshape forces and torques to match (num_envs, num_bodies, 3) when body_ids is a single index
        forces_reshaped = self.bucket_force_com.unsqueeze(1)
        torques_reshaped = self.bucket_moment_com.unsqueeze(1)

        body_ids = torch.tensor([self.m545_measurements.bucket_body_idx[0]], device=self.device)

        self.m545_asset.set_external_force_and_torque(
            forces_reshaped,
            torques_reshaped,
            body_ids=body_ids,
            env_ids=None
        )

    def _reset_idx(self, env_ids: Sequence[int]):
        """Reset environments based on specified indices.

        Args:
            env_ids: List of environment ids which must be reset
        """
        # ---------- Excavation Env specific

        # update the curriculum for environments that need a reset
        # self.curriculum_manager.compute(env_ids=env_ids)# NOTE: Removed here since done in algorithm loop
        self.curriculum_excavation.update_curriculum_excavation()
        # reset the internal buffers of the scene elements
        self.scene.reset(env_ids)
        # randomize the MDP for environments that need a reset, rejection sampling for excavation
        if "reset" in self.event_manager.available_modes:
            env_step_count = self._sim_step_counter // self.cfg.decimation
            self.event_manager.apply(env_ids=env_ids, mode="reset", global_env_step_count=env_step_count)

        # iterate over all managers and reset them
        # this returns a dictionary of information which is stored in the extras
        # note: This is order-sensitive! Certain things need be reset before others.
        self.extras["log"] = dict()
        # -- observation manager
        info = self.observation_manager.reset(env_ids)
        self.extras["log"].update(info)
        # -- action manager
        info = self.action_manager.reset(env_ids)
        self.extras["log"].update(info)
        # -- rewards manager
        # info = self.reward_manager.reset(env_ids) # To get data: reward over episode lenth
        info = self.reward_manager.reset_excavation(env_ids)  # To get data: reward for certain termination conition
        self.extras["log"].update(info)
        # -- curriculum manager
        # info = self.curriculum_manager.reset(env_ids) # NOTE: This is replaced by the line below
        info = self.curriculum_excavation.reset_excavation()
        self.extras["log"].update(info)
        # -- command manager
        info = self.command_manager.reset(env_ids)  # ok since empty
        self.extras["log"].update(info)
        # -- randomization manager
        info = self.event_manager.reset(env_ids)  # ok since empty
        self.extras["log"].update(info)
        # -- termination manager
        # info = self.termination_manager.reset(env_ids) # NOTE: This is replaced by the line below
        # self.extras["log"].update(dict_terms)
        info = self.termination_excavation.reset_excavation(env_ids)
        self.extras["log"].update(info)
        # -- termination manager
        #info = self.termination_manager.reset(env_ids)  # NOTE: This is replaced by the line below
        #self.extras["log"].update(info)
        # reset the episode length buffer
        self.episode_length_buf[env_ids] = 0
        # --.. Extra for Excavation
        self.reset_buf[env_ids] = 1
        # send timeout info to the algorithm
        if self.cfg.send_timeouts:
            self.extras["time_outs"] = self.termination_excavation.time_out_buf


    def reset(self, seed: int | None = None, options: dict[str, Any] | None = None) -> tuple[VecEnvObs, dict]:
        """Resets all the environments and returns observations.

        Args:
            seed: The seed to use for randomization. Defaults to None, in which case the seed is not set.
            options: Additional information to specify how the environment is reset. Defaults to None.

                Note:
                    This argument is used for compatibility with Gymnasium environment definition.
                    Excavtion: Added

        Returns:
            A tuple containing the observations and extras.
        """
        # Update the curriculum when reseted
        self.curriculum_excavation.update_curriculum_excavation()  # Excavation
        # set the seed
        if seed is not None:
            self.seed(seed)
        # reset state of scene
        indices = torch.arange(self.num_envs, dtype=torch.int64, device=self.device)
        self._reset_idx(indices)
        self.m545_measurements.update_measurements()
        self.update_derived_measurements()  # Excavation
        # TODO: what is this
        self.extras["episode_pos_term_counts"] = self.termination_excavation.episode_pos_term_counts
        self.extras["episode_neg_term_counts"] = self.termination_excavation.episode_neg_term_counts
        # return observations
        self.obs_buf = self.observation_manager.compute()
        return self.obs_buf, self.extras
    
    def draw_debug_vis(self):
        # Check if we're using a 3D soil model - it handles its own visualization
        if hasattr(self.soil, 'enable_visualization') and self.soil.enable_visualization:
            # Let the 3D soil model handle its own visualization updates
            # Still continue with bucket visualization
            pass
            
        # Markers
        zero_orientation = torch.tensor([1, 0, 0, 0], device=self.device).expand(self.num_envs, -1)

        # Bucket edge
        indices_bucket_edge = torch.zeros(self.num_envs, device=self.device)
        marker_pos_bucket_edge_locations = self.m545_measurements.bucket_pos_map
        marker_orientations_bucket_edge = zero_orientation.clone()

        # Bottom plate
        indices_bottom_plate = torch.ones(self.num_envs, device=self.device)
        marker_pos_bottom_plate_locations = (
            self.m545_measurements.bucket_pos_map + self.m545_measurements.bp_unit_vector_w * self.cfg.bucket.a
        )
        marker_orientations_bucket_edge = zero_orientation.clone()

        # -------------------------
        # Center of mass (marker index = 6)
        # -------------------------
        indices_com = torch.ones(self.num_envs, device=self.device) * 6

        # Add environment origins so it's correctly placed in the global frame
        marker_pos_com_locations = torch.stack(
            (
                self.m545_measurements.bucket_com_pos_w[:, 0] + self.scene.env_origins[:, 0],
                self.m545_measurements.bucket_com_pos_w[:, 1] + self.scene.env_origins[:, 1],
                self.m545_measurements.bucket_com_pos_w[:, 2] + self.scene.env_origins[:, 2],
            ),
            dim=1,
        )
        marker_orientations_com = zero_orientation.clone()
        # -------------------------

        # Concatenate (bucket edge + bottom plate first)
        marker_indices = torch.cat((indices_bucket_edge, indices_bottom_plate), dim=0)
        marker_locations = torch.cat((marker_pos_bucket_edge_locations, marker_pos_bottom_plate_locations), dim=0)
        marker_orientations = torch.cat((marker_orientations_bucket_edge, marker_orientations_bucket_edge), dim=0)

        # Now append center of mass
        marker_indices = torch.cat((marker_indices, indices_com), dim=0)
        marker_locations = torch.cat((marker_locations, marker_pos_com_locations), dim=0)
        marker_orientations = torch.cat((marker_orientations, marker_orientations_com), dim=0)

        # Soil Height futures
        marker_indices_height_future = torch.ones(self.num_envs, device=self.device) * 2
        for l in range(self.cfg.observations_excavation.num_soil_height_futures):
            marker_pos_center_height_future_curr = torch.stack(
                (
                    self.m545_measurements.bucket_pos_map[:, 0]
                    - l * self.cfg.observations_excavation.soil_height_futures_spacing,
                    self.scene.env_origins[:, 1],
                    self.soil_height_futures[:, l],
                ),
                dim=1,
            )
            marker_indices = torch.cat((marker_indices, marker_indices_height_future), dim=0)
            marker_locations = torch.cat((marker_locations, marker_pos_center_height_future_curr), dim=0)
            marker_orientations = torch.cat((marker_orientations, zero_orientation.clone()), dim=0)

        # Max depth height future
        marker_indices_max_depth_future = torch.ones(self.num_envs, device=self.device) * 3
        for k in range(self.cfg.observations_excavation.num_max_depth_futures):
            marker_pos_center_max_depth_future_curr = torch.stack(
                (
                    self.m545_measurements.bucket_pos_map[:, 0]
                    - k * self.cfg.observations_excavation.max_depth_futures_spacing,
                    self.scene.env_origins[:, 1],
                    self.max_depth_futures[:, k],
                ),
                dim=1,
            )
            marker_indices = torch.cat((marker_indices, marker_indices_max_depth_future), dim=0)
            marker_locations = torch.cat((marker_locations, marker_pos_center_max_depth_future_curr), dim=0)
            marker_orientations = torch.cat((marker_orientations, zero_orientation.clone()), dim=0)

        # Soil force and moment
        delta_F = self.soil.get_resultant_force() * 0.0001
        delta_M = self.soil.get_resultant_moment() * 0.0001

        force_mask = torch.any(delta_F > 1, dim=1)
        moment_mask = torch.any(delta_M > 1, dim=1)
        # Only compute/draw forces/moments for non-zero
        nonzero_mask = force_mask | moment_mask

        if torch.any(nonzero_mask):
            start = torch.stack(
                (
                    self.m545_measurements.bucket_com_pos_w[:, 0] + self.scene.env_origins[:, 0],
                    self.m545_measurements.bucket_com_pos_w[:, 1] + self.scene.env_origins[:, 1],
                    self.m545_measurements.bucket_com_pos_w[:, 2] + self.scene.env_origins[:, 2],
                ),
                dim=1,
            )
            num_points = 10

            delta_F_x = torch.empty((self.num_envs, num_points), device=self.device)
            delta_F_y = torch.empty((self.num_envs, num_points), device=self.device)
            delta_F_z = torch.empty((self.num_envs, num_points), device=self.device)

            delta_M_x = torch.empty((self.num_envs, num_points), device=self.device)
            delta_M_y = torch.empty((self.num_envs, num_points), device=self.device)
            delta_M_z = torch.empty((self.num_envs, num_points), device=self.device)

            for i in range(self.num_envs):
                delta_F_x[i, :] = torch.linspace(0.0, delta_F[i, 0], num_points, device=self.device)
                delta_F_y[i, :] = torch.linspace(0.0, delta_F[i, 1], num_points, device=self.device)
                delta_F_z[i, :] = torch.linspace(0.0, delta_F[i, 2], num_points, device=self.device)

                delta_M_x[i, :] = torch.linspace(0.0, delta_M[i, 0], num_points, device=self.device)
                delta_M_y[i, :] = torch.linspace(0.0, delta_M[i, 1], num_points, device=self.device)
                delta_M_z[i, :] = torch.linspace(0.0, delta_M[i, 2], num_points, device=self.device)

            point_F = start.clone()
            point_M = start.clone()

            marker_indices_point_F = torch.ones(self.num_envs, device=self.device) * 4
            marker_indices_point_M = torch.ones(self.num_envs, device=self.device) * 5

            for j in range(num_points):
                # F
                f_shift = torch.stack((delta_F_x[:, j], delta_F_y[:, j], delta_F_z[:, j]), dim=1)
                marker_pos_point_F = point_F + (j + 1) * f_shift
                # Only append if force is non-zero for that env
                marker_indices = torch.cat((marker_indices, marker_indices_point_F[force_mask]), dim=0)
                marker_locations = torch.cat((marker_locations, marker_pos_point_F[force_mask]), dim=0)
                marker_orientations = torch.cat((marker_orientations, zero_orientation[force_mask]), dim=0)

                # M
                m_shift = torch.stack((delta_M_x[:, j], delta_M_y[:, j], delta_M_z[:, j]), dim=1)
                marker_pos_point_M = point_M + (j + 1) * m_shift
                # Only append if moment is non-zero for that env
                marker_indices = torch.cat((marker_indices, marker_indices_point_M[moment_mask]), dim=0)
                marker_locations = torch.cat((marker_locations, marker_pos_point_M[moment_mask]), dim=0)
                marker_orientations = torch.cat((marker_orientations, zero_orientation[moment_mask]), dim=0)

        if self.sim.has_gui():
            # Visualization
            self.excavation_visualizer.visualize(marker_locations, marker_orientations, marker_indices=marker_indices)
            self.excavation_draw_interface.clear_lines()

        for i in range(self.num_envs):
            x, z = self.soil.get_soil_height()
            vertices_source = torch.zeros((x.shape[0] - 1, 3), device=self.device)
            vertices_target = torch.zeros((x.shape[0] - 1, 3), device=self.device)
            vertices_source[:, 0] = x[:-1] + self.scene.env_origins[i, 0]
            vertices_source[:, 1] = self.scene.env_origins[i, 1]
            vertices_source[:, 2] = z[i, :-1]
            vertices_target[:, 0] = x[1:] + self.scene.env_origins[i, 0]
            vertices_target[:, 1] = self.scene.env_origins[i, 1]
            vertices_target[:, 2] = z[i, 1:]
            num_lines = vertices_source.shape[0]
            lines_colors = [[0.0, 1.0, 0.0, 1.0]] * num_lines
            line_thicknesses = [4.0] * num_lines
            self.excavation_draw_interface.draw_lines(
                vertices_source.tolist(), vertices_target.tolist(), lines_colors, line_thicknesses
            )

            x, z = self.soil.get_max_depth_height()
            vertices_source = torch.zeros((x.shape[0] - 1, 3), device=self.device)
            vertices_target = torch.zeros((x.shape[0] - 1, 3), device=self.device)
            vertices_source[:, 0] = x[:-1] + self.scene.env_origins[i, 0]
            vertices_source[:, 1] = self.scene.env_origins[i, 1]
            vertices_source[:, 2] = z[i, :-1]
            vertices_target[:, 0] = x[1:] + self.scene.env_origins[i, 0]
            vertices_target[:, 1] = self.scene.env_origins[i, 1]
            vertices_target[:, 2] = z[i, 1:]
            num_lines = vertices_source.shape[0]
            lines_colors = [[1.0, 0.0, 0.0, 1.0]] * num_lines
            line_thicknesses = [4.0] * num_lines
            self.excavation_draw_interface.draw_lines(
                vertices_source.tolist(), vertices_target.tolist(), lines_colors, line_thicknesses
            )

            num_lines = 2
            vertices_source = torch.zeros((num_lines, 3), device=self.device)
            vertices_target = torch.zeros((num_lines, 3), device=self.device)
            vertices_source[0, 0] = self.pullup_dist[i] + self.scene.env_origins[i, 0]
            vertices_target[0, 0] = self.pullup_dist[i] + self.scene.env_origins[i, 0]
            vertices_source[1, 0] = (
                self.pullup_dist[i] + self.curriculum_excavation.curr_pullup_band + self.scene.env_origins[i, 0]
            )
            vertices_target[1, 0] = (
                self.pullup_dist[i] + self.curriculum_excavation.curr_pullup_band + self.scene.env_origins[i, 0]
            )
            vertices_source[:, 1] = self.scene.env_origins[i, 1]
            vertices_target[:, 1] = self.scene.env_origins[i, 1]
            vertices_source[:, 2] = -1.5
            vertices_target[:, 2] = 1.5
            lines_colors = [[0.98, 0.63, 0.1, 1.0]] * num_lines
            line_thicknesses = [4.0] * num_lines
            self.excavation_draw_interface.draw_lines(
                vertices_source.tolist(), vertices_target.tolist(), lines_colors, line_thicknesses
            )

            num_lines = self.cfg.observations_excavation.num_soil_normal_futures
            vertices_source = torch.zeros((num_lines, 3), device=self.device)
            vertices_target = torch.zeros((num_lines, 3), device=self.device)
            for k in range(num_lines):
                x_pos = self.m545_asset.bucket_pos_w[i, 0] - k * self.cfg.observations_excavation.max_soil_normal_futures_spacing
                z_pos = self.soil.get_soil_height_at_pos(x_pos, env_ids=[i])
                vertices_source[k, 0] = x_pos + self.scene.env_origins[i, 0]
                vertices_source[k, 1] = self.scene.env_origins[i, 1]
                vertices_source[k, 2] = z_pos
                vertices_target[k, 0] = (
                    x_pos + torch.cos(self.soil_normal_futures[i, k] + np.pi / 2.0) + self.scene.env_origins[i, 0]
                )
                vertices_target[k, 1] = self.scene.env_origins[i, 1]
                vertices_target[k, 2] = z_pos + torch.sin(self.soil_normal_futures[i, k] + np.pi / 2.0)

            lines_colors = [[0.0, 0.0, 1.0, 1.0]] * num_lines
            line_thicknesses = [4.0] * num_lines
            self.excavation_draw_interface.draw_lines(
                vertices_source.tolist(), vertices_target.tolist(), lines_colors, line_thicknesses
            )
