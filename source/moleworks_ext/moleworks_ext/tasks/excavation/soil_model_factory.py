# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Factory for creating soil models based on configuration."""

from __future__ import annotations

from typing import TYPE_CHECKING, Any
"""
if TYPE_CHECKING:
    from .excavation_env_3d import ExcavationEnv
"""
from .soil_model_base import SoilModelBase
from .env_cfg.excavation_env_cfg import ExcavationEnvCfg

# Forward declarations to avoid circular imports
Soil = None
Soil3D = None


def create_soil_model(cfg: ExcavationEnvCfg):
    """Factory function to create the appropriate soil model.
    
    Args:
        cfg: Configuration for the excavation environment
        env: The excavation environment instance
        
    Returns:
        An instance of a soil model that implements SoilModelBase
        
    Raises:
        ValueError: If the requested soil model type is not supported
    """
    model_type = cfg.soil_model_cfg.type
    
    if model_type == "1d":
        # Use the 1D soil model
        from .soil_model.soil import Soil
        return Soil(cfg)
    elif model_type == "3d":
        # Use the 3D soil model
        from .soil_model_3d.soil_3d import Soil3D
        return Soil3D(cfg)
    else:
        raise ValueError(f"Unsupported soil model type: {model_type}. Options are '1d' or '3d'.") 