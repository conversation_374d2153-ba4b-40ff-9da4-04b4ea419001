# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Script designed to merge rocks from obj file with a terrain in Isaac Sim"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os
import torch

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Train an RL agent with RSL-RL.")
parser.add_argument("--num_objects", type=float, default=30)
parser.add_argument(
    "--object_list",
    type=list,
    default=[
        # all terrains
        # Object I want to integrate to my terrain
        # "/home/<USER>/Documents/boulder/meshes/boulder_0000.obj",
        "/home/<USER>/Documents/boulder/meshes/0000.usd",
        "/home/<USER>/Documents/boulder/meshes/0001.usd",
        # "/home/<USER>/Documents/boulder/meshes/boulder_0003.obj",
    ],
    help="List of generated terrains that should be combined.",
)
parser.add_argument(
    "--dataset_path",
    type=list,
    default=["/home/<USER>/Documents/stone_data_set/stones_obj"],
    help="Folder containing .obj meshes that should be merged to the given terrain",
)

# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.headless = True
args_cli.generator_list = ["OBSTACLE_TERRAIN_CFG"]  # ["FDM_EXTEROCEPTIVE_TERRAINS_CFG", "FDM_TERRAINS_CFG"

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import numpy as np
import trimesh

import carb
import omni.ext
import isaacsim.core.utils.prims as prim_utils
import omni.kit.commands
import omni.kit.utils
import omni.usd
from pxr import Gf, Sdf, Usd, UsdGeom, UsdShade

import isaaclab.sim as sim_utils
from isaaclab.terrains import TerrainGenerator, TerrainImporter, TerrainImporterCfg

# MERGES TERRAINS TOGETHER
from moleworks_ext.tasks.driving.terrain_cfg.terrain_cfg import OBSTACLE_TERRAIN_CFG


def obj_folder_to_usd(obj_folder):
    # convert any .obj files to .usd
    converter_cfg = sim_utils.MeshConverterCfg(
        mass_props=sim_utils.MassPropertiesCfg(),
        collision_props=sim_utils.CollisionPropertiesCfg(
            collision_enabled=True,
        ),
        asset_path=args_cli.dataset_path[0],
        rotation=(1.0, 0.0, 0.0, 0.0),
        scale=(1.0, 1.0, 1.0),
        force_usd_conversion=False,
    )
    # Reads the .obj dataset
    try:
        # Get a list of all files and directories in the given directory
        all_entries = os.listdir(obj_folder)
        # Filter out files with '.obj' extension and get their full paths
        files = [
            os.path.join(obj_folder, entry)
            for entry in all_entries
            if os.path.isfile(os.path.join(obj_folder, entry)) and entry.lower().endswith(".obj")
        ]
    except Exception as e:
        print(f"An error occurred: {e}")
        files = []

    for env in files:
        sim_utils.MeshConverter(
            converter_cfg.replace(
                asset_path=env,
                usd_file_name=os.path.basename(env).replace(".obj", ".usd"),
                usd_dir="/home/<USER>/Documents/stone_data_set/stones_usd",
            )
        )


def obj_to_usd():
    # it any .obj files to .usd
    converter_cfg = sim_utils.MeshConverterCfg(
        mass_props=sim_utils.MassPropertiesCfg(),
        collision_props=sim_utils.CollisionPropertiesCfg(
            collision_enabled=True,
        ),
        asset_path=args_cli.object_list[0],
        rotation=(1.0, 0.0, 0.0, 0.0),
        scale=(1.0, 1.0, 1.0),
    )
    for env in args_cli.object_list:
        sim_utils.MeshConverter(
            converter_cfg.replace(
                asset_path=env,
                usd_file_name=os.path.basename(env).replace(".obj", ".usd"),
                usd_dir=os.path.dirname(env),
            )
        )


def main():

    # Convert obj meshes to usd
    # obj_folder_to_usd(args_cli.dataset_path[0])

    heights = get_all_obj_file_heights("/home/<USER>/Documents/stone_data_set/stones_obj")
    for element in heights:
        print(element)

    # init simulation context
    sim = sim_utils.SimulationContext()
    OBSTACLE_TERRAIN_CFG.num_cols = 1
    OBSTACLE_TERRAIN_CFG.num_cols = 1
    # Handler for terrains importing
    terrain_importer_cfg = TerrainImporterCfg(
        num_envs=1,
        env_spacing=3.0,
        prim_path="/World/ground",
        max_init_terrain_level=None,
        terrain_type="generator",
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # 0.8, # should be 0.8
            dynamic_friction=0.8,  # 0.8,
            restitution=0.8,
        ),
        terrain_generator=OBSTACLE_TERRAIN_CFG,  # This is a terrain generator cfg
        debug_vis=True,
    )
    # Create terrain importer
    terrain_importer = TerrainImporter(terrain_importer_cfg)
    # Find the limit of the terrain
    origin = to_np(terrain_importer.env_origins[0])
    terrain_size = OBSTACLE_TERRAIN_CFG.size  # TODO: how to do it for num_rows and num_cols
    platform_width = 2
    platform_corners = np.asarray(
        [
            [origin[0] - platform_width / 2, origin[1] - platform_width / 2],
            [origin[0] + platform_width / 2, origin[1] + platform_width / 2],
        ]
    )

    # Limit of the terrain excluding a circle in the middle

    # Stack the objects on top of the terrain
    # sample center for objects
    while True:
        num_objects = args_cli.num_objects
        object_centers = np.zeros((num_objects, 3))
        # This loop is to avoid defining rocks where the excavator is spawned
        excavator_size_x, excavator_size_y = 3, 3
        for i in range(num_objects):
            # Define interval
            interval_x = [(-terrain_size[0] / 2, -excavator_size_x), (excavator_size_x, terrain_size[0] / 2)]
            interval_y = [(-terrain_size[1] / 2, -excavator_size_y), (excavator_size_y, terrain_size[1] / 2)]
            # Choose the interval to sample from
            interval_choice_x = np.random.choice(len(interval_x))
            interval_choice_y = np.random.choice(len(interval_y))
            chosen_interval_x = interval_x[interval_choice_x]
            chosen_interval_y = interval_y[interval_choice_y]
            # Find center
            object_centers[i, 0] = np.random.uniform(chosen_interval_x[0], chosen_interval_x[1])
            object_centers[i, 1] = np.random.uniform(chosen_interval_y[0], chosen_interval_y[1])
        # Sample among z
        # object_centers[:, 2] =
        # filter out the centers that are on the platform
        is_within_platform_x = np.logical_and(
            object_centers[:, 0] >= platform_corners[0, 0], object_centers[:, 0] <= platform_corners[1, 0]
        )
        is_within_platform_y = np.logical_and(
            object_centers[:, 1] >= platform_corners[0, 1], object_centers[:, 1] <= platform_corners[1, 1]
        )
        masks = np.logical_and(is_within_platform_x, is_within_platform_y)
        # if there are no objects on the platform, break
        if not np.any(masks):
            break

    import random

    uds_list_folder = "/home/<USER>/Documents/stone_data_set/stones_usd"
    # Get a list of all files and directories in the given directory
    all_entries = os.listdir(uds_list_folder)
    # Filter out files with '.obj' extension and get their full paths
    uds_list = [
        os.path.join(uds_list_folder, entry)
        for entry in all_entries
        if os.path.isfile(os.path.join(uds_list_folder, entry)) and entry.lower().endswith(".usd")
    ]

    sample_list = random.choices(uds_list, k=num_objects)
    selected_indices = [uds_list.index(file) for file in sample_list]
    # Loop through all the meshes and place them into the scene
    if num_objects > 0:
        for i in range(num_objects):
            # Sample among the usd files
            object_path = sample_list[i]
            # Import USD
            terrain_importer.import_usd(f"object_{i}", object_path)
            rock_prim = prim_utils.get_prim_at_path(f"/World/ground/object_{i}")
            # Sample z center between 0 and rock height
            object_centers[i, 2] = (
                heights[selected_indices[i]] / 4
            )  # random.uniform(-heights[selected_indices[i]]/2, 0)
            # Place the rock randomly in the limit excluding a circel
            object_center = object_centers[i].tolist()
            rock_prim.GetAttribute("xformOp:translate").Set(Gf.Vec3d(object_center))

    # Merge all the meshes
    _merge_mesh(prim_utils.get_prim_at_path("/World"))

    # add collision properties
    sim_utils.define_collision_properties("/Merged", sim_utils.CollisionPropertiesCfg(collision_enabled=True))
    # export merged prim to file
    sim_utils.export_prim_to_file(
        source_prim_path="/Merged",
        target_prim_path="/NavigationTerrain",
        path=os.path.join(os.path.dirname(os.path.dirname(args_cli.object_list[0])), "merged_mesh.usd"),
    )

    print("Done merging")


def get_obj_file_height(file_path):
    min_y = float("inf")
    max_y = float("-inf")

    with open(file_path) as file:
        for line in file:
            if line.startswith("v "):  # Vertex line
                parts = line.split()
                if len(parts) >= 4:
                    try:
                        y = float(parts[2])
                        min_y = min(min_y, y)
                        max_y = max(max_y, y)
                    except ValueError:
                        # Handle the case where conversion to float fails
                        continue

    if min_y == float("inf") or max_y == float("-inf"):
        return None  # No vertices found
    return max_y - min_y


def get_all_obj_file_heights(folder_path):
    heights = []

    for filename in os.listdir(folder_path):
        if filename.lower().endswith(".obj"):
            file_path = os.path.join(folder_path, filename)
            height = get_obj_file_height(file_path)
            if height is not None:
                heights.append(height)

    return heights


def _merge_mesh(curr_prim: Usd.Prim):
    """
    Merge all meshes found within `curr_prim`

    Args:
        curr_prim (Usd.Prim): The parent USD primitive that contains the meshes to be merged.
    """
    stage = omni.usd.get_context().get_stage()
    prim_transform = omni.usd.get_world_transform_matrix(curr_prim, Usd.TimeCode.Default())
    count = 0
    meshes = []

    # Iterate through child primitives of curr_prim
    for child_prim in Usd.PrimRange(curr_prim, Usd.TraverseInstanceProxies()):
        # Filter and Process Meshes:
        imageable = UsdGeom.Imageable(child_prim)
        visible = imageable.ComputeVisibility(Usd.TimeCode.Default())
        if (
            child_prim.IsA(UsdGeom.Mesh)
            and visible != UsdGeom.Tokens.invisible
            and imageable.GetPurposeAttr().Get() in ["default", "render"]
        ):
            # Extract Mesh Data:
            carb.log_warn(child_prim.GetName())
            usdMesh = UsdGeom.Mesh(child_prim)
            mesh = {"points": usdMesh.GetPointsAttr().Get()}
            # Transform and Rotate Points:
            world_mtx = omni.usd.get_world_transform_matrix(child_prim, Usd.TimeCode.Default())
            world_mtx = world_mtx * prim_transform.GetInverse()
            world_rot = world_mtx.ExtractRotation()
            mesh["points"][:] = [world_mtx.TransformAffine(x) for x in mesh["points"]]
            # Process Normals:
            mesh["normals"] = usdMesh.GetNormalsAttr().Get()
            mesh["attr_normals"] = usdMesh.GetPrim().GetAttribute("primvars:normals").Get()
            mesh["attr_normals_indices"] = usdMesh.GetPrim().GetAttribute("primvars:normals:indices").Get()
            if not mesh["attr_normals"]:
                mesh["attr_normals"] = []
            if not mesh["attr_normals_indices"]:
                mesh["attr_normals_indices"] = []
            if mesh["normals"]:
                mesh["normals"][:] = [world_rot.TransformDir(x).GetNormalized() for x in mesh["normals"]]
            else:
                mesh["normals"] = []
                carb.log_warn(f"mesh doesn't contain normals: ({child_prim.GetName()})")
            if mesh["attr_normals"]:
                mesh["attr_normals"][:] = [world_rot.TransformDir(x) for x in mesh["attr_normals"]]
            # Get Additional Mesh Attributes:
            mesh["vertex_counts"] = usdMesh.GetFaceVertexCountsAttr().Get()
            mesh["vertex_indices"] = usdMesh.GetFaceVertexIndicesAttr().Get()
            mesh["name"] = child_prim.GetName()
            # Material Binding:
            mat, rel = UsdShade.MaterialBindingAPI(usdMesh).ComputeBoundMaterial()
            mat_path = str(mat.GetPath())

            if not rel:
                mat_path = "/None"
            # Process Subsets:
            mesh["mat"] = mat_path
            subsets = UsdGeom.Subset.GetAllGeomSubsets(UsdGeom.Imageable(child_prim))
            mesh["subset"] = []
            for s in subsets:
                mat, rel = UsdShade.MaterialBindingAPI(s).ComputeBoundMaterial()
                mat_path = str(mat.GetPath())

                if not rel:
                    mat_path = "/None"
                mesh["subset"].append((mat_path, s.GetIndicesAttr().Get()))

            meshes.append(mesh)
            count = count + 1
    carb.log_info(f"Merging: {count} meshes")
    all_points = []
    all_normals = []
    all_normals_attr = []
    all_normals_indices = []
    all_vertex_counts = []
    all_vertex_indices = []
    all_mats = {}
    index_offset = 0
    normals_offset = 0
    index = 0
    range_offset = 0
    # Loop through meshes
    for mesh in meshes:
        all_points.extend(mesh["points"])
        all_normals.extend(mesh["normals"])
        all_normals_attr.extend(mesh["attr_normals"])
        mesh["attr_normals_indices"][:] = [x + normals_offset for x in mesh["attr_normals_indices"]]
        all_normals_indices.extend(mesh["attr_normals_indices"])
        if mesh["normals"]:
            mesh["normals"][:] = [world_rot.TransformDir(x).GetNormalized() for x in mesh["normals"]]
        all_vertex_counts.extend(mesh["vertex_counts"])
        mesh["vertex_indices"][:] = [x + index_offset for x in mesh["vertex_indices"]]
        all_vertex_indices.extend(mesh["vertex_indices"])
        # all_st.extend(mesh["st"])
        index_offset = index_offset + len(meshes[index]["points"])
        normals_offset = normals_offset + len(mesh["attr_normals_indices"])
        # print("Offset", index_offset)
        index = index + 1
        # create the material entry
        if len(mesh["subset"]) == 0:
            if mesh["mat"] not in all_mats:
                all_mats[mesh["mat"]] = []
            all_mats[mesh["mat"]].extend([*range(range_offset, range_offset + len(mesh["vertex_counts"]), 1)])
        else:
            for subset in mesh["subset"]:
                if subset[0] not in all_mats:
                    all_mats[subset[0]] = []
                all_mats[subset[0]].extend([*(x + range_offset for x in subset[1])])
        range_offset = range_offset + len(mesh["vertex_counts"])
    merged_path = "/Merged/" + str(curr_prim.GetName())
    merged_path = omni.usd.get_stage_next_free_path(stage, merged_path, False)
    carb.log_info(f"Merging to path: {merged_path}")
    merged_mesh = UsdGeom.Mesh.Define(stage, merged_path)
    xform = UsdGeom.Xformable(merged_mesh)
    xform_op_t = xform.AddXformOp(UsdGeom.XformOp.TypeTranslate, UsdGeom.XformOp.PrecisionDouble, "")
    xform_op_r = xform.AddXformOp(UsdGeom.XformOp.TypeOrient, UsdGeom.XformOp.PrecisionDouble, "")
    xform_op_t.Set(prim_transform.ExtractTranslation())
    q = prim_transform.ExtractRotation().GetQuaternion()
    xform_op_r.Set(Gf.Quatd(q.GetReal(), q.GetImaginary()))
    merged_mesh.CreatePointsAttr(all_points)
    if all_normals:
        merged_mesh.CreateNormalsAttr(all_normals)
        merged_mesh.SetNormalsInterpolation(UsdGeom.Tokens.faceVarying)
    merged_mesh.CreateSubdivisionSchemeAttr("none")
    merged_mesh.CreateFaceVertexCountsAttr(all_vertex_counts)
    merged_mesh.CreateFaceVertexIndicesAttr(all_vertex_indices)
    if all_normals_attr:
        normals_attr = merged_mesh.GetPrim().CreateAttribute("primvars:normals", Sdf.ValueTypeNames.Float3Array, False)
        normals_attr.Set(all_normals_attr)
        normals_attr.SetMetadata("interpolation", "vertex")
        merged_mesh.GetPrim().CreateAttribute("primvars:normals:indices", Sdf.ValueTypeNames.IntArray, False).Set(
            all_normals_indices
        )
    extent = merged_mesh.ComputeExtent(all_points)
    merged_mesh.CreateExtentAttr().Set(extent)
    for name, counts in sorted(all_mats.items(), key=lambda a: a[0].rsplit("/", 1)[-1]):
        subset_name = merged_path + "/{}".format(name.rsplit("/", 1)[-1])
        geomSubset = UsdGeom.Subset.Define(stage, omni.usd.get_stage_next_free_path(stage, subset_name, False))
        geomSubset.CreateElementTypeAttr("face")
        geomSubset.CreateFamilyNameAttr("materialBind")
        # print(mesh["vertex_indices"])
        geomSubset.CreateIndicesAttr(counts)
        if name != "/None":
            material = UsdShade.Material.Get(stage, name)
            binding_api = UsdShade.MaterialBindingAPI(geomSubset)
            binding_api.Bind(material)


def to_np(tensor: torch.Tensor) -> np.ndarray:
    """
    Converts a PyTorch tensor to a NumPy array.

    Args:
        tensor (torch.Tensor): The input PyTorch tensor.

    Returns:
        np.ndarray: The converted NumPy array.
    """
    # Check if the tensor is on GPU and move it to CPU if necessary
    if tensor.is_cuda:
        tensor = tensor.cpu()

    # Convert the tensor to a NumPy array
    return tensor.numpy()


if __name__ == "__main__":
    # run the main execution
    main()
    # close sim app
    simulation_app.close()
