# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Functions to generate different terrains using the ``trimesh`` library."""

from __future__ import annotations

import math
import numpy as np
import trimesh
from typing import TYPE_CHECKING

from isaaclab.terrains.trimesh.utils import *  # noqa: F401, F403
from isaaclab.terrains.trimesh.utils import make_border, make_plane

if TYPE_CHECKING:
    from . import stairs_ramp_terrain_cfg


def stairs_ramp_eval_terrain(
    difficulty: float, cfg: stairs_ramp_terrain_cfg.StairsRampTerrainCfg
) -> tuple[list[trimesh.Trimesh], np.ndarray]:
    """Generate a terrain with a stairs and ramp next to each other

    Terrain is designed to have a stairs and ramp next to each other. Can be used to eval purposes with increasing
    step height for the stairs and increasing slope for the ramp.

    Args:
        difficulty: The difficulty of the terrain. This is a value between 0 and 1.
        cfg: The configuration for the terrain.

    Returns:
        A tuple containing the tri-mesh of the terrain and the origin of the terrain (in m).
    """
    if not (cfg.modify_step_height or cfg.modify_ramp_slope):
        print(
            "[WARNING] No change based on difficulty performed because neither step height nor ramp slope are modified."
        )

    # compute number of steps in x and y direction
    ramp_len = cfg.size[0] - 2 * cfg.border_width - 2 * cfg.platform_width
    stairs_ramp_width = (cfg.size[1] - 2 * cfg.border_width) / 2
    num_steps = int(ramp_len / cfg.step_width)

    # compute step height and ramp slope based on difficulty
    if cfg.modify_step_height:
        step_height = cfg.step_height_range[0] + difficulty * (cfg.step_height_range[1] - cfg.step_height_range[0])
        ramp_height = step_height * num_steps
    elif cfg.modify_ramp_slope:
        ramp_slope = cfg.ramp_slope_range[0] + difficulty * (cfg.ramp_slope_range[1] - cfg.ramp_slope_range[0])
        ramp_height = math.tan(np.deg2rad(ramp_slope)) * ramp_len
        step_height = ramp_height / num_steps
    else:
        step_height = cfg.step_height_range[0] if isinstance(cfg.step_height_range, tuple) else cfg.step_height_range
        ramp_slope = cfg.ramp_slope_range[0] if isinstance(cfg.ramp_slope_range, tuple) else cfg.ramp_slope_range
        ramp_height = math.tan(np.deg2rad(ramp_slope)) * ramp_len
        assert np.isclose(step_height * num_steps, ramp_height, atol=0.1), (
            "No difficulty modification is defined and the define step height and ramp slope do not match."
            f"Current step height: {step_height}, current ramp slope: {ramp_slope} with final stairs height of "
            f"{step_height * num_steps} and final ramp length of {ramp_height}."
        )

    # check if the maximum height is exceeded
    if cfg.max_height is not None and step_height * num_steps > cfg.max_height:
        # adjust the len of the stairs and/ or ramp depending on which is modified by the difficulty, make the other
        # one easier
        if cfg.modify_step_height:
            num_steps = int(cfg.max_height / step_height)
            ramp_height = step_height * num_steps
        elif cfg.modify_ramp_slope:
            ramp_height = cfg.max_height
            ramp_len = ramp_height / math.tan(np.deg2rad(ramp_slope))
            step_height = ramp_height / num_steps
        else:
            ramp_height = cfg.max_height
            step_height = cfg.max_height / num_steps

    # restrict the space
    # initialize list of meshes
    meshes_list = list()
    # generate a ground plane for the terrain
    ground_plane = make_plane(cfg.size, height=0.0, center_zero=False)
    meshes_list.append(ground_plane)
    # generate the border if needed
    if cfg.border_width > 0.0:
        # obtain a list of meshes for the border
        border_center = [0.5 * cfg.size[0], 0.5 * cfg.size[1], -step_height / 2]
        border_inner_size = (cfg.size[0] - 2 * cfg.border_width, cfg.size[1] - 2 * cfg.border_width)
        make_borders = make_border(cfg.size, border_inner_size, step_height, border_center)
        # add the border meshes to the list of meshes
        meshes_list += make_borders

    # generate the terrain
    # -- compute the position of the center of the terrain and the centers of the stairs as well as ramp
    terrain_center = [0.5 * cfg.size[0], 0.5 * cfg.size[1], 0.0]
    stairs_center = [terrain_center[0], terrain_center[1] - stairs_ramp_width / 2]
    ramp_center = [terrain_center[0], terrain_center[1] + stairs_ramp_width / 2]

    # -- generate the stair pattern
    for k in range(num_steps):
        # compute the quantities of the box
        # -- location
        box_z = terrain_center[2] + k * step_height / 2.0
        box_offset = (k / 2.0 + 0.5) * cfg.step_width
        # -- dimensions
        box_height = (k + 1) * step_height
        # generate the stair
        box_dims = ((num_steps - k) * cfg.step_width, stairs_ramp_width, box_height)
        box_pos = (stairs_center[0] + box_offset, stairs_center[1], box_z)
        meshes_list.append(trimesh.creation.box(box_dims, trimesh.transformations.translation_matrix(box_pos)))

    # -- generate the ramp
    # define the vertices
    vertices = np.array(
        [
            [ramp_center[0] - ramp_len / 2, ramp_center[1] - stairs_ramp_width / 2, terrain_center[2]],
            [ramp_center[0] - ramp_len / 2, ramp_center[1] + stairs_ramp_width / 2, terrain_center[2]],
            [ramp_center[0] + ramp_len / 2, ramp_center[1] + stairs_ramp_width / 2, terrain_center[2]],
            [ramp_center[0] + ramp_len / 2, ramp_center[1] - stairs_ramp_width / 2, terrain_center[2]],
            [ramp_center[0] + ramp_len / 2, ramp_center[1] - stairs_ramp_width / 2, terrain_center[2] + ramp_height],
            [ramp_center[0] + ramp_len / 2, ramp_center[1] + stairs_ramp_width / 2, terrain_center[2] + ramp_height],
        ]
    )
    faces = np.array([[0, 1, 2], [0, 2, 3], [3, 2, 4], [2, 5, 4], [0, 3, 4], [0, 4, 5], [0, 5, 1], [1, 5, 2]])
    meshes_list.append(trimesh.Trimesh(vertices=vertices, faces=faces, process=False))

    # -- generate the platform that account for possible varing length of the stairs and ramp
    # define the platform behind the stairs
    platform_start = stairs_center[0] + (num_steps * cfg.step_width) / 2
    platform_end = cfg.size[0] - cfg.border_width
    platform_center = (
        platform_start + (platform_end - platform_start) / 2,
        stairs_center[1],
        terrain_center[2] + (num_steps * step_height) / 2,
    )
    platform_dims = (platform_end - platform_start, stairs_ramp_width, (num_steps * step_height))
    meshes_list.append(trimesh.creation.box(platform_dims, trimesh.transformations.translation_matrix(platform_center)))
    # define the platform behind the ramp
    platform_start = ramp_center[0] + ramp_len / 2
    platform_center = (
        platform_start + (platform_end - platform_start) / 2,
        ramp_center[1],
        terrain_center[2] + ramp_height / 2,
    )
    platform_dims = (platform_end - platform_start, stairs_ramp_width, ramp_height)
    meshes_list.append(trimesh.creation.box(platform_dims, trimesh.transformations.translation_matrix(platform_center)))
    # compute the origin of the terrain
    origin = np.array([terrain_center[0], terrain_center[1], terrain_center[2]])

    print(f"Rampe height: {ramp_height}, stairs height: {step_height * num_steps}")

    return meshes_list, origin
