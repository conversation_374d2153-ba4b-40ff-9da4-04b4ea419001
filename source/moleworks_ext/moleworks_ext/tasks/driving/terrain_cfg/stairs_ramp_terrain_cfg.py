# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from dataclasses import MISSING

from isaaclab.terrains import SubTerrainBaseCfg
from isaaclab.utils import configclass

from .stairs_ramp_terrain import stairs_ramp_eval_terrain


@configclass
class StairsRampTerrainCfg(SubTerrainBaseCfg):
    """Configuration for a pyramid stair mesh terrain."""

    function = stairs_ramp_eval_terrain

    border_width: float = 0.0
    """The width of the border around the terrain (in m). Defaults to 0.0.

    The border is a flat terrain with the same height as the terrain.
    """
    modify_step_height: bool = False
    """If True, the step height will be modified based on the difficulty. Defaults to False."""
    step_height_range: tuple[float, float] = MISSING
    """The minimum and maximum height of the steps (in m)."""
    modify_ramp_slope: bool = False
    """If True, the ramp slope will be modified based on the difficulty. Defaults to False."""
    ramp_slope_range: tuple[float, float] = MISSING
    """The minimum and maximum slope of the ramp (in degrees)."""
    step_width: float = MISSING
    """The width of the steps (in m)."""
    platform_width: float = 2.0
    """The minimum width of the platform in front and behind the stairs and the ramp. Defaults to 2.0.

    ..note ::
        The platform behind the stairs and the ramp can be extended in the case the maximum height of the stairs
        and the ramp exceeds the :attr:`max_height` attribute (if it is defined).
    """
    max_height: float | None = 2.0
    """The maximum height of the stairs and the ramp (in m). Defaults to 2.0."""
