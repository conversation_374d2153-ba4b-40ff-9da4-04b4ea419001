# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import isaaclab.terrains as terrain_gen

import moleworks_ext.tasks.driving.terrain_cfg as m545_terrain_gen

##
# Terrain Generator
##

OBSTACLE_TERRAIN_CFG = terrain_gen.TerrainGeneratorCfg(
    # Overall parameters
    size=(20.0, 20.0),
    border_width=1.0,
    num_rows=1,  # only one terrain spawned
    num_cols=1,
    horizontal_scale=0.1,
    vertical_scale=0.005,
    slope_threshold=0.75,
    use_cache=False,
    color_scheme="random",
    sub_terrains={
        # Sub_terrain type can be custom or from terrain_gen
        "outdoor": m545_terrain_gen.ObstacleTerrainCfg(  # Custom terrain with obstacles
            box_objects=m545_terrain_gen.ObstacleTerrainCfg.BoxCfg(
                width=(2.0, 1.0), length=(0.2, 0.5), max_yx_angle=(0, 10), height=(0.5, 3), num_objects=(0, 0)
            ),
            platform_width=1,
            max_height_noise=0.5,
            # rough_terrain=terrain_gen.HfRandomUniformTerrainCfg(
            #    noise_range=(0.02, 0.1), noise_step=0.02, border_width=0.25
            # ),
            border_height=0,
            border_width=0,
        ),
        # "pyramid_stairs": terrain_gen.MeshPyramidStairsTerrainCfg(
        #    proportion=0.15,
        #    step_height_range=(0.05, 0.23),
        #    step_width=0.3,
        #    platform_width=3.0,
        #    border_width=1.0,
        #    holes=False,
        # ),
    },
)
