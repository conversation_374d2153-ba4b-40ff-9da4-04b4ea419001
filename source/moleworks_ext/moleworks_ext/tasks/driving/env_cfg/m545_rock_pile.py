# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
""" Environments with a rock pile"""


from __future__ import annotations

import os

# import isaacsim.core.utils.nucleus as nucleus_utils
import torch

import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg
from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass

from moleworks_ext import MOLEWORKS_RSC_DIR
from moleworks_ext.common.env_cfg.general_env_cfg import m545_camera, m545_lidar, transparent_plane
from moleworks_ext.rsc.driving.m545.m545 import M545_DRIVE_CFG


@configclass
class DrivingRockPileSceneCfg(InteractiveSceneCfg):
    """Configuration for the terrain scene with a humanoid robot."""

    # Plane
    plane = transparent_plane

    # Plane from terrain generator
    rocks = TerrainImporterCfg(
        num_envs=1,
        env_spacing=3.0,
        prim_path="/World/ground_rock",
        max_init_terrain_level=None,
        terrain_type="usd",
        usd_path=f"{MOLEWORKS_RSC_DIR}/driving/terrains/rock_pile_5.usd",
        debug_vis=True,
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="multiply",
            restitution_combine_mode="multiply",
            static_friction=1.0,
            dynamic_friction=1.0,
        ),
    )

    ## sensors
    camera = m545_camera

    lidar = m545_lidar
    # robot
    robot: ArticulationCfg = M545_DRIVE_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

    # lights
    light = AssetBaseCfg(
        prim_path="/World/light",
        spawn=sim_utils.DistantLightCfg(color=(0.75, 0.75, 0.75), intensity=3000.0),
    )

    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(color=(0.13, 0.13, 0.13), intensity=1000.0),
    )


@configclass
class M545EnvCfg(ManagerBasedRLEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""

    # Scene settings
    scene: DrivingRockPileSceneCfg = DrivingRockPileSceneCfg(num_envs=2, env_spacing=15)  # 15

    def __post_init__(self):
        """Post initialization."""
        # general settings
        self.decimation = 4
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.04
        # self.sim.disable_contact_processing = True
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0
