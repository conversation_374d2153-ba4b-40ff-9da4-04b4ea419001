# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


""" This script is deigned to show how to spawn different terrain types"""
from __future__ import annotations

import os

# import isaacsim.core.utils.nucleus as nucleus_utils
import torch

import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObject, RigidObjectCfg
from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sim.spawners.from_files.from_files_cfg import GroundPlaneCfg, UsdFileCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass

from moleworks_ext.common.env_cfg.general_env_cfg import m545_camera, m545_lidar
from moleworks_ext.rsc.driving.m545.m545 import M545_DRIVE_CFG
from moleworks_ext.tasks.driving.terrain_cfg.terrain_cfg import OBSTACLE_TERRAIN_CFG
from moleworks_ext.tasks.driving.terrain_cfg.terrain_cfg_pascal_roth import PILLAR_TERRAIN_EVAL_CFG

##
# Scene definition
##

# The common path relative to the .local directory
#

# Construct the full path using the current user's home directory


from isaaclab.terrains.config.rough import ROUGH_TERRAINS_CFG  # isort: skip


@configclass
class DrivingGeneratedTerrainSceneCfg(InteractiveSceneCfg):
    """Configuration for the terrain scene with a humanoid robot."""

    # Standard Plane
    #

    # Terrain generated by the script merge_mesh_and_terrain, plane with rocks in the trimesh
    # terrain = TerrainImporterCfg(
    #    num_envs=1,
    #    env_spacing=3.0,
    #    prim_path="/World/ground",
    #    max_init_terrain_level=None,
    #    terrain_type="usd",
    #    usd_path="/home/<USER>/Documents/boulder/terrain_with_rocks_as_trimesh.usd",
    #    debug_vis=True,
    #    physics_material=sim_utils.RigidBodyMaterialCfg(
    #        friction_combine_mode="multiply",
    #        restitution_combine_mode="multiply",
    #        static_friction=1.0,
    #        dynamic_friction=1.0,
    #    )
    # )

    # Terrain generated by smashing rocks together
    # plane = transparent_plane
    # terrain = TerrainImporterCfg(
    #    num_envs=1,
    #    env_spacing=3.0,
    #    prim_path="/World/ground",
    #    max_init_terrain_level=None,
    #    terrain_type="usd",
    #    usd_path="/home/<USER>/Documents/Terrains/rocks_stacked.usd",  #
    #    debug_vis=True,
    # )

    # Terrain generated by a config
    terrain = TerrainImporterCfg(
        num_envs=1,
        env_spacing=3.0,
        prim_path="/World/ground",
        max_init_terrain_level=None,
        terrain_type="generator",
        physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # 0.8, # should be 0.8
            dynamic_friction=0.8,  # 0.8,
            restitution=0.8,
        ),
        collision_group=-1,
        terrain_generator=ROUGH_TERRAINS_CFG,  # This is a terrain generator cfg
        debug_vis=True,
    )

    # robot
    robot: ArticulationCfg = M545_DRIVE_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")
    # Sensors
    lidar = m545_lidar
    camera = m545_camera

    # lights
    light = AssetBaseCfg(
        prim_path="/World/light",
        spawn=sim_utils.DistantLightCfg(color=(0.75, 0.75, 0.75), intensity=3000.0),
    )

    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(color=(0.13, 0.13, 0.13), intensity=1000.0),
    )


@configclass
class M545EnvCfg(ManagerBasedRLEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""

    # Scene settings
    scene: DrivingGeneratedTerrainSceneCfg = DrivingGeneratedTerrainSceneCfg(num_envs=2, env_spacing=15)  # 15

    def __post_init__(self):
        """Post initialization."""
        # general settings
        self.decimation = 4
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.04
        # self.sim.disable_contact_processing = True
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0
