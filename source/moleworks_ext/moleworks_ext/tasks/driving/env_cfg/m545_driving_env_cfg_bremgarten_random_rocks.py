# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Bremgarten terrain with random rocks"""

from __future__ import annotations

import os
import torch

import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObject, RigidObjectCfg
from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass

from moleworks_ext import MOLEWORKS_RSC_DIR
from moleworks_ext.common.env_cfg.general_env_cfg import m545_camera, m545_lidar
from moleworks_ext.common.sim.spawners.multi_asset import MultiAssetCfg
from moleworks_ext.tasks.single_boulder_excavation.utils.multi_objects_spawning.multi_object_spawning_functions import (
    get_randomized_rocks_assets,
    get_rocks_assets,
)
from moleworks_ext.rsc.driving.m545.m545 import M545_DRIVE_CFG

# Number of objects to spawn and randomize or not
USD_PATHS = [
    #f"/home/<USER>/Documents/Omniverse_m545/rocks_rigid_objects_moleworks/0026.usd"
    f"{MOLEWORKS_RSC_DIR}/single_boulder_excavation/boulders/rocks_rigid_objects/{str(i).zfill(4)}.usd"
    for i in range(49)  # There are 50 of them in the folder
]
RANDOMIZE = True
NUM_OBJECTS_PER_ENV = 1
DENSITY = 2500 # kg/m³
OBJECT_LIST = [
    RigidObjectCfg(
        prim_path=f"/World/envs/env_.*/Objects_{i}",
        spawn=MultiAssetCfg(assets_cfg=get_randomized_rocks_assets(density=DENSITY, scale=(0.6,0.8), usd_paths=USD_PATHS) if RANDOMIZE else get_rocks_assets(density=DENSITY, usd_paths=USD_PATHS)),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(3, 0, 1 + i)),
        collision_group=-1
    )
    for i in range(NUM_OBJECTS_PER_ENV)
]


@configclass
class BremGartenRandomRockSceneCfg(InteractiveSceneCfg):
    """Configuration for the terrain scene with a humanoid robot."""

    # BremGarten Terrain
    terrain_importer_cfg = TerrainImporterCfg(
        num_envs=1,
        env_spacing=15,
        prim_path="/World/ground",
        max_init_terrain_level=None,
        terrain_type="usd",
        usd_path=f"{MOLEWORKS_RSC_DIR}/driving/terrains/Bremgarten_Terrain_fbx_high_res.usd",
        debug_vis=True,
    )
    # Sensors
    lidar = m545_lidar
    camera = m545_camera
    # lights
    # robot
    robot: ArticulationCfg = M545_DRIVE_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

    light = AssetBaseCfg(
        prim_path="/World/light",
        spawn=sim_utils.DistantLightCfg(color=(0.75, 0.75, 0.75), intensity=3000.0),
    )

    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(color=(0.13, 0.13, 0.13), intensity=1000.0),
    )

    #object_0 = RigidObjectCfg(
    #    prim_path="/World/envs/env_.*/Objects_0",
    #    spawn=sim_utils.UsdFileCfg(
    #        usd_path="/home/<USER>/Documents/Omniverse_m545/rocks_rigid_objects_moleworks/0000.usd",
    #        mass_props = sim_utils.MassPropertiesCfg(density=2500),
    #        collision_props = sim_utils.CollisionPropertiesCfg(collision_enabled=True,contact_offset= 0.01),
    #        rigid_props= sim_utils.RigidBodyPropertiesCfg(max_depenetration_velocity=0.1),
#
    #    ),
    #    init_state=RigidObjectCfg.InitialStateCfg(pos=(1, 0, 1 ))
    #)
    #cone_spawn_cfg = RigidObjectCfg(
    #prim_path="/World/envs/env_.*/Objects_1",
    #    spawn=sim_utils.CuboidCfg(
    #        size=(0.5, 3, 0.5),
    #        rigid_props=sim_utils.RigidBodyPropertiesCfg(
    #            disable_gravity=False,
    #        ),
    #        collision_props=sim_utils.CollisionPropertiesCfg(
    #            collision_enabled=True,
    #        ),
    #    ),
    #     init_state=RigidObjectCfg.InitialStateCfg(pos=(0, 5, 1 ))
    #) 



    def __post_init__(self):
        # Dynamically create object_0, object_1, etc.
        for i, obj_cfg in enumerate(OBJECT_LIST):
            setattr(self, f"object_{i}", obj_cfg)
        #setattr(self, "lidar", m545_lidar)


@configclass
class M545EnvCfg(ManagerBasedRLEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""

    # Scene settings
    scene: BremGartenRandomRockSceneCfg = BremGartenRandomRockSceneCfg(num_envs=2, env_spacing=20)  # 15

    def __post_init__(self):
        """Post initialization."""
        # general settings
        self.decimation = 4
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.04
        # self.sim.disable_contact_processing = True
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0
