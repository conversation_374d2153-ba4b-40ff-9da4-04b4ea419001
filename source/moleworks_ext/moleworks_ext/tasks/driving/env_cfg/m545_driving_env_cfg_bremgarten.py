# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import os

# import isaacsim.core.utils.nucleus as nucleus_utils
import torch

import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObject, RigidObjectCfg
from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass

from moleworks_ext import MOLEWORKS_RSC_DIR
from moleworks_ext.common.env_cfg.general_env_cfg import m545_camera, m545_lidar
from moleworks_ext.rsc.driving.m545.m545 import M545_DRIVE_CFG, M545_DRIVE_CABIN_ARM_CFG


@configclass
class BremGartenRockPileNavMeshSceneCfg(InteractiveSceneCfg):
    """Configuration for the terrain scene with a humanoid robot."""

    # Handler for terrains importing
    terrain_importer_cfg = TerrainImporterCfg(
        num_envs=1,
        env_spacing=3.0,
        prim_path="/World/ground",
        max_init_terrain_level=None,
        terrain_type="usd",
        usd_path=f"{MOLEWORKS_RSC_DIR}/driving/terrains/Bremgarten_Terrain_fbx_high_res_with_rock_pile_and_nav_mesh.usd",  # "/home/<USER>/Documents/Omniverse_m545/Bremgarten_Terrain_fbx_high_res.usd",
        debug_vis=True,
    )

    # sensors
    camera = m545_camera
    lidar = m545_lidar

    # robot
    robot: ArticulationCfg = M545_DRIVE_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

    # lights
    light = AssetBaseCfg(
        prim_path="/World/light",
        spawn=sim_utils.DistantLightCfg(color=(0.75, 0.75, 0.75), intensity=3000.0),
    )

    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(color=(0.13, 0.13, 0.13), intensity=1000.0),
    )


@configclass
class M545EnvCfg(ManagerBasedRLEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""

    # Scene settings
    scene: BremGartenRockPileNavMeshSceneCfg = BremGartenRockPileNavMeshSceneCfg(num_envs=1, env_spacing=15)  # 15

    def __post_init__(self):
        """Post initialization."""
        # general settings
        self.decimation = 4
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.04
        # self.sim.disable_contact_processing = True
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0
