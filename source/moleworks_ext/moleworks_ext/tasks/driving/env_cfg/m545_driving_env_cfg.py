# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import os

# import isaacsim.core.utils.nucleus as nucleus_utils
import torch

import isaacsim.core.utils.prims as prim_utils

import isaaclab.envs.mdp as mdp
import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg
from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.terrains import TerrainImporterCfg
from isaaclab.utils import configclass
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObjectCfg

from moleworks_ext.common.env_cfg.general_env_cfg import m545_camera, m545_lidar, transparent_plane
from moleworks_ext.rsc.driving.m545.m545 import M545_DRIVE_CFG


@configclass
class DrivingSceneCfg(InteractiveSceneCfg):
    """Configuration for the terrain scene with a humanoid robot."""

    # Handler for terrains importing
    terrain_importer_cfg = transparent_plane

    # sensors
    #camera = m545_camera
    #lidar = m545_lidar

    # robot
    robot: ArticulationCfg = M545_DRIVE_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

    # Humanoids

    # lights
    light = AssetBaseCfg(
        prim_path="/World/light",
        spawn=sim_utils.DistantLightCfg(color=(0.75, 0.75, 0.75), intensity=3000.0),
    )

    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(color=(0.13, 0.13, 0.13), intensity=1000.0),
    )
    cube = RigidObjectCfg(
    prim_path="/World/envs/env_.*/cube",
        spawn=sim_utils.CuboidCfg(
            size=(2,2,2),
            #visual_material=sim_utils.GlassMdlCfg(glass_ior=1.0003),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
                #contact_offset = 0.5
            ),
            physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # 0.8, # should be 0.8
            dynamic_friction=0.8,  # 0.8,
            restitution=0.8,
        ),rigid_props=sim_utils.RigidBodyPropertiesCfg(
                #disable_gravity=True,
                #max_linear_velocity=0,
                #max_angular_velocity=0,
                #kinematic_enabled = True
            ),
        
        ),
         init_state=RigidObjectCfg.InitialStateCfg(pos=(2, 0, -0.704 )),
         collision_group=0
    )

    cube2 = RigidObjectCfg(
    prim_path="/World/envs/env_.*/cube2",
        spawn=sim_utils.CuboidCfg(
            size=(2,2,2),
            #visual_material=sim_utils.GlassMdlCfg(glass_ior=1.0003),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
                #contact_offset = 0.5
            ),
            physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # 0.8, # should be 0.8
            dynamic_friction=0.8,  # 0.8,
            restitution=0.8,
        ),rigid_props=sim_utils.RigidBodyPropertiesCfg(
                #disable_gravity=True,
                #max_linear_velocity=0,
                #max_angular_velocity=0,
                #kinematic_enabled = True
            ),
        
        ),
         init_state=RigidObjectCfg.InitialStateCfg(pos=(1, 4, -0.704 )),
         collision_group=0
    )

@configclass
class ActionsCfg:
    """Action specifications for the MDP."""

    joint_position = mdp.JointPositionActionCfg(asset_name="robot", joint_names=["J_RF_STEER", "J_LF_STEER"])
    joint_velocity = mdp.JointVelocityActionCfg(
        asset_name="robot", joint_names=["J_LF_WHEEL", "J_LH_WHEEL", "J_RF_WHEEL", "J_RH_WHEEL"]
    )


@configclass
class M545EnvCfg(ManagerBasedRLEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""

    # Scene settings
    scene: DrivingSceneCfg = DrivingSceneCfg(num_envs=2, env_spacing=15)  # 15

    def __post_init__(self):
        """Post initialization."""
        # general settings
        self.decimation = 4
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.04
        # self.sim.disable_contact_processing = True
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0
