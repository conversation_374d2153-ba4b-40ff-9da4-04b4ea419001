# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script is designed to test the driving environment with 0 action

"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="This script demonstrates how to simulate a bipedal robot.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments

args_cli = parser.parse_args()
args_cli.enable_cameras = True

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import torch
import traceback

import carb

import isaaclab.sim as sim_utils

##
# Pre-defined configs
##
from isaaclab.scene import InteractiveScene
from isaaclab.sim import SimulationContext

from moleworks_ext.tasks.driving.devices.gamepad.gamepad_control_driving import m545Gamepad_driving
from moleworks_ext.tasks.driving.devices.keyboard.keyboard_control import Se2Keyboard
from moleworks_ext.tasks.driving.env_cfg.m545_driving_env_cfg import DrivingSceneCfg
from moleworks_ext.tasks.driving.env_cfg.m545_driving_env_cfg_bremgarten import BremGartenRockPileNavMeshSceneCfg
from moleworks_ext.tasks.driving.env_cfg.m545_driving_env_cfg_bremgarten_random_rocks import (
    BremGartenRandomRockSceneCfg,
)
from moleworks_ext.tasks.driving.env_cfg.m545_rock_pile import DrivingRockPileSceneCfg
from moleworks_ext.tasks.driving.env_cfg.m545_terrain_gen_example_env_cfg import DrivingGeneratedTerrainSceneCfg


def main():
    """Main function."""

    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device="cuda")
    sim = SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view([12, 12, 12], [0.0, 0.0, 0.0])

    # -- Spawn things into stage
    # Lights-1
    # -- Spawn things into stage, use Scence from Cfg
    scene_cfg = DrivingSceneCfg(num_envs=1, env_spacing=10)
    scene = InteractiveScene(scene_cfg)
    robot = scene["robot"]

    # Play the simulator
    sim.reset()

    # Now we are ready!
    print("[INFO]: Setup complete...")
    print("Root state: ", robot.data.default_root_state[:, :7])

    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    sim_time = 0.0
    count = 0
    # Simulate physics
    while simulation_app.is_running():
        # reset
        if count % 10000 == 0:
            sim_time = 0.0
            count = 0
            # reset dof state
            joint_pos, joint_vel = robot.data.default_joint_pos, robot.data.default_joint_vel
            robot.write_joint_state_to_sim(joint_pos, joint_vel)
            robot.write_root_pose_to_sim(robot.data.default_root_state[:, :7])
            robot.write_root_velocity_to_sim(robot.data.default_root_state[:, 7:])
            robot.reset()
            # reset command
            print(">>>>>>>> Reset!")
        i = 1.0
        # velocity_input = torch.tensor([i,0,i,0,0,0], device = robot.device)
        position_input = torch.tensor([0.0, 0.0], device=robot.device)
        velocity_input = torch.tensor([i, i, i, i], device=robot.device)
        # apply action to the robot
        robot.set_joint_position_target(position_input, joint_ids=[0, 2])
        robot.set_joint_velocity_target(velocity_input, joint_ids=[1, 3, 4, 5])
        robot.write_data_to_sim()
        # perform step
        sim.step()
        # update buffers
        robot.update(sim_dt)

        # update sim-time
        sim_time += sim_dt
        count += 1


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
