# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script is designed to drive an excavation agent with sensors

"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="This script demonstrates how to simulate a bipedal robot.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments

args_cli = parser.parse_args()
args_cli.enable_cameras = True
args_cli.teleop_type = "Gamepad"  # Keyboard or Gamepad

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import torch
import traceback

import carb

import isaaclab.sim as sim_utils

##
# Pre-defined configs
##
from isaaclab.scene import InteractiveScene
from isaaclab.sim import SimulationContext

from moleworks_ext.tasks.driving.devices.gamepad.gamepad_control_driving import m545Gamepad_driving
from moleworks_ext.tasks.driving.devices.keyboard.keyboard_control import Se2Keyboard
from moleworks_ext.tasks.driving.env_cfg.m545_driving_env_cfg import DrivingSceneCfg
from moleworks_ext.tasks.driving.env_cfg.m545_driving_env_cfg_bremgarten import BremGartenRockPileNavMeshSceneCfg
from moleworks_ext.tasks.driving.env_cfg.m545_driving_env_cfg_bremgarten_random_rocks import (
    BremGartenRandomRockSceneCfg,
)
from moleworks_ext.tasks.driving.env_cfg.m545_rock_pile import DrivingRockPileSceneCfg
from moleworks_ext.tasks.driving.env_cfg.m545_terrain_gen_example_env_cfg import DrivingGeneratedTerrainSceneCfg


def main():
    """Main function."""

    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device="cuda")
    sim = SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view([12, 12, 12], [0.0, 0.0, 0.0])

    # -- Choose which Scene Cfg among the imported ones to use
    scene_cfg = DrivingSceneCfg(num_envs=1, env_spacing=20)
    scene = InteractiveScene(scene_cfg)
    robot = scene["robot"]

    # Define the keyboard interface
    if args_cli.teleop_type == "Gamepad":
        teleop_interface = m545Gamepad_driving(v_x_sensitivity=5, v_y_sensitivity=0.5, omega_z_sensitivity=1)
    elif args_cli.teleop_type == "Keyboard":
        teleop_interface = Se2Keyboard(v_x_sensitivity=5, v_y_sensitivity=0.5, omega_z_sensitivity=1)
    else:
        raise TypeError("Invalid teleoperation type")

    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")

    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    sim_time = 0.0
    count = 0

    # Initialization
    robot.data.default_root_state[:, :7][:, 2] = 3
    robot.write_root_pose_to_sim(robot.data.default_root_state[:, :7])

    # Simulate physics
    while simulation_app.is_running():
        # Teleop Interface
        velocity, steering = teleop_interface.advance()

        # Inputs
        position_input = torch.tensor(steering, device=robot.device).to(dtype=torch.float32)
        velocity_input = torch.tensor(velocity, device=robot.device).to(dtype=torch.float32)
        # apply action to the robot
        robot.set_joint_position_target(position_input, joint_ids=[0, 2])
        robot.set_joint_velocity_target(velocity_input, joint_ids=[1, 3, 4, 5])
        robot.write_data_to_sim()

        # perform step
        sim.step()
        # update buffers
        scene.update(sim_dt)
        # update sim-time
        sim_time += sim_dt
        count += 1


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
