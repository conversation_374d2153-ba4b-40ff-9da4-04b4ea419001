# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import os
# import isaacsim.core.utils.nucleus as nucleus_utils
import torch

import isaaclab.sim as sim_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObjectCfg
from isaaclab.managers import CurriculumTermCfg as CurrTerm
from isaaclab.managers import EventTermCfg as EventTerm
from isaaclab.managers import ObservationGroupCfg as ObsGroup
from isaaclab.managers import RewardTermCfg as RewTerm
from isaaclab.managers import SceneEntityCfg
from isaaclab.managers import TerminationTermCfg as DoneTerm
from isaaclab.scene import InteractiveSceneCfg
from isaaclab.sensors import CameraCfg, ContactSensorCfg, RayCasterCfg, patterns
from moleworks_ext.common.sensors.ray_caster_idx.multi_mesh_ray_caster_mesh_idx_cfg import MultiMeshRayCasterMeshIdxCfg
from isaaclab.sensors import FrameTransformerCfg, OffsetCfg

from isaaclab.utils import configclass

import moleworks_ext.tasks.single_boulder_excavation.mdp as mdp
from moleworks_ext.common.sensors.ContactSensorPrecise.ContactSensorPreciseCfg import ContactSensorPreciseCfg
from moleworks_ext.common.actions.actions_cfg import InverseDynamicsActionCfg
from moleworks_ext.common.env_cfg.general_env_cfg import m545_lidar, transparent_plane
from moleworks_ext.common.managers.observations.obs_with_mean import ObservationWithMeanTermCfg as ObsTerm
from moleworks_ext.common.sim.spawners.multi_asset import MultiAssetCfg
from moleworks_ext.rsc.single_boulder_excavation.m545.m545 import M545_DOF_ARM_W_COLLISION_CFG, M545_DOF_ARM_W_COLLISION_SHOVEL_CFG
from moleworks_ext.tasks.single_boulder_excavation.env_cfg.excavation_single_boulder_env_cfg import (
    ExcavationSingleBoulderEnvCfg,
)
from moleworks_ext.tasks.single_boulder_excavation.utils.multi_objects_spawning.multi_object_spawning_functions import (
    get_randomized_rocks_assets,
    get_rocks_assets,
)
from moleworks_ext import MOLEWORKS_RSC_DIR

##
# Scene definition
##
# List of USD (Universal Scene Description) file paths for the rock assets
USD_PATHS = [
    #f"/home/<USER>/Documents/Omniverse_m545/rocks_rigid_objects_moleworks/0026.usd"
    f"{MOLEWORKS_RSC_DIR}/single_boulder_excavation/boulders/rocks_rigid_objects/{str(i).zfill(4)}.usd"
    for i in range(49)  # There are 50 of them in the folder
]
RANDOMIZE = True
NUM_OBJECTS_PER_ENV = 1
DENSITY = 2500 # kg/m³
OBJECT_LIST = [
    RigidObjectCfg(
        prim_path=f"/World/envs/env_.*/Objects_{i}",
        spawn=MultiAssetCfg(assets_cfg=get_randomized_rocks_assets(density=DENSITY, scale=(0.6,0.8), usd_paths=USD_PATHS) if RANDOMIZE else get_rocks_assets(density=DENSITY, usd_paths=USD_PATHS)),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(3, 0, 1 + i)),
        collision_group=-1
    )
    for i in range(NUM_OBJECTS_PER_ENV)
]
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg

X_LIMIT_NEG = 3
X_LIMIT_POS = 7
Y_LIMIT_NEG = -1
Y_LIMIT_POS = 1


@configclass
class ExcavationSceneCfg(InteractiveSceneCfg):
    """Configuration for the terrain scene with a humanoid robot."""

    # robot
    robot: ArticulationCfg = M545_DOF_ARM_W_COLLISION_SHOVEL_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

    # contact_forces = ContactSensorCfg(prim_path="{ENV_REGEX_NS}/Robot/ROTO_BASE")
    frame_transformer = FrameTransformerCfg(
        prim_path="{ENV_REGEX_NS}/Robot/BASE",
        target_frames=[
            FrameTransformerCfg.FrameCfg(
                name="EE",
                prim_path="{ENV_REGEX_NS}/Robot/ROTO_BASE",
                offset=OffsetCfg(pos=(1.418, 0.0, 0.053),rot=(0.992567, 0.0, -0.121698, 0.0))
            ),
        ],
)   

    # lights
    light = AssetBaseCfg(
        prim_path="/World/light",
        spawn=sim_utils.DistantLightCfg(color=(0.75, 0.75, 0.75), intensity=3000.0),
    )

    sky_light = AssetBaseCfg(
        prim_path="/World/skyLight",
        spawn=sim_utils.DomeLightCfg(color=(0.13, 0.13, 0.13), intensity=1000.0),
    )

    # Contact Forces
    contact_forces = ContactSensorPreciseCfg(prim_path="{ENV_REGEX_NS}/Robot/SHOVEL", history_length = 10, track_pose=True, filter_prim_paths_expr = ["{ENV_REGEX_NS}/Objects_0"], max_contact_data_count=3600000)
    #contact_forces_2 = ContactSensorPreciseCfg(prim_path="{ENV_REGEX_NS}/Robot/ROTO_BASE", history_length = 10, track_pose=True, filter_prim_paths_expr = ["{ENV_REGEX_NS}/Objects_0"], max_contact_data_count=3600000)

    #contact_forces = ContactSensorCfg(prim_path="{ENV_REGEX_NS}/Robot/ROTO_BASE", history_length = 10, track_pose=True, filter_prim_paths_expr = ["{ENV_REGEX_NS}/Objects_0"])

    # Platform
    platform = RigidObjectCfg(
    prim_path="/World/envs/env_.*/platform",
        spawn=sim_utils.CuboidCfg(
            size=(7.75,6.5,0.01),
            #visual_material=sim_utils.GlassMdlCfg(glass_ior=1.0003),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
                #contact_offset = 0.5
            ),
            physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # 0.8, # should be 0.8
            dynamic_friction=0.8,  # 0.8,
            restitution=0.8,
        ),rigid_props=sim_utils.RigidBodyPropertiesCfg(
                #disable_gravity=True,
                max_linear_velocity=0,
                max_angular_velocity=0,
                kinematic_enabled = True
            ),
        
        ),
         init_state=RigidObjectCfg.InitialStateCfg(pos=(5.1, 0, -0.704 )),
         collision_group=1
    )
    '''cube = RigidObjectCfg(
    prim_path="/World/envs/env_.*/cube",
        spawn=sim_utils.CuboidCfg(
            size=(2,2,2),
            #visual_material=sim_utils.GlassMdlCfg(glass_ior=1.0003),
            collision_props=sim_utils.CollisionPropertiesCfg(
                collision_enabled=True,
                #contact_offset = 0.5
            ),
            physics_material=sim_utils.RigidBodyMaterialCfg(
            friction_combine_mode="average",
            restitution_combine_mode="multiply",
            static_friction=0.8,  # 0.8, # should be 0.8
            dynamic_friction=0.8,  # 0.8,
            restitution=0.8,
        ),rigid_props=sim_utils.RigidBodyPropertiesCfg(
                #disable_gravity=True,
                #max_linear_velocity=0,
                #max_angular_velocity=0,
                #kinematic_enabled = True
            ),
        
        ),
         init_state=RigidObjectCfg.InitialStateCfg(pos=(2, 0, -0.704 )),
         collision_group=0
    )'''
    '''raycaster = RayCasterCfg(
        prim_path="{ENV_REGEX_NS}/Robot/BASE",
        update_period=0.02,
        offset=RayCasterCfg.OffsetCfg(pos=(0.92,0.31,2.37), rot=(1.0, 0.0, 0.0, 0.0)),
        attach_yaw_only=False,
        pattern_cfg=patterns.LidarPatternCfg(
            channels=100, vertical_fov_range=(-70.0, 0.0), horizontal_fov_range=(-25.0, 25.0), horizontal_res=0.35
        ),
        debug_vis=True,
        mesh_prim_paths=[
            #RayCasterCfg.RaycastTargetCfg(target_prim_expr="/World/envs/env_.*/cube", 
            RayCasterCfg.RaycastTargetCfg(target_prim_expr="/World/envs/env_.*/Objects_0", 
            ],#,"/World/envs/env_.*/Objects_0/"
            
    )'''
    raycaster = MultiMeshRayCasterMeshIdxCfg(
        prim_path="{ENV_REGEX_NS}/Robot/BASE",
        update_period=0.02,
        offset=RayCasterCfg.OffsetCfg(pos=(0.92,0.31,2.37), rot=(1.0, 0.0, 0.0, 0.0)),
        attach_yaw_only=False,
        pattern_cfg=patterns.LidarPatternCfg(
            channels=80, vertical_fov_range=(-70, 0), horizontal_fov_range=(-25.0, 25.0), horizontal_res=0.35
        ), # 64 channels by 45 degrees
        debug_vis=True,
        mesh_prim_paths=[
            #RayCasterCfg.RaycastTargetCfg(target_prim_expr="/World/envs/env_.*/cube", 
            MultiMeshRayCasterMeshIdxCfg.RaycastTargetCfg(target_prim_expr="/World/envs/env_.*/Objects_0", is_global=False),# is_global=False),
            MultiMeshRayCasterMeshIdxCfg.RaycastTargetCfg(target_prim_expr="/World/envs/env_.*/Robot/SHOVEL", is_global=False),# is_global=False),
            #RayCasterCfg.RaycastTargetCfg(target_prim_expr="/World/envs/env_.*/platform", 
            #RayCasterCfg.RaycastTargetCfg(target_prim_expr="/World/envs/env_.*/Robot/TELE", 

            ],#,"/World/envs/env_.*/Objects_0/"
            track_mesh_transforms=True,
            
            
    )

    def __post_init__(self):
        # Dynamically create object_0, object_1, etc.
        for i, obj_cfg in enumerate(OBJECT_LIST):
            setattr(self, f"object_{i}", obj_cfg)
        # This allows randomization amomng the USD through envs
        self.replicate_physics = False


@configclass
class ObservationsCfg:
    """Observation specifications for the MDP."""

    @configclass
    class PolicyCfg(ObsGroup):
        """Observations for policy group."""

        # Joint Velocities, 4
        dof_pos = ObsTerm(func=mdp.dof_pos, params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])})
        # Joint positions, 4
        dof_vel = ObsTerm(
            func=mdp.dof_vel,
            params={"asset_cfg": SceneEntityCfg("robot", joint_names=[".*"])},
            scale=(1 / 0.1, 1 / 0.1, 1 / 0.2, 1 / 0.2),
        )
        # Joint Torques, 4
        dof_tau = ObsTerm(
            func=mdp.dof_tau,
            mean=[-2.66e4, 2.45e4, 4.24e4, 1.19e4],
            scale=(1 / 1.6e5, 1 / 1.44e5, 1 / 1.48e5, 1 / 5.47e4),
        )
        # previous action 4
        prev_action = ObsTerm(func=mdp.last_action_excavation)
        #
        #pitch_vel = ObsTerm(func=mdp.pitch_vel, scale=[1 / 0.5, 1 / 0.5])
        # Bucket Pitch Gac, 2
        #base_pitch_gac = ObsTerm(func=mdp.base_pitch_gac, scale=[1 / 0.1, 1 / 0.5])
        # Bucket lin gac, 4
        #bucket_lin_gac = ObsTerm(
        #    func=mdp.bucket_lin_gac, mean=[6.0, -1.25, 0.0, 0.0], scale=[1 / 2.0, 1 / 1.75, 1 / 0.5, 1 / 0.5]
        #)
        bucket_lin_gac_base = ObsTerm(
            func=mdp.bucket_lin_gac_base, mean=[6.0, -1.25, 0.0, 0.0], scale=(1 / 2.0, 1 / 1.75, 1 / 0.5, 1 / 0.5)
        )
        ## Bucket ang gac, 2
        bucket_ang_gac = ObsTerm(func=mdp.bucket_ang_gac)
        ### Bucket lin_vel norm, 1
        bucket_lin_vel_norm = ObsTerm(func=mdp.bucket_lin_vel_norm, mean=[0.25], scale=(1 / 0.25))
        # Bucket Pitch Gac, 2
        # fill_ratio_aoa = ObsTerm(func=mdp.fill_ratio_aoa, mean=[0.5, 0.0], scale=[0.5, 1.0])
        ## 5
        #soil_height = ObsTerm(
        #   func=mdp.soil_height, mean=[-1.25, -1.25, -1.25, -1.25, -1.25], scale=[1.75, 1.75, 1.75, 1.75, 1.75]
        #)
        ## 1
        #bucket_depth = ObsTerm(func=mdp.bucket_depth, mean=[-0.25], scale=[0.75])
        ## 2

        ## 5
        #max_depth = ObsTerm(
        #    func=mdp.max_depth,
        #    mean=[-1.25, -1.25, -1.25, -1.25, -1.25],
        #    scale=[1 / 1.75, 1 / 1.75, 1 / 1.75, 1 / 1.75, 1 / 1.75],
        #)

        ## 1
        # pullup_dist = ObsTerm(func=mdp.pullup_dist, mean=[2.75], scale=[0.75])
        # 6
        # soil_parameters = ObsTerm(func=mdp.soil_parameters, mean = [50000.0, 0.5, 0.55, 19500.0, 0.285, 150.0], scale = [50000.0, 0.5, 0.25, 2500, 0.095, 150.0])
        # Boulders could work with boulder_ellipsoid_axes_w but also with extremeties, to test
        # boulder_ellispoid_axes = ObsTerm(func=mdp.boulder_ellipsoid_axes_w)
        # boulder_com_pos_w = ObsTerm(func=mdp.boulder_com_pos_w, mean=[4, 0, 0])

        # To use with Raycaster
        #raycast_boulder = ObsTerm(func=mdp.raycast_boulder, params={"num_points": 15})
        # raycast_boulder_base_frame = ObsTerm(func=mdp.raycast_boulder_base, params={"num_points": 15})
        # to use with RaycasterMeshReturn
        # raycast_boulder_idx = ObsTerm(func=mdp.raycast_boulder_idx_base, params={"num_points": 15})
        raycast_boulder_idx = ObsTerm(func=mdp.ray_cast_boulder_idx_w_noise_base, params={"target_mesh_id": 0.0,
                                                                                          "num_samples": 15, 
                                                                                          "noise_x_std": 1.0, 
                                                                                          "noise_y_std": 0.1, 
                                                                                          "noise_z_std": 0.15, 
                                                                                          "edge_percentage": 0.15,
                                                                                          "std": [7.0, 1.0, 1.0] #[8.0, 1.5, 1.5]
                                                                                          })
        #raycast_boulder_idx = ObsTerm(func=mdp.ray_cast_boulder_idx_w_noise_base, params={"target_mesh_id": 0.0,
        #                                                                                  "num_samples": 15, 
        #                                                                                  "noise_x_std": 0.0, 
        #                                                                                  "noise_y_std": 0.0, 
        #                                                                                  "noise_z_std": 0.0, 
        #                                                                                  "edge_percentage": 0.0,
        #                                                                                  "std": [7.0, 1.0, 1.0] #[8.0, 1.5, 1.5]
        #                                                                                  })

        def __post_init__(self):
            self.enable_corruption = True
            self.concatenate_terms = True

    # observation groups
    policy: PolicyCfg = PolicyCfg()


TERMINATION_HEIGHT = 0.5
TERMINATION_ABOVE_SOIL_HEIGHT = 0.7
@configclass
class TerminationsCfg:
    """Termination terms for the MDP."""

    time_outs = DoneTerm(func=mdp.time_out, time_out=True)
    ### Negative
    negative_termination_base_vel = DoneTerm(func=mdp.negative_termination_base_vel)
    negative_termination_bucket_vel = DoneTerm(func=mdp.negative_termination_bucket_vel, params={"max_bucket_vel": 1.5})
    negative_termination_joint_vel = DoneTerm(func=mdp.negative_termination_joint_vel)
    # negative_termination_base_pitch = DoneTerm(func=mdp.negative_termination_base_pitch) # to implement
    negative_termination_boulder_height = DoneTerm(func=mdp.negative_termination_boulder_height)
    #### Positive
    positive_termination_boulder_in_bucket = DoneTerm(func=mdp.positive_termination_boulder_in_bucket, 
                                                      params={"termination_height": TERMINATION_HEIGHT,
                                                            "termination_height_above_soil": TERMINATION_ABOVE_SOIL_HEIGHT}
                                                      )  # to implement
    negative_termination_bucket_aoa = DoneTerm(func=mdp.negative_termination_bucket_aoa)
    #positive_termination_boulder_in_bucket_flat = DoneTerm(func=mdp.positive_termination_boulder_in_bucket_flat, 
    #                                                  params={"termination_height": TERMINATION_HEIGHT}
    #                                                  )  # to implement
@configclass
class RewardsCfg:
    """Reward terms for the MDP."""

    # --------- Soil indep
    # RewTerm(func=mdp.action_rate_l2, weight=-1e-3)s
    action_rate = RewTerm(func=mdp.action_rate_l2_excavation, weight=-0.005)  #
    #action_penalty = RewTerm(func=mdp.action_penalty, weight=-0.00075)
    #reward_distance_bucket_boulder = RewTerm(func=mdp.reward_distance_bucket_boulder, weight =0.0125)
    reward_boulder_height_increase = RewTerm(func=mdp.reward_boulder_height_increase, params={"termination_height": TERMINATION_HEIGHT, 
                                                                                             "termination_height_above_soil": TERMINATION_ABOVE_SOIL_HEIGHT}, 
                                                                                              weight=0.075)
    #reward_depth = RewTerm(func=mdp.reward_depth, weight=-0.005) #
    #reward_boulder_height_increase_flat = RewTerm(func=mdp.reward_boulder_height_increase_flat, params={"termination_height": TERMINATION_HEIGHT}, 
    #                                                                                          weight=0.075)
    # penalize in soil
    #reward_in_soil= RewTerm(func=mdp.reward_in_soil, weight=-0.005)
    reward_boulder_in_shovel = RewTerm(func=mdp.reward_boulder_in_shovel, weight=0.05)
    
    # Reward for pitch to be in front of the most backward part of the shovel
    # reward_pitch_in_front =  RewTerm(func=mdp.reward_pitch_in_front, weight =0.5)
    #reward_curled_enough = RewTerm(func=mdp.reward_curled_enough, weight=0.025)
    #termination_reward = RewTerm(func=mdp.termination_reward, weight=1.0)
    #time_penalty = RewTerm(func=mdp.const_reward, weight=-0.005)
    #penalty_bucket_vel = RewTerm(func=mdp.penalty_bucket_vel,params={"max_bucket_vel": 1.0}, weight=-0.1)
    termination_reward_pos = RewTerm(func=mdp.termination_reward_pos, weight=0.5)
    termination_reward_neg = RewTerm(func=mdp.termination_reward_neg, weight=0.5)
    # add curl and up


PLATFORM_Z_MAX= 0.2
PLATFORM_Z_MIN = -1.4
@configclass
class EventCfg:
    """Configuration for events."""

    # Reject sampling using a cache
    # boulder_randomization = EventTerm(
    #    func=single_boulder_randomization.boulder_randomization,
    #    mode="reset"
    # )
    rejection_sampling = EventTerm(
        func=mdp.rejection_sampling_boulder,
        params={"asset_cfg": SceneEntityCfg("robot"),
                "max_z_platform": PLATFORM_Z_MAX,
                "min_z_platform": PLATFORM_Z_MIN},
        mode="reset",
    )


@configclass
class ActionsCfg:
    """Action specifications for the MDP."""

    joint_effort = InverseDynamicsActionCfg(asset_name="robot", 
                                            joint_names=[".*"],
                                            scale={
                                                "J_BOOM": 0.1,
                                                "J_STICK": 0.1,
                                                "J_TELE": 0.1,
                                                "J_EE_PITCH": 0.2,
                                            },
                                            controller_cfg=InverseDynamicsControllerCfg(
                                            command_type="vel",
                                            k_p = [10,10,30,30],
                                            k_d = [25,30,40,40],
                                            dof_limits=[
                                                [-0.5, 0.5],
                                                [-0.6, 0.6],
                                                [-0.4, 0.4],
                                                [-0.8, 0.8]
                                            ],
                                            dof_efforts_limits=[
                                                [-2e6, 2e6],
                                                [-1e6, 1e6],
                                                [-1e6, 1e6],
                                                [-1e6, 1e6]
                                            ]
                    ))


@configclass
class M545EnvCfg(ExcavationSingleBoulderEnvCfg):
    """Configuration for the locomotion velocity-tracking environment."""

    # Scene settings
    scene: ExcavationSceneCfg = ExcavationSceneCfg(num_envs=2, env_spacing=15, replicate_physics=False)  # 15
    scene.replicate_physics = False
    # Basic settings
    observations: ObservationsCfg = ObservationsCfg()
    actions: ActionsCfg = ActionsCfg()
    # commands: CommandsCfg = CommandsCfg()
    ## MDP settings
    rewards: RewardsCfg = RewardsCfg()
    terminations: TerminationsCfg = TerminationsCfg()
    events: EventCfg = EventCfg()
    # curriculum: CurriculumCfg = CurriculumCfg()

    # TODO: Inherit from ManagerBasedRLEnvCfg and add excavation: ExcavationEnvCfg() = ExcavationEnvCfg but then quiet a
    # lot to modify in code

    arm_joints_names = ["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"]
    ee_body_name = "SHOVEL"
    '''
    arm_joints_names = ["J_BOOM","J_STICK","J_TELE","J_EE_PITCH"]
    ee_body_name = "SHOVEL"
    ee_frame = "ENDEFFECTOR_CONTACT"
    '''

    def __post_init__(self):
        """Post initialization."""
        # general settings
        self.decimation = 8
        self.episode_length_s = 19.95
        # simulation settings
        self.sim.dt = 0.02
        # self.sim.disable_contact_processing = True
        self.sim.physx.bounce_threshold_velocity = 1.0
        self.sim.physx.solver_type = 1.0
        self.sim.render_interval = self.decimation
        self.sim.physx.min_position_iteration_count = 32
        self.sim.physx.min_velocity_iteration_count = 32

        self.soil_parameters.type = "s_0_0" # before curriculum puts random
