# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import torch
from dataclasses import MISSING

from isaaclab.envs import ManagerBasedRLEnvCfg
from isaaclab.utils import configclass


@configclass
class ExcavationSingleBoulderEnvCfg(ManagerBasedRLEnvCfg):
    """
    Configuration for a reinforcement learning environment for excavation.
    This configuration file is just for the environment itself
    """

    # PID Tuning
    k_d = [25,30,40,40] #  okay 50, 60, 40, 40, good for boulder in bucket 50, 100, 150, 175
    k_p = [10,10,30,30]
    # Timeout
    send_timeouts = True
    bucket_width = 1.4

    @configclass
    class Limits:
        
        action_scaling = (0.1, 0.1, 0.1, 0.2)
        infinite_torque = False

        @configclass
        class Velocity:
            upper = [0.3, 0.6, 0.4, 0.8]
            lower = [-0.3, -0.6, -0.4, -0.8]

        velocity = Velocity()

        @configclass
        class Position:
            upper = [0.4, 2.76, 1.8, 2.32]
            lower = [-1.29, 0.54, 0.0, -0.59]

        # TODO: measure true limits on the machine, change also in URDF!!!
        # from excavator model
        # pos lim upper:      0.400769      2.76691          1.8       2.3289
        # pos lim lower:      -1.29665      0.543349             0      -0.59714

        position = Position()

        # from m545_description/m545_cylinder_definitions.hpp
        @configclass
        class CylinderForce:
            upper = [530100.0, 398100.0, 190800.0, 235500.0]
            lower = [-379180.53, -282611.19, -105894.0, -150908.4]

        cylinder_force = CylinderForce()

        @configclass
        class CylinderVelocity:
            upper = [0.118, 0.308, 0.416, 0.334]
            lower = [-0.132, -0.295, -0.381, -0.33]

        cylinder_velocity = CylinderVelocity()

    limits = Limits()

    @configclass
    class Reset:
        sample_soil = True  # True
        sample_max_depth = False  # not needed, sampling happens in sample soil
        sample_pullup_dist = True  # True
        pullup_dist = 3.0  # fixed if not sampled
        pullup_dist_range = [2.0, 3.5]
        sample_obstacles = False
        fixed_config = False  # False or any of the below (str)
        only_above_soil = True

        @configclass
        class Configs:
            # close = {"idx": 184599, "height": -0.31, "height_idx": 37}
            # medium = {"idx": 140603, "height": -0.92, "height_idx": 61}
            # far = {"idx": 191851, "height": -0.2, "height_idx": 76}
            # medium_flat = {"idx": 194844, "height": -0.15, "height_idx": 58}
            # far_flat = {"idx": 164420, "height": -0.58, "height_idx": 57}
            # close_flat = {"idx": 36197, "height": -2.57, "height_idx": 40}
            # medium_deep = {"idx": 56064, "height": -2.2, "height_idx": 59}
            close = {"idx": 139745, "height": -0.9422, "height_idx": 45}
            medium = {"idx": 182001, "height": -0.3394, "height_idx": 61}
            far = {"idx": 218501, "height": 0.2043, "height_idx": 79}
            # medium_flat = {"idx": 194844, "height": -0.15, "height_idx": 58}
            # far_flat = {"idx": 164420, "height": -0.58, "height_idx": 57}
            # close_flat = {"idx": 36197, "height": -2.57, "height_idx": 40}
            # medium_deep = {"idx": 56064, "height": -2.2, "height_idx": 59}

        configs = Configs()

        @configclass
        class Arm_depth_limtis:  # depth > 0: in soil, depth < 0: above soil
            lower = -0.4
            upper = 0.4

        arm_depth_limits = Arm_depth_limtis()

        min_ang_bucket_to_soil = 0  # negative: pushing with bottom plate; 0: parallel to soil
        max_soil_force = 40000.0
        max_soil_moment = 40000.0
        max_soil_force_and_moment = 40000.0

        @configclass
        class Narrow:
            # additional reset criteria checked for testing/trenching
            enable = False
            max_ang_bucket_to_soil = 100  # effectively no limit, only used for deployment
            x_min = 0
            x_max = 100
            z_min = -10
            z_max = 100

        narrow = Narrow()

    reset = Reset()

    class Soil_height:
        type = "rbf"  # "rbf" or "slope"
        x_min = 0  # m, in world frame
        x_max = 8  # m, in world frame
        z_min = -0.7049  # -2.5  # m, in world frame
        z_max = -0.7049  # 0.5  # m, in world frame
        # rbf params
        min_resolution = 0.1
        theta = 0.5
        scale = 0.2
        # slope params, start=closer to excavator
        slope_start_height = -0.5
        slope_start = 4  # [m] from excavator
        slope_ang = (-30) * 3.14 / 180  # TODO: Use deg2grad fct
        slope_x_len = 3  # [m]

        # if value: average is that value,
        # if none and upper limit: sampled between min and upper_limit.offset
        # if none and no upper limits: sampled between min and max
        # if dict [height, idx]: sets height at pos[idx]
        offset = None  # {"height": -0.5, "idx": -1}

    soil_height = Soil_height()

    @configclass
    class Soil_forces:
        # for debugging
        fee_multiplyer = 1.0
        penetration_edge_multiplyer = 1.0
        penetration_plate_multiplyer = 1.0
        deadload_multiplyer = 1.0

    soil_forces = Soil_forces()

    @configclass
    class Max_depth_height:
        type = "rbf"
        x_min = 0  # m, in world frame
        x_max = 8  # m, in world frame
        z_min = -3.0  # m, in world frame
        z_max = 0.5  # m, in world frame
        # rbf params
        min_resolution = 0.1
        theta = 0.5
        scale = 0.2
        # slope params, start=closer to excavator
        slope_start_height = -0.5
        slope_start = 4  # [m] from excavator
        slope_ang = (-30) * 3.14 / 180  # TODO: Replace by deg2grad
        slope_x_len = 3  # [m]

        offset = None  # if value: average is that value, if none: sampled between min and max
        clip_margin = 0.05  # margin, if upper_limit clipped

    max_depth_height = Max_depth_height()

    @configclass
    class Bucket:
        a = 0.525  # bottom plate length
        da = 0.0105  # discretization step of bottom plate for finding intersection with soil
        b = 1.4  # bucket with
        r = 0.375  # bucket radius
        edge_half_angle = 0.1  # for penetration model
        edge_top_width = 0.015  # for penetration model

    bucket = Bucket()

    @configclass
    class Ssp:
        # highest power first, scalar end, alpha = f^-1(A) ~ poly(A)
        ssp_angle_poly_coeffs = [
            -6.56853263e02,
            9.55497849e02,
            -5.49159120e02,
            1.63194692e02,
            -2.76530025e01,
            4.64763769e00,
            4.98590828e-03,
        ]
        dL_max = 0.01  # ssp discretization for finding intersection with soil
        L_over_max = 1.1  # on this length, we check if ssp exits soil

    ssp = Ssp()

    @configclass
    class Soil_parameters:
        type = "s_0_0"  # "S_0_0"  # random or specific type

        @configclass
        class S_0_0:
            c = 0  # [0-105 kPa] soil cohesion [Pa]
            ca_f = 0  # [0-100 %] soil adhesion fraction of cohesion
            phi = 0.55  # soil internal friction angle [17-45°]
            gamma = 19500  # soil unit weight = density * g [N/m^3] 17-22kN/m^3
            delta = 0.4  # [11°-22°] = [0.19 - 0.38] soil - metal friction angle
            alpha = 0  # soil surface inclination
            CP = 1  # factor lumping cavity expansion model - limit pressure p = f*p0 [50-350]

        s_0_0 = S_0_0()

        @configclass
        class S_0_1:
            c = 0
            ca_f = 0
            phi = 0.77
            gamma = 21500
            delta = 0.4
            alpha = 0
            CP = 300

        s_0_1 = S_0_1()

        @configclass
        class S_1_0:
            c = 20000
            ca_f = 0.5
            phi = 0.47
            gamma = 18000
            delta = 0.3
            alpha = 0
            CP = 1

        s_1_0 = S_1_0()

        @configclass
        class S_1_1:
            c = 20000
            ca_f = 0.5
            phi = 0.59
            gamma = 18000
            delta = 0.3
            alpha = 0
            CP = 300

        s_1_1 = S_1_1()

        @configclass
        class S_2_0:
            c = 60000
            ca_f = 0.5
            phi = 0.31
            gamma = 18000
            delta = 0.4
            alpha = 0
            CP = 1

        s_2_0 = S_2_0()

        @configclass
        class S_2_1:
            c = 60000
            ca_f = 0.5
            phi = 0.56
            gamma = 21000
            delta = 0.4
            alpha = 0
            CP = 300

        s_2_1 = S_2_1()

        @configclass
        class S_3_0:
            c = 105000
            ca_f = 0.5
            phi = 0.31
            gamma = 18000
            delta = 0.4
            alpha = 0
            CP = 1

        s_3_0 = S_3_0()

        @configclass
        class S_3_1:
            c = 105000
            ca_f = 0.5
            phi = 0.56
            gamma = 21000
            delta = 0.4
            alpha = 0
            CP = 300

        s_3_1 = S_3_1()

    soil_parameters = Soil_parameters()

    @configclass
    class Curriculum_Utils:
        exp_f = 0.01
        start_curl_fill_ratio = 0.4
        end_curl_fill_ratio = 0.9
        start_term_fill_ratio = 0.5
        end_term_fill_ratio = 0.98
        start_height_above_soil = 0.0
        end_height_above_soil = 0.9
        start_pullup_band = 1.0  # [m] band to allow for positive termination (furter away than pullup dist)
        end_pullup_band = 0.3  # [m] band to allow for positive termination (furter away than pullup dist)
        start_spilling_depth_margin = 0.05
        end_spilling_depth_margin = 0.05
        rbf_theta_cf = 1

    curriculum_utils = Curriculum_Utils()

    @configclass
    class Terminations_Excavation:
        @configclass
        class Negative_terminations:
            bucket_aoa = True
            bucket_vel = True
            base_vel = True
            joint_vel = True
            bucket_height = True
            invalid_soil_model = True
            self_collision = True
            max_depth = True
            pullup = True
            spilling_soil = True
            stuck = True

        negative_terminations = Negative_terminations()

        @configclass
        class Positive_terminations:
            desired_close = 5  # UNCOMMENT ALSO IN TERMINATIONS
            desired_full = 10

        positive_terminations = Positive_terminations()

        # config
        disable_negative_termination = False
        neg_term_rew = -1

        # other params
        max_bucket_vel = 1 #0.5  # m/s
        bucket_vel_aoa_threshold = 0.075  # m/s
        bucket_vel_spillig_threshold = 0.075  # m/s
        max_base_vel = 0.1 #0.11  # m/s, lin vel
        max_joint_vel = 1
        max_bucket_height_above_soil = 1.0
        term_above_soil_fill_ratio = 0.01
        max_depth_overshoot = 0.05  # terminate if depth > max_depth + max_depth_overshoot
        max_curl_ang = 0.1  # want smaller than this = more curled
        min_curl_ang = -0.2  # want larger than this = less curled, no overcurling!!

    terminations_excavation = Terminations_Excavation()

    @configclass
    class Terminations_Boulder:
        @configclass
        class Negative_terminations:
            bucket_aoa = True
            bucket_vel = True
            base_vel = True
            joint_vel = True
            bucket_height = True
            invalid_soil_model = True
            self_collision = True
            max_depth = True
            pullup = True
            spilling_soil = True
            stuck = True

        negative_terminations = Negative_terminations()

        @configclass
        class Positive_terminations:
            desired_close = 5  # UNCOMMENT ALSO IN TERMINATIONS
            desired_full = 10

        positive_terminations = Positive_terminations()

        # config
        disable_negative_termination = False
        neg_term_rew = -1
        pos_term_rew = 10

        # other params
        max_bucket_vel = 1.2  # 0.5  # m/s
        bucket_vel_aoa_threshold = 0.075  # m/s
        bucket_vel_spillig_threshold = 0.075  # m/s
        max_base_vel = 0.1  # 0.11  # m/s, lin vel
        max_joint_vel = 1
        max_bucket_height_above_soil = 1.0
        term_above_soil_fill_ratio = 0.01
        max_depth_overshoot = 0.05  # terminate if depth > max_depth + max_depth_overshoot
        max_curl_ang = 0.1  # want smaller than this = more curled
        min_curl_ang = -0.2  # want larger than this = less curled, no overcurling!!

    terminations_boulder = Terminations_Boulder()

    @configclass
    class Observations_Excavation:
        num_soil_height_futures = 5  # current max depth + future points
        soil_height_futures_spacing = 0.2  # m
        num_max_depth_futures = 5  # current max depth + future points
        max_depth_futures_spacing = 0.2  # m
        num_soil_normal_futures = 0  # current max depth + future points
        max_soil_normal_futures_spacing = 0.5  # m

        @configclass
        class Enable:
            soil_normals = False  # TODO: add to obs? infer from soil heights?
            max_depth = True
            pullup_dist = True
            soil_parameters = False
            torques = True

        enable = Enable()

    observations_excavation = Observations_Excavation()

    @configclass
    class Rewards_Excavation:
        @configclass
        class Scales:
            bucket_filling = 1.0  # 1.0
            action_rate = -0.005
            max_depth_tracking = 0.04  # 0.04
            bucket_curl = 0.1  # 0.05
            pitch_up = 0.1  # 0.1
            bucket_edge_up = 0.0  # 0.05  # 0.1
            bucket_edge_down = -0.1  # -0.05  # -0.1
            power = 1e-6
            const = 0.0
            bucket_curl_and_up = 0.0

        scales = Scales()
        # config
        # if true negative total rewards are clipped at zero (avoids early termination problems)
        only_positive_rewards = False
        print_debug = False

        # exponential factors
        max_depth_tracking_sigma = 0.01  # tracking reward = exp(-error^2/sigma)
        max_depth_tracking_offset = 0.0  # get full reward for tracking max_depth + max_depth_tracking_offset
        # other params
        go_down_fill_ratio = 0.01  # if less than this, encourage to go down

    rewards_excavation = Rewards_Excavation()
