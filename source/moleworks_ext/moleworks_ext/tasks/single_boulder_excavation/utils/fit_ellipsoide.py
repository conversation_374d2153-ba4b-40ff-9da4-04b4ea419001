import numpy as np
from numpy.linalg import eig, inv
from scipy.spatial import ConvexHull, convex_hull_plot_2d

from pxr import Usd, UsdGeom


def ls_ellipsoid(xx, yy, zz):
    # finds best fit ellipsoid. Found at http://www.juddzone.com/ALGORITHMS/least_squares_3D_ellipsoid.html
    # least squares fit to a 3D-ellipsoid
    #  Ax^2 + By^2 + Cz^2 +  Dxy +  Exz +  Fyz +  Gx +  Hy +  Iz  = 1
    #
    # Note that sometimes it is expressed as a solution to
    #  Ax^2 + By^2 + Cz^2 + 2Dxy + 2Exz + 2Fyz + 2Gx + 2Hy + 2Iz  = 1
    # where the last six terms have a factor of 2 in them
    # This is in anticipation of forming a matrix with the polynomial coefficients.
    # Those terms with factors of 2 are all off diagonal elements.  These contribute
    # two terms when multiplied out (symmetric) so would need to be divided by two

    # change xx from vector of length N to Nx1 matrix so we can use hstack
    x = xx[:, np.newaxis]
    y = yy[:, np.newaxis]
    z = zz[:, np.newaxis]

    #  Ax^2 + By^2 + Cz^2 +  Dxy +  Exz +  Fyz +  Gx +  Hy +  Iz = 1
    J = np.hstack((x * x, y * y, z * z, x * y, x * z, y * z, x, y, z))
    K = np.ones_like(x)  # column of ones

    # np.hstack performs a loop over all samples and creates
    # a row in J for each x,y,z sample:
    # J[ix,0] = x[ix]*x[ix]
    # J[ix,1] = y[ix]*y[ix]
    # etc.

    JT = J.transpose()
    JTJ = np.dot(JT, J)
    InvJTJ = np.linalg.inv(JTJ)
    ABC = np.dot(InvJTJ, np.dot(JT, K))

    # Rearrange, move the 1 to the other side
    #  Ax^2 + By^2 + Cz^2 +  Dxy +  Exz +  Fyz +  Gx +  Hy +  Iz - 1 = 0
    #    or
    #  Ax^2 + By^2 + Cz^2 +  Dxy +  Exz +  Fyz +  Gx +  Hy +  Iz + J = 0
    #  where J = -1
    eansa = np.append(ABC, -1)

    return eansa


def polyToParams3D(vec, printMe):
    # gets 3D parameters of an ellipsoid. Found at http://www.juddzone.com/ALGORITHMS/least_squares_3D_ellipsoid.html
    # convert the polynomial form of the 3D-ellipsoid to parameters
    # center, axes, and transformation matrix
    # vec is the vector whose elements are the polynomial
    # coefficients A..J
    # returns (center, axes, rotation matrix)

    # Algebraic form: X.T * Amat * X --> polynomial form

    if printMe:
        print("\npolynomial\n", vec)

    Amat = np.array(
        [
            [vec[0], vec[3] / 2.0, vec[4] / 2.0, vec[6] / 2.0],
            [vec[3] / 2.0, vec[1], vec[5] / 2.0, vec[7] / 2.0],
            [vec[4] / 2.0, vec[5] / 2.0, vec[2], vec[8] / 2.0],
            [vec[6] / 2.0, vec[7] / 2.0, vec[8] / 2.0, vec[9]],
        ]
    )

    if printMe:
        print("\nAlgebraic form of polynomial\n", Amat)

    # See B.Bartoni, Preprint SMU-HEP-10-14 Multi-dimensional Ellipsoidal Fitting
    # equation 20 for the following method for finding the center
    A3 = Amat[0:3, 0:3]
    A3inv = inv(A3)
    ofs = vec[6:9] / 2.0
    center = -np.dot(A3inv, ofs)
    if printMe:
        print("\nCenter at:", center)

    # Center the ellipsoid at the origin
    Tofs = np.eye(4)
    Tofs[3, 0:3] = center
    R = np.dot(Tofs, np.dot(Amat, Tofs.T))
    if printMe:
        print("\nAlgebraic form translated to center\n", R, "\n")

    R3 = R[0:3, 0:3]
    R3test = R3 / R3[0, 0]
    # print('normed \n',R3test)
    s1 = -R[3, 3]
    R3S = R3 / s1
    (el, ec) = eig(R3S)

    recip = 1.0 / np.abs(el)
    axes = np.sqrt(recip)
    if printMe:
        print("\nAxes are\n", axes, "\n")

    inve = inv(ec)  # inverse is actually the transpose here
    if printMe:
        print("\nRotation matrix\n", inve)
    return (center, axes, inve)


def fit_ellipsoide_least_squares(points):
    """
        Fit ellipse using least squares
    Args:
        Point cloud
    Returns:
        center: center of the ellipsoid
        radii: axes of the ellipsoid
        evecs: Eigen vectors proving orientation of the ellipse
        scaled_axes: scaled axes of the ellipsoid
    """
    # Divide point cloud in x,y,z
    x = points[:, 0]
    y = points[:, 1]
    z = points[:, 2]
    # get convex hull
    surface = np.stack((x, y, z), axis=-1)
    hullV = ConvexHull(surface)
    lH = len(hullV.vertices)
    hull = np.zeros((lH, 3))
    for i in range(len(hullV.vertices)):
        hull[i] = surface[hullV.vertices[i]]
    hull = np.transpose(hull)

    # fit ellipsoid on convex hull
    eansa = ls_ellipsoid(hull[0], hull[1], hull[2])  # get ellipsoid polynomial coefficients
    # print("coefficients:"  , eansa)
    center, axes, rotationmatrix = polyToParams3D(eansa, False)  # get ellipsoid 3D parameters
    # print("center:"        , center)
    # print("axes:"          , axes)
    # print("rotationMatrix:", inve)
    scaled_axes = np.dot(np.diag(axes), rotationmatrix)
    return center, axes, rotationmatrix, scaled_axes


def plot_ellipsoid(points, center, radii, evecs, scaled_axes):
    import matplotlib.pyplot as plt

    from mpl_toolkits.mplot3d import Axes3D

    fig = plt.figure()
    ax = fig.add_subplot(111, projection="3d")
    x = points[:, 0]
    y = points[:, 1]
    z = points[:, 2]
    # ax.scatter(x, y, z, c='b', marker='.', linewidth=0.01, alpha=0.3)
    ax.scatter(points[:, 0], points[:, 1], points[:, 2])

    cageColor = "r"
    cageAlpha = 0.2
    u = np.linspace(0.0, 2.0 * np.pi, 100)
    v = np.linspace(0.0, np.pi, 100)

    # cartesian coordinates that correspond to the spherical angles:
    x = radii[0] * np.outer(np.cos(u), np.sin(v))
    y = radii[1] * np.outer(np.sin(u), np.sin(v))
    z = radii[2] * np.outer(np.ones_like(u), np.cos(v))

    # rotate accordingly
    for i in range(len(x)):
        for j in range(len(x)):
            [x[i, j], y[i, j], z[i, j]] = np.dot([x[i, j], y[i, j], z[i, j]], evecs) + center

    # Plot axes
    for i in range(3):
        ax.quiver(center[0], center[1], center[2], scaled_axes[i, 0], scaled_axes[i, 1], scaled_axes[i, 2], color="r")

    # plot ellipsoid
    ax.plot_wireframe(x, y, z, rstride=4, cstride=4, color=cageColor, alpha=cageAlpha)

    ax.set_box_aspect([1, 1, 1])
    ax.set_aspect("equal")
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z")
    ax.view_init(elev=0, azim=-72)


def fit_ellipsoide_min_volume(points, debug=True):
    """
        Fit ellipse using minimum volume
    Args:
        Point cloud
    Returns:
        center: center of the ellipsoid
        radii: axes of the ellipsoid
        evecs: Eigen vectors proving orientation of the ellipse
        scaled_axes: scaled axes of the ellipsoid
    """

    # Sample points from the mesh
    point_cloud = points
    center, radii, evecs = getMinVolEllipse(point_cloud)
    scaled_axes = np.dot(np.diag(radii), evecs)

    return center, radii, evecs, scaled_axes


def getMinVolEllipse(P=None, tolerance=0.01):
    """Find the minimum volume ellipsoid which holds all the points
    Code from https://github.com/minillinim/ellipsoid/blob/master/ellipsoid.py

    Based on work by Nima Moshtagh
    http://www.mathworks.com/matlabcentral/fileexchange/9542
    and also by looking at:
    http://cctbx.sourceforge.net/current/python/scitbx.math.minimum_covering_ellipsoid.html
    Which is based on the first reference anyway!

    Here, P is a numpy array of N dimensional points like this:
    P = [[x,y,z,...], <-- one point per line
         [x,y,z,...],
         [x,y,z,...]]

    Returns:
    (center, radii, rotation)

    """
    (N, d) = np.shape(P)
    d = float(d)

    # Q will be our working array
    Q = np.vstack([np.copy(P.T), np.ones(N)])
    QT = Q.T

    # initializations
    err = 1.0 + tolerance
    u = (1.0 / N) * np.ones(N)

    # Khachiyan Algorithm
    while err > tolerance:
        V = np.dot(Q, np.dot(np.diag(u), QT))
        M = np.diag(np.dot(QT, np.dot(np.linalg.inv(V), Q)))  # M the diagonal vector of an NxN matrix
        j = np.argmax(M)
        maximum = M[j]
        step_size = (maximum - d - 1.0) / ((d + 1.0) * (maximum - 1.0))
        new_u = (1.0 - step_size) * u
        new_u[j] += step_size
        err = np.linalg.norm(new_u - u)
        u = new_u

    # center of the ellipse
    center = np.dot(P.T, u)

    # the A matrix for the ellipse
    A = np.linalg.inv(np.dot(P.T, np.dot(np.diag(u), P)) - np.array([[a * b for b in center] for a in center])) / d

    # Get the values we'd like to return
    U, s, rotation = np.linalg.svd(A)
    radii = 1.0 / np.sqrt(s)

    return (center, radii, rotation)
