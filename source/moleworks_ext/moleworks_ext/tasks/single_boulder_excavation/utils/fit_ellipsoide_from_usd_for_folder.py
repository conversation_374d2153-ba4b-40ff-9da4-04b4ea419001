# Load the CSV file into a NumPy array
import argparse
import matplotlib.pyplot as plt
import numpy as np

from mpl_toolkits.mplot3d import Axes3D

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Tutorial on adding sensors on a robot.")
parser.add_argument("--num_envs", type=int, default=1, help="Number of environments to spawn.")
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
import csv
import numpy as np
import os

# launch omniverse app
app_launcher = AppLauncher(args_cli)
from pxr import Usd, UsdGeom

from moleworks_ext.tasks.single_boulder_excavation.utils.fit_ellipsoide import (
    fit_ellipsoide_least_squares,
    fit_ellipsoide_min_volume,
)

# Folder containing USD files
folder_path = "/home/<USER>/Documents/Omniverse_m545/rocks_rigid_objects/"
output_csv = "/home/<USER>/Coding/IsaacLab/source/extensions/isaaclab_tasks/omni/isaac/lab_tasks/manager_based/m545/utils/single_boulder_excavation/ellipsoid_data_min_volume.csv"


def array_to_str(array):
    return ",".join(map(str, array.flatten()))


# Prepare CSV file
with open(output_csv, mode="w", newline="") as file:
    writer = csv.writer(file)
    writer.writerow(["File", "Center", "Radii", "Eigenvectors", "Scaled Axes"])  # CSV headers

    # Loop over all USD files in the folder
    for filename in os.listdir(folder_path):
        if filename.endswith(".usd"):
            usd_path = os.path.join(folder_path, filename)

            # Open the USD file
            stage = Usd.Stage.Open(usd_path)
            mesh_found = False

            # Look for the mesh in the USD file
            for prim in stage.Traverse():
                if prim.IsA(UsdGeom.Mesh):
                    geom = UsdGeom.Mesh(prim)
                    points = np.array(geom.GetPointsAttr().Get())
                    mesh_found = True
                    break

            if not mesh_found:
                print(f"No mesh found in {filename}. Skipping.")
                continue

            # Fit ellipsoid to the points
            center, radii, evecs, scaled_axes = fit_ellipsoide_min_volume(points)

            # Write the results to the CSV file
            # Write data to the CSV file
            writer.writerow(
                [filename, array_to_str(center), array_to_str(radii), array_to_str(evecs), array_to_str(scaled_axes)]
            )

print(f"Ellipsoid data written to {output_csv}.")
