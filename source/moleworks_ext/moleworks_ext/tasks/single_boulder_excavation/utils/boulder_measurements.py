import numpy as np
import torch

import isaacsim.core.utils.torch as torch_utils

from .utils import find_ellipsoide_data


class BoulderMeasurements:
    def __init__(self, num_envs, device) -> None:
        self.num_envs = num_envs
        self.device = device

        # Boulder
        self.boulder_count = torch.zeros(self.num_envs, device=self.device)
        self.init_measurements_buffers()

        print("Rocks initialized")

    def init_measurements_buffers(self):
        # Linear and angular velocity of the root of the asset in world frame
        self.root_lin_vel_w = torch.zeros((self.num_envs, 3), device=self.device)
        self.root_ang_vel_w = torch.zeros((self.num_envs, 3), device=self.device)
        self.root_quat_w = torch.zeros((self.num_envs, 4), device=self.device)
        self.root_pos_map = torch.zeros((self.num_envs, 3), device=self.device)
        self.prev_root_pos_w = torch.zeros((self.num_envs, 3), device=self.device)
        self.root_pos_w = torch.zeros((self.num_envs, 3), device=self.device)
        # position in bucket frame
        self.pos_b = torch.zeros(self.num_envs, 3, device=self.device)

        # boulder in shovel
        self.in_shovel = torch.zeros(self.num_envs, device=self.device)
        self.ellipsoid_axes_w = torch.zeros(self.num_envs, 9, device=self.device)
        #
        self.x_min = torch.zeros(self.num_envs, device=self.device)
        self.x_max = torch.zeros(self.num_envs, device=self.device)

    def post_init(self, env):
        self.env = env
        self.m545_measurements = env.m545_measurements
        self.boulder = env.scene["object_0"]
        # Boulder info
        usd_files = self.boulder.cfg.spawn.usd_list
        scales = self.boulder.cfg.spawn.scale_list
        self.scales = torch.tensor(self.boulder.cfg.spawn.scale_list, device=self.device)
        # Buffers to store information about boulders
        self.axes = torch.zeros((self.num_envs, 3, 3), device=self.device, dtype=torch.float32)
        self.centers = torch.zeros((self.num_envs, 3), device=self.device, dtype=torch.float32)
        self.radiis = torch.zeros((self.num_envs, 3), device=self.device, dtype=torch.float32)
        self.start_height = torch.zeros((self.num_envs), device=self.device, dtype=torch.float32)

        # Loop through files (Attention this is for one Object per env, otherwise see pattern)
        for i in range(len(usd_files)):
            usd_file = usd_files[i] + ".usd"
            scale = scales[i]
            # Least square error ellipse, for collision
            center, radii, _, scaled_axes = find_ellipsoide_data(usd_file)
            scale = scales[i]
            self.axes[i, :] = torch.tensor(scaled_axes, device=self.device, dtype=torch.float32) * scale
            self.centers[i, :] = torch.tensor(center, device=self.device, dtype=torch.float32)
            self.radiis[i, :] = torch.tensor(radii, device=self.device, dtype=torch.float32)

            # minimum volume, for spawning
            # _, radii_min, _, _ = find_ellipsoide_data(usd_file, csv_file='/home/<USER>/Coding/IsaacLab/source/extensions/isaaclab_tasks/omni/isaac/lab_tasks/manager_based/m545/utils/single_boulder_excavation/ellipsoid_data_min_volume.csv')
        # masses
        self.original_masses = self.boulder.root_physx_view.get_masses()
        #  Correct info
        self.root_lin_vel_w = self.boulder.data.root_lin_vel_w[:]  # torch.zeros((self.num_envs, 3), device=self.device)
        self.root_ang_vel_w = self.boulder.data.root_ang_vel_w[
            :, :
        ]  # torch.zeros((self.num_envs, 3), device=self.device)
        self.root_quat_w = self.boulder.data.root_quat_w[:, :]  # torch.zeros((self.num_envs, 4), device=self.device)
        self.root_pos_map = self.boulder.data.root_pos_w[:, :]  # torch.zeros((self.num_envs, 3), device=self.device)
        self.prev_root_pos_w = self.boulder.data.root_pos_w[:, :] - self.env.scene.env_origins
        self.root_pos_w = self.boulder.data.root_pos_w[:, :] - self.env.scene.env_origins
        # extremeties

        self.update_measurements()

    def update_measurements(self):
        # Linear and angular velocity of the root of the asset in world frame
        self.root_lin_vel_w = self.boulder.data.root_lin_vel_w[:]  # torch.zeros((self.num_envs, 3), device=self.device)
        self.root_ang_vel_w = self.boulder.data.root_ang_vel_w[
            :, :
        ]  # torch.zeros((self.num_envs, 3), device=self.device)
        self.root_quat_w = self.boulder.data.root_quat_w
        self.root_quat_w = self.boulder.data.root_quat_w[:, :]  # torch.zeros((self.num_envs, 4), device=self.device)
        self.root_pos_map = self.boulder.data.root_pos_w[:, :]  # torch.zeros((self.num_envs, 3), device=self.device)
        self.prev_root_pos_w = self.root_pos_w
        self.root_pos_w = self.boulder.data.root_pos_w[:, :] - self.env.scene.env_origins

        # Uptes pos of boulder in the bucket and computes if boulder in Bucket

        axes_x_w = torch_utils.quat_rotate(
            self.root_quat_w[:], self.axes[:, 0, :]
        )  # torch.tile(axes[0, :], (num_envs, 1)))
        axes_y_w = torch_utils.quat_rotate(
            self.root_quat_w[:], self.axes[:, 1, :]
        )  # torch.tile(axes[1, :], (num_envs, 1)))
        axes_z_w = torch_utils.quat_rotate(
            self.root_quat_w[:], self.axes[:, 2, :]
        )  # torch.tile(axes[2, :], (num_envs, 1)))
        self.ellipsoid_axes_w = torch.stack([axes_x_w, axes_y_w, axes_z_w], dim=1).reshape(-1, 9)
        # extremeties
        x_extr1 = self.ellipsoid_axes_w[:, 0]
        x_extr2 = -self.ellipsoid_axes_w[:, 0]
        x_extr = torch.stack([x_extr1, x_extr2])
        self.x_min = x_extr.min() + self.root_pos_w[:, 0]
        self.x_max = x_extr.max() + self.root_pos_w[:, 0]

        # Check if boulder is inside the shovel
        pos_diff = self.root_pos_w[:] - self.m545_measurements.bucket_pos_w[:]
        
        #print(" self.m545_measurements.bucket_pos_w ",  self.m545_measurements.bucket_pos_w)
        #print(" self.m545_measurements.bucket_pos_w_old ",  self.m545_measurements.bucket_pos_w_old)
        #print(" self.m545_measurements.quat_bucket_w ",  self.m545_measurements.quat_bucket_w)
        #print(" self.m545_measurements.quat_bucket_w_old ",  self.m545_measurements.quat_bucket_w_old)
        #print("diff pos",self.m545_measurements.bucket_pos_w-self.m545_measurements.bucket_pos_w_old )
        #print("diff quat",self.m545_measurements.quat_bucket_w-self.m545_measurements.quat_bucket_w_old )
        
        # Calculate the norms in a batch-wise manner
        distances = torch.linalg.norm(pos_diff, dim=1)

        # Create a mask where the distance is less than the threshold
        mask = distances < 0.8

        # Preallocate tensors for pos_b and in_shovel
        self.pos_b.zero_()  # = torch.zeros_like(self.pos_w)
        self.in_shovel.zero_()  # = torch.zeros(self.num_envs, device=self.device)

        rot_vec = self.m545_measurements.quat_bucket_w_old[:]
        self.pos_b = torch_utils.quat_rotate_inverse(rot_vec, pos_diff)
        

        # If no boulders are close: skip
        if mask.any():
            # Check if boulder is in shovel only for environments where mask is True
            self.in_shovel[mask] = torch.where(
                (self.pos_b[mask, 0] < self.m545_measurements.bucket_max_x_b[mask, 0])
                & (self.pos_b[mask, 0] > self.m545_measurements.bucket_min_x_b[mask, 0])
                & (self.pos_b[mask, 1] < self.m545_measurements.bucket_max_y_b[mask, 0])
                & (self.pos_b[mask, 1] > self.m545_measurements.bucket_min_y_b[mask, 0])
                & (self.pos_b[mask, 2] < self.m545_measurements.bucket_max_z_b[mask, 0])
                & (self.pos_b[mask, 2] > self.m545_measurements.bucket_min_z_b[mask, 0]),
                torch.ones(mask.sum(), device=self.device),
                torch.zeros(mask.sum(), device=self.device),
            )

            #print('Relative pos',self.pos_b[mask, :])
            #print('cond 1', (self.pos_b[mask, 0] < self.m545_measurements.bucket_max_x_b[mask, 0]) & (self.pos_b[mask, 0] > self.m545_measurements.bucket_min_x_b[mask, 0]))
            #print('cond 2', (self.pos_b[mask, 1] < self.m545_measurements.bucket_max_y_b[mask, 0]) & (self.pos_b[mask, 1] > self.m545_measurements.bucket_min_y_b[mask, 0]))
            #print('cond 3', (self.pos_b[mask, 2] < self.m545_measurements.bucket_max_z_b[mask, 0]) & (self.pos_b[mask, 2] > self.m545_measurements.bucket_min_z_b[mask, 0]))
            #print('-----------')
