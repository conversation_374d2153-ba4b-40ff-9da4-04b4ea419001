# Copyright (c) 2022-2024, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
As the curriculum is handled differently then in Orbit, a custom manager for the excavation environment is implemented.
The main reason is that the curriculum has to update its own buffers over iterations.
"""

from __future__ import annotations

import torch



class Curriculum_Boulder:
    def __init__(self, env):
        self.env = env
        self.device = self.env.device

        # params
        self.exp_f = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.exp_f

        self.start_term_fill_ratio = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_term_fill_ratio
        )
        self.end_term_fill_ratio = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_term_fill_ratio

        self.start_height_above_soil = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_height_above_soil
        )
        self.end_height_above_soil = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_height_above_soil
        )

        self.start_curl_fill_ratio = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_curl_fill_ratio
        )
        self.end_curl_fill_ratio = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_curl_fill_ratio

        self.start_pullup_band = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_pullup_band
        self.end_pullup_band = torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_pullup_band

        self.start_spilling_depth_margin = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.start_spilling_depth_margin
        )
        self.end_spilling_depth_margin = (
            torch.ones(1, device=self.device) * self.env.cfg.curriculum_utils.end_spilling_depth_margin
        )

        self.curr_term_fill_ratio = self.start_term_fill_ratio.clone()
        self.curr_term_height_above_soil = self.start_height_above_soil.clone()
        self.curr_curl_ratio = self.start_curl_fill_ratio.clone()
        self.curr_pullup_band = self.start_pullup_band.clone()
        self.curr_spilling_depth_margin = self.start_spilling_depth_margin.clone()

        self.algo_iter = 0
        self.level = torch.zeros(1, device=self.device)
        self.level_boulder = torch.zeros(1, device=self.device)
        self.full_term_mean = torch.zeros(1, device=self.device)
        self.close_term_mean = torch.zeros(1, device=self.device)
        self.timeout_term_mean = torch.zeros(1, device=self.device)
        self.theta = torch.zeros(1, device=self.device)
        self.completed = False


    def update_curriculum_excavation(self):
        # performance_better = (-self.last_full_term_mean + self.full_term_mean) > 0.0

        # self.level += performance_better * (self.full_term_mean > 0.5)
        #self.level += self.env.termination_excavation.full_term_mean + self.env.termination_excavation.close_term_mean > 0.5
        if self.level_boulder == 0:
            #episodic_sum_avg = torch.mean(self.env.reward_manager._episode_sums["termination_reward_pos"][env_ids])
            pos_rew_key = "Episode_Reward/" + "termination_reward_pos"
            if "log" in self.env.extras and pos_rew_key in self.env.extras["log"]:
                episode_reward_pos_term = self.env.extras["log"][pos_rew_key]
                if episode_reward_pos_term > 0.1:
                    self.env.cfg.soil_parameters.type = "random"
                    self.level_boulder[:] = 1
        elif self.level_boulder == 1:
            pos_rew_key = "Episode_Reward/" + "termination_reward_pos"
            if "log" in self.env.extras and pos_rew_key in self.env.extras["log"]:
                episode_reward_pos_term = self.env.extras["log"][pos_rew_key]
                if episode_reward_pos_term > 0.15:
                    self.env.cfg.soil_parameters.type = "s_3_1"
                    self.level_boulder[:] = 2
        elif self.level_boulder == 2:
             '''pos_rew_key = "Episode_Reward/" + "termination_reward_pos"
             if "log" in self.env.extras and pos_rew_key in self.env.extras["log"]:
                episode_reward_pos_term = self.env.extras["log"][pos_rew_key]  
                if episode_reward_pos_term > 0.19:
                    self.level_boulder[:] = 3
            
            '''
             pos_rew_key = "Episode_Termination/" + "positive_termination_boulder_in_bucket"
             if "log" in self.env.extras and pos_rew_key in self.env.extras["log"]:
                episode_reward_pos_term = self.env.extras["log"][pos_rew_key]    
                if episode_reward_pos_term > 150:
                    self.level_boulder[:] = 3
            
             
             '''
                if self.env.num_ppo_update  > 1000:
               self.level_boulder[:] = 3
            '''
        # linear 0-500
        self.level1 = torch.minimum(self.level, torch.tensor(250.0, device=self.device))
        self.level2 = torch.minimum(self.level, torch.tensor(500.0, device=self.device))
        self.level3 = torch.minimum(self.level, torch.tensor(750.0, device=self.device))

        self.cf = self.level1 / 250.0
        self.cf2 = self.level2 / 500.0
        self.cf3 = self.level3 / 750.0

        # exponential
        # self.cf = 1.0 - torch.exp(-self.exp_f * self.level)

        self.curr_term_fill_ratio[:] = self.start_term_fill_ratio + self.cf * (
            self.end_term_fill_ratio - self.start_term_fill_ratio
        )

        self.curr_curl_ratio[:] = self.start_curl_fill_ratio + self.cf * (
            self.end_curl_fill_ratio - self.start_curl_fill_ratio
        )

        self.curr_term_height_above_soil[:] = self.start_height_above_soil + self.cf * (
            self.end_height_above_soil - self.start_height_above_soil
        )
        self.curr_pullup_band[:] = self.start_pullup_band + self.cf * (self.end_pullup_band - self.start_pullup_band)

        self.curr_spilling_depth_margin[:] = self.start_spilling_depth_margin + self.cf * (
            self.end_spilling_depth_margin - self.start_spilling_depth_margin
        )
        # reduce go down and go up rewards with curriculum
        # go_down_idx = self.env.reward_manager.reward_names.index("bucket_edge_down")
        # self.env.reward_manager.reward_scales[go_down_idx] = (
        #     1.0 - self.cf
        # ) * self.env.cfg.rewards.scales.bucket_edge_down

        # curl_idx = self.env.reward_manager.reward_names.index("bucket_curl")
        # self.env.reward_manager.reward_scales[curl_idx] = (1.0 - self.cf) * self.env.cfg.rewards.scales.bucket_curl

        # go_up_idx = self.env.reward_manager.reward_names.index("pitch_up")
        # self.env.reward_manager.reward_scales[go_up_idx] = (1.0 - self.cf) * self.env.cfg.rewards.scales.pitch_up

        if self.env.cfg.curriculum_utils.rbf_theta_cf == 1:
            self.theta = self.cf / 2.0

            if self.cf < 1.0:
                self.env.soil.soil_height.compute_norm_transform(theta=self.theta)
                self.env.soil.max_depth_height.compute_norm_transform(theta=self.theta)

        if self.env.cfg.curriculum_utils.rbf_theta_cf == 2:
            self.theta = self.cf2 / 2.0

            if self.cf2 < 1.0:
                self.env.soil.soil_height.compute_norm_transform(theta=self.theta)
                self.env.soil.max_depth_height.compute_norm_transform(theta=self.theta)

        if self.env.cfg.curriculum_utils.rbf_theta_cf == 3:
            self.theta = self.cf3 / 2.0

            if self.cf3 < 1.0:
                self.env.soil.soil_height.compute_norm_transform(theta=self.theta)
                self.env.soil.max_depth_height.compute_norm_transform(theta=self.theta)

    def reset_excavation(self):
        extras = {}
        extras["Curriculum/level_boulder"] = self.level_boulder.item()
        #extras["Curriculum/level2"] = self.level2.item()
        #extras["Curriculum/level3"] = self.level3.item()
        return extras

    def set_level_and_update(self, level):
        self.level = level
        self.update_curriculum_excavation()
        print("updating curriculum level!")
