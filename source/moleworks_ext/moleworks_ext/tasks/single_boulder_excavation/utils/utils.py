import csv
import numpy as np
import os
import pathlib

LAB_M545_DIR = pathlib.Path(__file__).parents[0]

ELLIPSE_CSV_PATH = os.path.join(LAB_M545_DIR, "ellipsoid_data_least_square.csv")


def find_ellipsoide_data(filename_to_find, csv_file=ELLIPSE_CSV_PATH):
    with open(csv_file) as file:
        reader = csv.DictReader(file)

        for row in reader:
            if row["File"] == filename_to_find:
                center = str_to_array(row["Center"], shape=(3,))
                radii = str_to_array(row["Radii"], shape=(3,))
                evecs = str_to_array(row["Eigenvectors"], shape=(3, 3))
                scaled_axes = str_to_array(row["Scaled Axes"], shape=(3, 3))

                return center, radii, evecs, scaled_axes

        return None  # Return None if the file is not found


# Helper function to convert a comma-separated string back to a NumPy array
def str_to_array(s, shape=None):
    arr = np.array(list(map(float, s.split(","))))
    if shape:
        arr = arr.reshape(shape)
    return arr
