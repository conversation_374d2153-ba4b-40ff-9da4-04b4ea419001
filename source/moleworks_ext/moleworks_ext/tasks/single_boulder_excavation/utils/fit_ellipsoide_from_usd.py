# Load the CSV file into a NumPy array
import argparse
import matplotlib.pyplot as plt
import numpy as np

from mpl_toolkits.mplot3d import Axes3D

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Tutorial on adding sensors on a robot.")
parser.add_argument("--num_envs", type=int, default=1, help="Number of environments to spawn.")
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app
import numpy as np
from numpy.linalg import eig, inv
from scipy.spatial import ConvexHull, convex_hull_plot_2d

from pxr import Usd, UsdGeom

from moleworks_ext.tasks.single_boulder_excavation.utils.fit_ellipsoide import *

stage = Usd.Stage.Open("/home/<USER>/Documents/Omniverse_m545/rocks_rigid_objects/0001.usd")
mesh_found = False
for prim in stage.Traverse():
    if prim.IsA(UsdGeom.Mesh):
        geom = UsdGeom.Mesh(prim)
        import numpy as np

        points = np.array(geom.GetPointsAttr().Get())
        mesh_found = True
        break

center, radii, evecs, scaled_axes = fit_ellipsoide_least_squares(points)
plot_ellipsoid(points, center, radii, evecs, scaled_axes)
center, radii, evecs, scaled_axes = fit_ellipsoide_min_volume(points)
plot_ellipsoid(points, center, radii, evecs, scaled_axes)
plt.show()
