"""
    This class measurements buffers of the excavation agent that gets updated every steps/ inter-decim steps.
"""

import numpy as np
import torch

import isaacsim.core.utils.torch as torch_utils
from moleworks_ext.common.utils.m545_measurements import M545Measurements

class M545MeasurementsBoulder(M545Measurements):
    def __init__(self, cfg, num_joints, num_envs, device, env):
        super().__init__(cfg, num_joints, num_envs, device, env)
    
    def initialize_asset(self):
        super().initialize_asset()

        self.net_contact_forces = self.env.scene.sensors["contact_forces"].data.net_forces_w
        self.bucket_contact_force = self.net_contact_forces[:, -1, :]
        self.contact_f_genco_tau = torch.matmul(
            self.bucket_jac_lin_T_dof[:], self.bucket_contact_force[:].unsqueeze(-1)
        )

        pos = torch.zeros_like(self.j_pitch_pos) - self.j_pitch_pos
        boulder_tau = torch.cross(pos, self.bucket_contact_force, dim=1)
        self.contact_tau_force = self.contact_f_genco_tau.squeeze(-1)[:, -self.num_dofs :]
        contact_m_genco_tau = torch.matmul(self.bucket_jac_rot_T_dof[:], boulder_tau.unsqueeze(-1))
        self.contact_tau_moment = contact_m_genco_tau.squeeze(-1)[:, -self.num_dofs :]
        
        self.is_first_contact = torch.ones(self.num_envs, device=self.device, dtype=torch.bool)
        # 
        max_contact_points_per_env = 200
        self.contact_normal_forces        = torch.zeros(self.num_envs, max_contact_points_per_env, 1, device=self.device)
        self.contact_points               = torch.zeros(self.num_envs, max_contact_points_per_env, 3, device=self.device)
        self.contact_normal_directions    = torch.zeros(self.num_envs, max_contact_points_per_env, 3, device=self.device)

        self.contact_tau_ext_force = torch.zeros_like(self.contact_tau_force)
        self.contact_tau_ext_moment = torch.zeros_like(self.contact_tau_force)
        self.filtered_contact_tau_ext_force = torch.zeros_like(self.contact_tau_force)
        self.filtered_contact_tau_ext_moment = torch.zeros_like(self.contact_tau_force)
        # Additional measurements constant about the bucket specific for single rock excavation
        # Corner positions in bucket frame
        self.bucket_fc1_pos_b = torch.tensor([0.0, 0.5 * self.cfg.bucket_width, 0.0], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_fc2_pos_b = torch.tensor([0.0, -0.5 * self.cfg.bucket_width, 0.0], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_fc3_pos_b = torch.tensor([-0.5594, 0.5 * self.cfg.bucket_width, 0.4909], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_fc4_pos_b = torch.tensor(
            [-0.5594, -0.5 * self.cfg.bucket_width, 0.4909], device=self.device
        ).expand(self.num_envs, -1)
        self.bucket_bc1_pos_b = torch.tensor([0.0, 0.5 * self.cfg.bucket_width, 0.62], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_bc2_pos_b = torch.tensor([0.0, -0.5 * self.cfg.bucket_width, 0.62], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_bc3_pos_b = torch.tensor([-0.5069, 0.5 * self.cfg.bucket_width, 0.8309], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_bc4_pos_b = torch.tensor(
            [-0.5069, -0.5 * self.cfg.bucket_width, 0.8309], device=self.device
        ).expand(self.num_envs, -1)
        #  Shovel boundaries
        self.bucket_max_x_b = torch.tensor([0.55], device=self.device).expand(self.num_envs, -1)
        self.bucket_min_x_b = torch.tensor([0], device=self.device).expand(self.num_envs, -1)
        self.bucket_max_y_b = torch.tensor([0.5 * self.cfg.bucket_width], device=self.device).expand(self.num_envs, -1)
        self.bucket_min_y_b = torch.tensor([-0.5 * self.cfg.bucket_width], device=self.device).expand(self.num_envs, -1)
        self.bucket_max_z_b = torch.tensor([0.78], device=self.device).expand(self.num_envs, -1)
        self.bucket_min_z_b = torch.tensor([0.0], device=self.device).expand(self.num_envs, -1)
        # point inside bucket to calculate distance to boulder
        self.boulder_ref_b = torch.tensor([-0.4, 0.0, 0.15], device=self.device).expand(self.num_envs, -1)
        # const offset vector eef to pitch in eef
        self.e_r_ep = torch.tensor([-1.3888, 0.0, 0.291139], device=self.device).expand(self.num_envs, -1)
        # const offset vector pitch to eef in pitch
        self.p_r_pe = torch.tensor([1.418, 0.0, 0.053], device=self.device).expand(self.num_envs, -1)
        # cabin: constant cabin pos in base frame (base and cabin frame have same orientation as long as turn joint is 0!!)
        self.b_r_bc = torch.tensor([0.0, 0.0, 0.516], device=self.device).expand(self.num_envs, -1)
        # constant rot pitch to eef
        self.quat_pe = torch.tensor([0.0, -0.121698, 0.0, 0.992567], device=self.device).expand(self.num_envs, -1)
        # constant force application point pitch to force application point in pitch
        self.p_r_pf = torch.tensor([1.268, 0.0, 0.3], device=self.device).expand(self.num_envs, -1)
        # BUcket boundaries
        # Force Application point for Torque estimation
        self.coms = self.asset.root_physx_view.get_coms().to(self.device)[0]

        self.w_r_pf = torch_utils.quat_rotate(self.j_pitch_quat, self.p_r_pf)
        self.w_r_pcom = torch_utils.quat_rotate(self.j_pitch_quat, self.coms[-1][:3].expand(self.num_envs, -1))

        self.prev_bucket_pos_w_old = torch.zeros_like(self.bucket_pos_w)
        self.bucket_pos_map_old = torch.zeros_like(self.bucket_pos_w)
        self.bucket_pos_w_old = torch.zeros_like(self.bucket_pos_w)
        self.quat_bucket_w_old = torch_utils.quat_mul(self.j_pitch_quat, self.quat_pe)

    def update_measurements(self):
        super().update_measurements()
        alpha = 0.2
        # Force Application point for Torque estimation
        self.coms = self.asset.root_physx_view.get_coms().to(self.device)[0]
        self.quat_bucket_w_old[:] = torch_utils.quat_mul(self.j_pitch_quat, self.quat_pe)


        self.w_r_pf = torch_utils.quat_rotate(self.j_pitch_quat, self.p_r_pf)
        self.w_r_pcom = torch_utils.quat_rotate(self.j_pitch_quat, self.coms[-1][:3].expand(self.num_envs, -1))

        self.prev_bucket_pos_w_old[:] = self.prev_bucket_pos_w_old
        self.bucket_pos_map_old[:] = self.j_pitch_pos + self.w_r_pe
        self.bucket_pos_w_old[:] = self.bucket_pos_map - self.env.scene.env_origins

        COM = False
        #self.env.scene['contact_force']
        if COM:
            self.filtered_contact_tau_ext_force[:] = self.contact_tau_force[:]
            self.filtered_contact_tau_ext_moment[:] = self.contact_tau_moment[:]
        else:
            # Contact sensor
            self.contact_normal_forces        = self.env.scene.sensors["contact_forces"].data.contact_normal_forces[:, 0, :, :]
            self.contact_points               = self.env.scene.sensors["contact_forces"].data.contact_points[:, 0, :, :]
            self.contact_normal_directions    = self.env.scene.sensors["contact_forces"].data.contact_normal_directions[:, 0, :, :]

            # Filter out zero forces for all environments at once
            mask = (self.contact_normal_forces[..., 0] != 0.)

            # Apply the mask to keep only non-zero forces and directions
            contact_forces = self.contact_normal_forces * self.contact_normal_directions
            env_origins_repeated = self.env.scene.env_origins.unsqueeze(1).repeat(1, 200, 1)
            j_pitch_pos_repeated = self.j_pitch_pos.unsqueeze(1).repeat(1, 200, 1)
            
            # Calculate lever arms
            lever_arms = self.contact_points - env_origins_repeated - j_pitch_pos_repeated
            expanded_mask = mask.unsqueeze(-1)  # Shape becomes [4, 200, 1]
            expanded_mask = expanded_mask.expand_as(lever_arms)  # Shape becomes [4, 200, k]
            # Set level_arms[i, j, :] to 0 where mask[i, j] is False
            lever_arms = torch.where(expanded_mask, lever_arms, torch.zeros_like(lever_arms))

            # Compute torques at contact points
            contact_point_taus = torch.cross(lever_arms, contact_forces, dim=2)

            # Expand bucket_jac_lin_T_dof along dimension 1 to match contact_forces shape
            bucket_jac_lin_T_dof_expanded = self.bucket_jac_lin_T_dof.unsqueeze(1).expand(-1, 200, -1, -1)  # Shape: [4, 200, 4, 3]
            # Unsqueeze contact_forces to shape [4, 200, 3, 1] to match for matmul
            contact_forces_expanded = contact_forces.unsqueeze(-1)  # Shape: [4, 200, 3, 1]
            # Perform matrix multiplication
            contact_f_genco_tau = torch.matmul(bucket_jac_lin_T_dof_expanded, contact_forces_expanded)  # Shape: [4, 200, 4, 1]
            # Sum over the 200 dimension if desired
            self.contact_tau_ext_force = contact_f_genco_tau.squeeze(-1).sum(dim=1)  # Shape: [

            # Expand bucket_jac_lin_T_dof along dimension 1 to match contact_forces shape
            bucket_jac_rot_T_dof_expanded = self.bucket_jac_rot_T_dof.unsqueeze(1).expand(-1, 200, -1, -1)  # Shape: [4, 200, 4, 3]
            # Unsqueeze contact_forces to shape [4, 200, 3, 1] to match for matmul
            contact_point_taus_expanded = contact_point_taus.unsqueeze(-1)  # Shape: [4, 200, 3, 1]
            # Perform matrix multiplication
            contact_m_genco_tau = torch.matmul(bucket_jac_rot_T_dof_expanded, contact_point_taus_expanded)  # Shape: [4, 200, 4, 1]
            # Sum over the 200 dimension if desired
            self.contact_tau_ext_moment = contact_m_genco_tau.squeeze(-1).sum(dim=1)  # Shape: [
            
            #is_equal_force = torch.allclose(self.contact_tau_ext_force, contact_f_genco_tau.squeeze(-1).sum(dim=1), atol=1)
            #is_equal_moment = torch.allclose(self.contact_tau_ext_moment, contact_m_genco_tau.squeeze(-1).sum(dim=1), atol=1)
            #print('is_equal_force ', is_equal_force)
            #print('is_equal_moment ', is_equal_moment)
            # Apply low-pass filtering (EMA) to smooth the forces and moments
            alpha = 0.2
            #first_contact_mask = self.is_first_contact.float()  # Shape: [4, ...]

            '''self.filtered_contact_tau_ext_force = (
                alpha * self.contact_tau_ext_force[:] + (1 - alpha) * self.filtered_contact_tau_ext_force[:]
            )
            self.filtered_contact_tau_ext_moment = (
                alpha * self.contact_tau_ext_moment[:] + (1 - alpha) * self.filtered_contact_tau_ext_moment[:]
            )'''
            # Apply filtering for forces and moments, vectorized for all indices
            self.filtered_contact_tau_ext_force = torch.where(
                self.is_first_contact.unsqueeze(-1),  # Use mask for first contact
                self.contact_tau_ext_force,  # If it's the first contact, use the raw force
                alpha * self.contact_tau_ext_force + (1 - alpha) * self.filtered_contact_tau_ext_force  # Else apply the filter
            )

            self.filtered_contact_tau_ext_moment = torch.where(
                self.is_first_contact.unsqueeze(-1),  # Use mask for first contact
                self.contact_tau_ext_moment,  # If it's the first contact, use the raw moment
                alpha * self.contact_tau_ext_moment + (1 - alpha) * self.filtered_contact_tau_ext_moment  # Else apply the filter
            )

            ## Update is_first_contact status
            #self.is_first_contact = ~(self.is_first_contact | (mask.any(dim=1)))

            self.is_first_contact = torch.where(
                (self.is_first_contact & (mask.any(dim=1))),  # (T, T) case -> True and True results in False
                torch.tensor(False),  # Result is F (False)
                torch.where(
                    (self.is_first_contact & ~(mask.any(dim=1))),  # (T, F) case -> True and False results in True
                    torch.tensor(True),  # Result is T (True)
                    torch.tensor(False)   # (F, T) and (F, F) cases -> Result is False
                )
            )

    def reset_idx(self, env_ids):
        self.is_first_contact[env_ids] = True

class M545Measurements_b:
    def __init__(self, cfg, num_joints, num_envs, device):
        # Asset to derive all necessary measuremnts buffers
        self.num_envs = num_envs
        self.num_dofs = num_joints
        self.num_bodies = num_joints + 1
        self.device = device
        self.gravity = torch.tensor([0, 0, -9.81], device=self.device)
        self.gravity_expanded = self.gravity.view(1, 3)
        self.cfg = cfg
        # Manually put on GPU because read on cpu
        self.masses = torch.zeros(self.num_bodies, device=self.device)
        self.masses_expanded = self.masses.view(self.num_bodies, 1)
        # Inertias
        inertias = torch.zeros((self.num_envs, self.num_bodies, 9), device=self.device)
        self.inertias = torch.reshape(inertias[0], (self.num_bodies, 3, 3))

        # Fill all the necessary buffers with zeros
        self.init_measurements_buffers()

    def post_init(self, env):
        self.env = env
        self.asset = env.scene.articulations["robot"]
        # Manually put on GPU because read on cpu
        self.masses = self.asset.root_physx_view.get_masses()[0, :].to(
            self.env.device
        )  # m545:  torch.tensor([10490.0020,  1400.0000,  1222.4600,   526.4620,   993.0000],device=self.device)
        self.masses_expanded = self.masses.view(self.asset.num_bodies, 1)
        # Inertias
        inertias = self.asset.root_physx_view.get_inertias().to(self.device)
        self.inertias = torch.reshape(inertias[0], (self.asset.num_bodies, 3, 3))
        # Non linear therms
        self.coriolis_centrifugal_force = self.asset.root_physx_view.get_coriolis_and_centrifugal_forces()
        # Asset related measurements
        # DOF pos and vel
        self.joint_pos = self.asset.data.joint_pos
        self.joint_vel = self.asset.data.joint_vel
        # Position and orientation of the Roto_base body:
        self.j_pitch_pos = self.asset.data.body_pos_w[:, -1, :]
        self.j_pitch_quat = self.asset.data.body_quat_w[:, -1, :]

        # Linear and angular veloctiy of the Roto_base body
        self.j_pitch_vel = self.asset.data.body_lin_vel_w[:, -1, :]
        self.j_pitch_ang_vel = self.asset.data.body_ang_vel_w[:, -1, :]
        # Linear and angular velocity of the root of the asset in world frame
        self.root_lin_vel_w = self.asset.data.root_lin_vel_w
        self.root_ang_vel_w = self.asset.data.root_ang_vel_w
        self.root_quat_w = self.asset.data.root_quat_w
        self.root_pos_w = self.asset.data.root_pos_w
        # coms are in body frame
        self.coms = self.asset.root_physx_view.get_coms().to(self.device)[0]
        # Force Application point for Torque estimation
        self.w_r_pf = torch_utils.quat_rotate(self.j_pitch_quat, self.p_r_pf)
        self.w_r_pcom = torch_utils.quat_rotate(self.j_pitch_quat, self.coms[-1][:3].expand(self.num_envs, -1))

        self.net_contact_forces = self.env.scene.sensors["contact_forces"].data.net_forces_w
        self.bucket_contact_force = self.net_contact_forces[:, -1, :]
        self.contact_f_genco_tau = torch.matmul(
            self.bucket_jac_lin_T_dof[:], self.bucket_contact_force[:].unsqueeze(-1)
        )
        pos = torch.zeros_like(self.j_pitch_pos) - self.j_pitch_pos
        boulder_tau = torch.cross(pos, self.bucket_contact_force, dim=1)
        self.contact_tau_force = self.contact_f_genco_tau.squeeze(-1)[:, -self.num_dofs :]
        contact_m_genco_tau = torch.matmul(self.bucket_jac_rot_T_dof[:], boulder_tau.unsqueeze(-1))
        self.contact_tau_moment = contact_m_genco_tau.squeeze(-1)[:, -self.num_dofs :]

        self.contact_tau_ext_force = torch.zeros_like(self.contact_tau_force)
        self.contact_tau_ext_moment = torch.zeros_like(self.contact_tau_force)
        self.filtered_contact_tau_ext_force = torch.zeros_like(self.contact_tau_force)
        self.filtered_contact_tau_ext_moment = torch.zeros_like(self.contact_tau_force)

        self.is_first_contact = torch.ones(self.num_envs, device=self.device, dtype=torch.bool)


        # 
        max_contact_points_per_env = 200
        self.contact_normal_forces        = torch.zeros(self.num_envs, max_contact_points_per_env, 1, device=self.device)
        self.contact_points               = torch.zeros(self.num_envs, max_contact_points_per_env, 3, device=self.device)
        self.contact_normal_directions    = torch.zeros(self.num_envs, max_contact_points_per_env, 3, device=self.device)

        self.contact_tau_ext_force = torch.zeros_like(self.contact_tau_force)
        self.contact_tau_ext_moment = torch.zeros_like(self.contact_tau_force)
        self.filtered_contact_tau_ext_force = torch.zeros_like(self.contact_tau_force)
        self.filtered_contact_tau_ext_moment = torch.zeros_like(self.contact_tau_force)

    def init_measurements_buffers(self):
        # DOF pos and vel
        self.joint_pos = torch.zeros((self.num_envs, self.num_dofs), device=self.device)
        self.joint_vel = torch.zeros((self.num_envs, self.num_dofs), device=self.device)
        # Position and orientation of the Roto_base body:
        self.j_pitch_pos = torch.zeros((self.num_envs, 3), device=self.device)
        self.j_pitch_quat = torch.zeros((self.num_envs, 4), device=self.device)

        # Linear and angular veloctiy of the Roto_base body
        self.j_pitch_vel = torch.zeros((self.num_envs, 3), device=self.device)
        self.j_pitch_ang_vel = torch.zeros((self.num_envs, 3), device=self.device)
        # Jacobian
        self.jacobian = torch.zeros((self.num_envs, self.num_bodies, 6, self.num_dofs + 6), device=self.device)
        self.jac_lin = self.jacobian[:, :, 0:3, :]
        self.jac_lin_T = self.jac_lin.transpose(-2, -1)
        self.jac_rot = self.jacobian[:, :, 3:6, :]
        self.jac_rot_T = self.jac_rot.transpose(-2, -1)
        # only bucket jacobians
        self.bucket_jac_lin_T_dof = self.jacobian[:, -1, 0:3, -self.num_dofs :].transpose(-2, -1)
        self.bucket_jac_rot_T_dof = self.jacobian[:, -1, 3:6, -self.num_dofs :].transpose(-2, -1)

        # contact forces, not as in Gym, TODO: check tensor size
        self.bucket_contact_force = torch.zeros((self.num_envs, 3), device=self.device)

        # Linear and angular velocity of the root of the asset in world frame
        self.root_lin_vel_w = torch.zeros((self.num_envs, 3), device=self.device)
        self.root_ang_vel_w = torch.zeros((self.num_envs, 3), device=self.device)
        self.root_quat_w = torch.zeros((self.num_envs, 4), device=self.device)
        self.root_pos_w = torch.zeros((self.num_envs, 3), device=self.device)

        # constants
        # const offset vector eef to pitch in eef
        self.e_r_ep = torch.tensor([-1.3888, 0.0, 0.291139], device=self.device).expand(self.num_envs, -1)
        # const offset vector pitch to eef in pitch
        self.p_r_pe = torch.tensor([1.418, 0.0, 0.053], device=self.device).expand(self.num_envs, -1)
        # cabin: constant cabin pos in base frame (base and cabin frame have same orientation as long as turn joint is 0!!)
        self.b_r_bc = torch.tensor([0.0, 0.0, 0.516], device=self.device).expand(self.num_envs, -1)
        # constant rot pitch to eef
        self.quat_pe = torch.tensor([0.992567, 0.0, -0.121698, 0.0], device=self.device).expand(
            self.num_envs, -1
        )  # Quat changed from gym to sim

        # derived - reset cache sets these
        self.mm = torch.zeros(self.num_envs, self.num_dofs, self.num_dofs, device=self.device)
        self.gravity_tau = torch.zeros(self.num_envs, self.num_dofs, device=self.device)

        # Unit z vector
        self.unit_vec_z = torch.tensor([0.0, 0.0, 1.0], device=self.device)
        self.unit_vec_z_expanded = self.unit_vec_z.expand(self.num_envs, -1)

        self.w_r_pe = torch.zeros([self.num_envs, 3], device=self.device)

        # Bucket Position in World Frame
        self.prev_bucket_pos_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_pos_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_pos_map = torch.zeros([self.num_envs, 3], device=self.device)

        # Bucket Velocity in World Frame
        self.bucket_vel_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.quat_bucket_w = torch.zeros([self.num_envs, 4], device=self.device)
        self.bp_unit_vector_w = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_com_pos_w = torch.zeros([self.num_envs, 3], device=self.device)

        # derived - reset cache does not set these
        self.base_pitch_w = torch.zeros([self.num_envs], device=self.device)
        self.bucket_aoa = torch.zeros(self.num_envs, device=self.device)
        self.prev_bucket_ang_gac = torch.zeros([self.num_envs], device=self.device)
        self.bucket_ang_gac = torch.zeros([self.num_envs], device=self.device)
        self.bucket_vel_norm = torch.zeros([self.num_envs], device=self.device)

        self.base_z_w = torch.zeros([self.num_envs, 3], device=self.device)

        # Additional measurements constant about the bucket specific for single rock excavation
        # Corner positions in bucket frame
        self.bucket_fc1_pos_b = torch.tensor([0.0, 0.5 * self.cfg.bucket_width, 0.0], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_fc2_pos_b = torch.tensor([0.0, -0.5 * self.cfg.bucket_width, 0.0], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_fc3_pos_b = torch.tensor([-0.5594, 0.5 * self.cfg.bucket_width, 0.4909], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_fc4_pos_b = torch.tensor(
            [-0.5594, -0.5 * self.cfg.bucket_width, 0.4909], device=self.device
        ).expand(self.num_envs, -1)
        self.bucket_bc1_pos_b = torch.tensor([0.0, 0.5 * self.cfg.bucket_width, 0.62], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_bc2_pos_b = torch.tensor([0.0, -0.5 * self.cfg.bucket_width, 0.62], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_bc3_pos_b = torch.tensor([-0.5069, 0.5 * self.cfg.bucket_width, 0.8309], device=self.device).expand(
            self.num_envs, -1
        )
        self.bucket_bc4_pos_b = torch.tensor(
            [-0.5069, -0.5 * self.cfg.bucket_width, 0.8309], device=self.device
        ).expand(self.num_envs, -1)
        #  Shovel boundaries
        self.bucket_max_x_b = torch.tensor([0.55], device=self.device).expand(self.num_envs, -1)
        self.bucket_min_x_b = torch.tensor([0], device=self.device).expand(self.num_envs, -1)
        self.bucket_max_y_b = torch.tensor([0.5 * self.cfg.bucket_width], device=self.device).expand(self.num_envs, -1)
        self.bucket_min_y_b = torch.tensor([-0.5 * self.cfg.bucket_width], device=self.device).expand(self.num_envs, -1)
        self.bucket_max_z_b = torch.tensor([0.78], device=self.device).expand(self.num_envs, -1)
        self.bucket_min_z_b = torch.tensor([0.0], device=self.device).expand(self.num_envs, -1)
        # point inside bucket to calculate distance to boulder
        self.boulder_ref_b = torch.tensor([-0.4, 0.0, 0.15], device=self.device).expand(self.num_envs, -1)
        # const offset vector eef to pitch in eef
        self.e_r_ep = torch.tensor([-1.3888, 0.0, 0.291139], device=self.device).expand(self.num_envs, -1)
        # const offset vector pitch to eef in pitch
        self.p_r_pe = torch.tensor([1.418, 0.0, 0.053], device=self.device).expand(self.num_envs, -1)
        # cabin: constant cabin pos in base frame (base and cabin frame have same orientation as long as turn joint is 0!!)
        self.b_r_bc = torch.tensor([0.0, 0.0, 0.516], device=self.device).expand(self.num_envs, -1)
        # constant rot pitch to eef
        self.quat_pe = torch.tensor([0.0, -0.121698, 0.0, 0.992567], device=self.device).expand(self.num_envs, -1)
        # constant force application point pitch to force application point in pitch
        self.p_r_pf = torch.tensor([1.268, 0.0, 0.3], device=self.device).expand(self.num_envs, -1)
        # BUcket boundaries

        # Jacobian
        # Gravity forces
        self.gravity_forces = self.masses_expanded * self.gravity_expanded
        # In joint space
        gravity_genco_tau = torch.sum(
            torch.matmul(
                self.jac_lin_T[:, -self.num_dofs :, -self.num_dofs :, :],
                self.gravity_forces[-self.num_dofs :, :].unsqueeze(-1),
            ),
            dim=1,
        )
        self.gravity_tau[:] = gravity_genco_tau.squeeze()
        # Mass Matrix
        self.mm[:] = torch.sum(
            self.jac_lin_T.matmul(self.masses.view(-1, 1, 1) * self.jac_lin)
            + self.jac_rot_T.matmul(self.inertias.matmul(self.jac_rot)),
            dim=1,
        )[:, -self.num_dofs :, -self.num_dofs :]

        # Base-frame vectors
        self.bucket_pos_base = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_vel_base = torch.zeros([self.num_envs, 3], device=self.device)
        self.bucket_ang_vel_base = torch.zeros([self.num_envs, 3], device=self.device)

    def update_measurements(self):

        # DOF pos and vel
        self.joint_pos = self.asset.data.joint_pos
        self.joint_vel = self.asset.data.joint_vel
        # Position and orientation of the Roto_base body:
        self.j_pitch_pos = self.asset.data.body_pos_w[:, -1, :]
        self.j_pitch_quat = self.asset.data.body_quat_w[:, -1, :]
        # Linear and angular veloctiy of the Roto_base body
        self.j_pitch_vel = self.asset.data.body_lin_vel_w[:, -1, :]
        self.j_pitch_ang_vel = self.asset.data.body_ang_vel_w[:, -1, :]
        # Linear and angular velocity of the root of the asset in world frame
        self.root_lin_vel_w = self.asset.data.root_lin_vel_w
        self.root_ang_vel_w = self.asset.data.root_ang_vel_w
        self.root_quat_w = self.asset.data.root_quat_w
        self.root_pos_w = self.asset.data.root_pos_w

        # Jacobian
        self.jacobian = self.asset.root_physx_view.get_jacobians()
        self.jac_lin = self.jacobian[:, :, 0:3, :]
        self.jac_lin_T = self.jac_lin.transpose(-2, -1)
        self.jac_rot = self.jacobian[:, :, 3:6, :]
        self.jac_rot_T = self.jac_rot.transpose(-2, -1)
        # only bucket jacobians
        self.bucket_jac_lin_T_dof = self.jacobian[:, -1, 0:3, -self.num_dofs :].transpose(-2, -1)
        self.bucket_jac_rot_T_dof = self.jacobian[:, -1, 3:6, -self.num_dofs :].transpose(-2, -1)

        # ---------
        # w_r_pe
        self.w_r_pe = torch_utils.quat_rotate(self.j_pitch_quat, self.p_r_pe)
        # bucket position and velocity in world frame
        self.prev_bucket_pos_w[:] = self.bucket_pos_w
        self.bucket_pos_map[:] = self.j_pitch_pos + self.w_r_pe
        self.bucket_pos_w[:] = self.bucket_pos_map - self.env.scene.env_origins
        self.j_pitch_pos -= self.env.scene.env_origins
        self.bucket_vel_w[:] = self.j_pitch_vel + torch.cross(self.j_pitch_ang_vel, self.w_r_pe)
        self.quat_bucket_w[:] = torch_utils.quat_mul(self.j_pitch_quat, self.quat_pe)
        self.bp_unit_vector_w[:] = torch_utils.quat_rotate(self.quat_bucket_w, self.unit_vec_z_expanded)

        # How it s done in sim
        # Jacobian
        self.jacobian_ee_lin = self.jacobian[:, :, :3, 6:]
        # Gravity forces
        self.gravity_forces = self.masses_expanded * self.gravity_expanded
        # Joint space
        gravity_genco_tau = torch.sum(
            torch.matmul(
                self.jac_lin_T[:, -self.num_dofs :, -self.num_dofs :, :],
                self.gravity_forces[-self.num_dofs :, :].unsqueeze(-1),
            ),
            dim=1,
        )
        self.gravity_tau[:] = gravity_genco_tau.squeeze()

        # Mass Matrix
        self.mm[:] = torch.sum(
            self.jac_lin_T.matmul(self.masses.view(-1, 1, 1) * self.jac_lin)
            + self.jac_rot_T.matmul(self.inertias.matmul(self.jac_rot)),
            dim=1,
        )[:, -self.num_dofs :, -self.num_dofs :]
        # Non linear therms
        self.coriolis_centrifugal_force[:] = self.asset.root_physx_view.get_coriolis_and_centrifugal_forces()

        # Force Application point for Torque estimation
        self.w_r_pf = torch_utils.quat_rotate(self.j_pitch_quat, self.p_r_pf)
        self.w_r_pcom = torch_utils.quat_rotate(self.j_pitch_quat, self.coms[-1][:3].expand(self.num_envs, -1))

        # contact w boulder force
        self.net_contact_forces = self.env.scene.sensors["contact_forces"].data.net_forces_w
        self.bucket_contact_force = self.net_contact_forces[:, -1, :]
        # Compute torque for force compensation at COM
        self.contact_f_genco_tau = torch.matmul(
            self.bucket_jac_lin_T_dof[:], self.bucket_contact_force[:].unsqueeze(-1)
        )
        pos = self.env.boulder_measurements.root_pos_w - self.j_pitch_pos
        boulder_tau = torch.cross(pos, self.bucket_contact_force, dim=1)
        self.contact_tau_force[:] = self.contact_f_genco_tau.squeeze(-1)[:, -self.num_dofs :]
        contact_m_genco_tau = torch.matmul(self.bucket_jac_rot_T_dof[:], boulder_tau.unsqueeze(-1))
        self.contact_tau_moment[:] = contact_m_genco_tau.squeeze(-1)[:, -self.num_dofs :]
        # Convert world to base frame
        self.bucket_pos_base[:] = torch_utils.quat_rotate_inverse(
            self.root_quat_w,
            (self.bucket_pos_map - self.root_pos_w)
        )
        self.bucket_vel_base[:] = torch_utils.quat_rotate_inverse(
            self.root_quat_w,
            (self.bucket_vel_w - self.root_lin_vel_w)
        )
        #self.bucket_ang_vel_base[:] = torch_utils.quat_rotate_inverse(
        #    self.root_quat_w,
        #    (self.bucket_ang_vel - self.root_ang_vel_w)
        #)
        COM = False
        #self.env.scene['contact_force']
        if COM:
            self.filtered_contact_tau_ext_force[:] = self.contact_tau_force[:]
            self.filtered_contact_tau_ext_moment[:] = self.contact_tau_moment[:]
        else:
            # Contact sensor
            self.contact_normal_forces        = self.env.scene.sensors["contact_forces"].data.contact_normal_forces[:, 0, :, :]
            self.contact_points               = self.env.scene.sensors["contact_forces"].data.contact_points[:, 0, :, :]
            self.contact_normal_directions    = self.env.scene.sensors["contact_forces"].data.contact_normal_directions[:, 0, :, :]
        
            '''for i in range(self.num_envs):
                # Filter 0 force
                mask = (self.contact_normal_forces[i,:,0] != 0.)
                number_relevant_contact = mask.sum()
                if (number_relevant_contact > 0):
                    
                    # Force at contact_points
                    contact_force = self.contact_normal_forces[i][mask]*self.contact_normal_directions[i][mask]
                    # Calculate the vector from joint position to contact point
                    lever_arm = self.contact_points[i,:][mask]-self.env.scene.env_origins[i,:].repeat(mask.sum(),1 )- self.j_pitch_pos[i].repeat(mask.sum(),1)
                    # Torque caused by force at the contact point
                    contact_point_tau = torch.cross(lever_arm, contact_force, dim=1)
                    # Compute the force torque compensation using Jacobians
                    contact_f_genco_tau = torch.matmul(self.bucket_jac_lin_T_dof[i,:], contact_force.unsqueeze(-1))
                    contact_m_genco_tau = torch.matmul(self.bucket_jac_rot_T_dof[i,:], contact_point_tau.unsqueeze(-1))
                    # Update contact torque forces and moments with total compensation
                    self.contact_tau_ext_force[i,:] = contact_f_genco_tau.sum(0).reshape(-1)
                    self.contact_tau_ext_moment[i,:] = contact_m_genco_tau.sum(0).reshape(-1)
                
                    # Apply low-pass filtering (EMA) to smooth the forces and moments
                    alpha = 0.2
                    if self.is_first_contact[i]:
                        self.filtered_contact_tau_ext_force[i, :] = self.contact_tau_ext_force[i,:]
                        self.filtered_contact_tau_ext_moment[i, :] = self.contact_tau_ext_moment[i,:]
                        self.is_first_contact[i] = False
                    else:
                        self.filtered_contact_tau_ext_force[i, :] = (
                            alpha * self.contact_tau_ext_force[i, :] + (1 - alpha) * self.filtered_contact_tau_ext_force[i, :]
                        )
                        self.filtered_contact_tau_ext_moment[i, :] = (
                            alpha * self.contact_tau_ext_moment[i, :] + (1 - alpha) * self.filtered_contact_tau_ext_moment[i, :]
                        )'''
            # Filter out zero forces for all environments at once
            mask = (self.contact_normal_forces[..., 0] != 0.)

            # Apply the mask to keep only non-zero forces and directions
            contact_forces = self.contact_normal_forces * self.contact_normal_directions
            env_origins_repeated = self.env.scene.env_origins.unsqueeze(1).repeat(1, 200, 1)
            j_pitch_pos_repeated = self.j_pitch_pos.unsqueeze(1).repeat(1, 200, 1)
            
            # Calculate lever arms
            lever_arms = self.contact_points - env_origins_repeated - j_pitch_pos_repeated
            expanded_mask = mask.unsqueeze(-1)  # Shape becomes [4, 200, 1]
            expanded_mask = expanded_mask.expand_as(lever_arms)  # Shape becomes [4, 200, k]
            # Set level_arms[i, j, :] to 0 where mask[i, j] is False
            lever_arms = torch.where(expanded_mask, lever_arms, torch.zeros_like(lever_arms))

            # Compute torques at contact points
            contact_point_taus = torch.cross(lever_arms, contact_forces, dim=2)

            # Expand bucket_jac_lin_T_dof along dimension 1 to match contact_forces shape
            bucket_jac_lin_T_dof_expanded = self.bucket_jac_lin_T_dof.unsqueeze(1).expand(-1, 200, -1, -1)  # Shape: [4, 200, 4, 3]
            # Unsqueeze contact_forces to shape [4, 200, 3, 1] to match for matmul
            contact_forces_expanded = contact_forces.unsqueeze(-1)  # Shape: [4, 200, 3, 1]
            # Perform matrix multiplication
            contact_f_genco_tau = torch.matmul(bucket_jac_lin_T_dof_expanded, contact_forces_expanded)  # Shape: [4, 200, 4, 1]
            # Sum over the 200 dimension if desired
            self.contact_tau_ext_force = contact_f_genco_tau.squeeze(-1).sum(dim=1)  # Shape: [

            # Expand bucket_jac_lin_T_dof along dimension 1 to match contact_forces shape
            bucket_jac_rot_T_dof_expanded = self.bucket_jac_rot_T_dof.unsqueeze(1).expand(-1, 200, -1, -1)  # Shape: [4, 200, 4, 3]
            # Unsqueeze contact_forces to shape [4, 200, 3, 1] to match for matmul
            contact_point_taus_expanded = contact_point_taus.unsqueeze(-1)  # Shape: [4, 200, 3, 1]
            # Perform matrix multiplication
            contact_m_genco_tau = torch.matmul(bucket_jac_rot_T_dof_expanded, contact_point_taus_expanded)  # Shape: [4, 200, 4, 1]
            # Sum over the 200 dimension if desired
            self.contact_tau_ext_moment = contact_m_genco_tau.squeeze(-1).sum(dim=1)  # Shape: [
            
            #is_equal_force = torch.allclose(self.contact_tau_ext_force, contact_f_genco_tau.squeeze(-1).sum(dim=1), atol=1)
            #is_equal_moment = torch.allclose(self.contact_tau_ext_moment, contact_m_genco_tau.squeeze(-1).sum(dim=1), atol=1)
            #print('is_equal_force ', is_equal_force)
            #print('is_equal_moment ', is_equal_moment)
            # Apply low-pass filtering (EMA) to smooth the forces and moments
            alpha = 0.2
            #first_contact_mask = self.is_first_contact.float()  # Shape: [4, ...]

            '''self.filtered_contact_tau_ext_force = (
                alpha * self.contact_tau_ext_force[:] + (1 - alpha) * self.filtered_contact_tau_ext_force[:]
            )
            self.filtered_contact_tau_ext_moment = (
                alpha * self.contact_tau_ext_moment[:] + (1 - alpha) * self.filtered_contact_tau_ext_moment[:]
            )'''
            # Apply filtering for forces and moments, vectorized for all indices
            self.filtered_contact_tau_ext_force = torch.where(
                self.is_first_contact.unsqueeze(-1),  # Use mask for first contact
                self.contact_tau_ext_force,  # If it's the first contact, use the raw force
                alpha * self.contact_tau_ext_force + (1 - alpha) * self.filtered_contact_tau_ext_force  # Else apply the filter
            )

            self.filtered_contact_tau_ext_moment = torch.where(
                self.is_first_contact.unsqueeze(-1),  # Use mask for first contact
                self.contact_tau_ext_moment,  # If it's the first contact, use the raw moment
                alpha * self.contact_tau_ext_moment + (1 - alpha) * self.filtered_contact_tau_ext_moment  # Else apply the filter
            )

            ## Update is_first_contact status
            #self.is_first_contact = ~(self.is_first_contact | (mask.any(dim=1)))

            self.is_first_contact = torch.where(
                (self.is_first_contact & (mask.any(dim=1))),  # (T, T) case -> True and True results in False
                torch.tensor(False),  # Result is F (False)
                torch.where(
                    (self.is_first_contact & ~(mask.any(dim=1))),  # (T, F) case -> True and False results in True
                    torch.tensor(True),  # Result is T (True)
                    torch.tensor(False)   # (F, T) and (F, F) cases -> Result is False
                )
            )



    def reset_idx(self, env_ids):
        self.is_first_contact[env_ids] = True
    

    
    def update_derived_measurements(self, env_ids, dim0):
        self.base_z_w = torch_utils.quat_rotate(
            self.root_quat_w[env_ids],
            self.unit_vec_z.expand(dim0, -1),
        )  # Noneed

        self.base_pitch_w[env_ids] = torch.atan2(self.base_z_w[:, 0], self.base_z_w[:, 2])

        vel_ang_w = torch.atan2(self.bucket_vel_w[env_ids, 2], self.bucket_vel_w[env_ids, 0])
        # in motion direction, bp unit vec points from tip backwards, -> invert sign here
        bp_ang_w = torch.atan2(-self.bp_unit_vector_w[env_ids, 2], -self.bp_unit_vector_w[env_ids, 0])
        aoa = bp_ang_w - vel_ang_w
        # wrap between +/- pi
        self.bucket_aoa[env_ids] = torch.where(
            torch.abs(aoa) > np.pi,
            aoa - torch.copysign(2.0 * np.pi * torch.ones(dim0, device=self.device), aoa),
            aoa,
        )
        self.prev_bucket_ang_gac[env_ids] = torch.atan2(
            self.bp_unit_vector_w[env_ids, 2], self.bp_unit_vector_w[env_ids, 0]
        )
        self.bucket_ang_gac[env_ids] = torch.atan2(self.bp_unit_vector_w[env_ids, 2], self.bp_unit_vector_w[env_ids, 0])
        # problem with slicing with two tensors that are not broadcastale, easier to
        # y vel is 0 anyways
        self.bucket_vel_norm[env_ids] = torch.linalg.norm(self.bucket_vel_w[env_ids], dim=-1)


