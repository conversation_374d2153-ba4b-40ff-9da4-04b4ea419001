import os

from isaacsim import SimulationApp

simulation_app = SimulationApp({"headless": False})  # You can also run as headless

import omni.usd
from pxr import Gf, Sdf, UsdGeom, UsdPhysics

"""
Standalone Isaac Sim Script which adds mass propreties to rigid objects in Isaac Sim
"""

# Path to the folder containing the USD files
usd_folder_path = "/home/<USER>/Coding/IsaacLab/source/extensions/isaaclab_assets/data/Robots/Gravis/boulders/rocks_rigid_objects"

print(os.listdir(usd_folder_path))

# Iterate over all USD files in the directory
for file_name in os.listdir(usd_folder_path):
    if file_name.endswith(".usd"):
        print("Processing file name", file_name)
        usd_path = os.path.join(usd_folder_path, file_name)
        print(f"Processing {usd_path}...")

        # Open the USD file
        omni.usd.get_context().open_stage(usd_path)
        stage = omni.usd.get_context().get_stage()

        # Get all prims under "/World"
        world_prim = stage.GetPrimAtPath("/World")
        if not world_prim.IsValid():
            print(f"No /World prim found in {file_name}")
            continue

        # Recursively find all child prims under /World
        def process_prims(prim):
            if prim.GetTypeName() == "Xform":  # Check if the prim can have mass and physics properties
                # Apply physics mass API if not present
                if not prim.HasAPI(UsdPhysics.MassAPI):
                    UsdPhysics.MassAPI.Apply(prim)

                # Set mass for the prim
                mass_api = UsdPhysics.MassAPI(prim)
                mass_attr = mass_api.GetMassAttr()
                # mass_attr.Set(5.0)  # Set the mass to 5.0

                # Set density for the prim using PhysicsMaterialAPI
                if not prim.HasAPI(UsdPhysics.MaterialAPI):
                    UsdPhysics.MaterialAPI.Apply(prim)

                material_api = UsdPhysics.MaterialAPI(prim)
                density_attr = material_api.GetDensityAttr()
                density_attr.Set(2500.0)  # Set the density to 2500

                print(f"Mass and density updated for prim {prim.GetPath()}")

            # Recursively process child prims
            for child_prim in prim.GetChildren():
                process_prims(child_prim)

        # Process all prims under /World
        process_prims(world_prim)

        # Save the USD file after modifications
        stage.GetRootLayer().Save()
        print(f"Finished processing {file_name}")

simulation_app.close()  # Close Isaac Sim
