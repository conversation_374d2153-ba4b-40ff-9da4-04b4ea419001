# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
M545 Excavation environment
"""

import gymnasium as gym

from . import agents
from .env_cfg import m545_single_boulder_excavation_cfg, m545_single_boulder_excavation_cfg_raycast, m545_single_boulder_excavation_cfg_raycast_w_shovel, m545_single_boulder_excavation_cfg_raycast_w_shovel_full_cloud

# Single Boulder
gym.register(
    id="Isaac-m545-single-boulder",
    entry_point="moleworks_ext.tasks.single_boulder_excavation.excavation_env_boulder:ExcavationEnvBoulder",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": m545_single_boulder_excavation_cfg.M545EnvCfg,
        "rsl_rl_cfg_entry_point": agents.rsl_rl_cfg_boulder.M545BoulderPPORunnerCfg,
    },
)


from moleworks_ext.tasks.single_boulder_excavation.env_cfg import deploy_boulder_cfg
# Single Boulder
gym.register(
    id="Isaac-m545-single-boulder-deploy",
    entry_point="moleworks_ext.tasks.single_boulder_excavation.excavation_env_boulder:ExcavationEnvBoulder",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": deploy_boulder_cfg.M545EnvCfg,
        "rsl_rl_cfg_entry_point": agents.rsl_rl_cfg_boulder.M545BoulderPPORunnerCfg,
    },
)

gym.register(
    id="Isaac-m545-single-boulder-raycast",
    entry_point="moleworks_ext.tasks.single_boulder_excavation.excavation_env_boulder:ExcavationEnvBoulder",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": m545_single_boulder_excavation_cfg_raycast.M545EnvCfg,
        "rsl_rl_cfg_entry_point": agents.rsl_rl_cfg_boulder.M545BoulderPPORunnerCfg,
    },
)

gym.register(
    id="Isaac-m545-single-boulder-raycast-shovel",
    entry_point="moleworks_ext.tasks.single_boulder_excavation.excavation_env_boulder:ExcavationEnvBoulder",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": m545_single_boulder_excavation_cfg_raycast_w_shovel.M545EnvCfg,
        "rsl_rl_cfg_entry_point": agents.rsl_rl_cfg_boulder.M545BoulderPPORunnerCfg,
    },
)

gym.register(
    id="Isaac-m545-single-boulder-raycast-shovel-full-cloud",
    entry_point="moleworks_ext.tasks.single_boulder_excavation.excavation_env_boulder:ExcavationEnvBoulder",
    disable_env_checker=True,
    kwargs={
        "env_cfg_entry_point": m545_single_boulder_excavation_cfg_raycast_w_shovel_full_cloud.M545EnvCfg,
        "rsl_rl_cfg_entry_point": agents.rsl_rl_cfg_boulder.M545BoulderPPORunnerCfg,
    },
)

