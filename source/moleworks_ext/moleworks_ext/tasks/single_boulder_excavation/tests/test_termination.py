# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")

parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-single-boulder-raycast-shovel"
args_cli.num_envs = 1000
args_cli.headless = True

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback

import carb
from rsl_rl.runners import OnPolicyRunner
import moleworks_ext
import isaaclab_tasks  # noqa: F401
from isaaclab_tasks.utils import parse_env_cfg


DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""

    env_cfg = parse_env_cfg(args_cli.task, num_envs=args_cli.num_envs)
    #env_cfg.sim.dt = 0.04
    # env_cfg.reset.only_above_soil = True

    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")
    # reset environment
    # env.unwrapped.curriculum_excavation.set_level_and_update(2000)
    env.reset()

    # Num steps and num_reset
    num_resets = 100
    num_steps = 1
    total_resets = (num_steps) * (num_resets) * env.unwrapped.num_envs
    done_count = torch.zeros(1, device=env.unwrapped.device)
    actions = torch.zeros(env.unwrapped.num_envs, 4)
    # Loggin storage
    num_terminations = len(env.unwrapped.termination_manager._term_names)
    termination_types_logs = torch.zeros(num_terminations, device=env.unwrapped.device)
    # run everything in inference mode
    count = 0

    robot = env.unwrapped.scene["robot"]
    with torch.inference_mode():
        for i in range(num_resets):
            # Reset the env
            env.reset()
            print("Reset: ", i)
            # compute zero actions
            for j in range(num_steps):
                print("Step: ", j)
                # apply actions
                obs, rewards, dones, timeouts, infos = env.step(actions)
                done_count += torch.count_nonzero(dones)
                i = 0
                for term_name in env.unwrapped.termination_manager._term_names:
                    value = (env.unwrapped.termination_manager.get_term(term_name)).sum()
                    termination_types_logs[i] += value
                    i += 1

    # Print success rate
    print(
        "done/resets: {} / {} ({} %)".format(
            int(done_count), int(total_resets), 100.0 * float(done_count) / (total_resets)
        )
    )
    i = 0
    for term_name in env.unwrapped.termination_manager._term_names:
        print(term_name, " :", 100 * termination_types_logs[i] / total_resets, " %")
        i += 1

    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
