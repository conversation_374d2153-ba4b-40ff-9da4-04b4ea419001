# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script demonsrates how to deal with perception in Isaac Sim

"""

"""Launch Isaac Sim Simulator first."""

import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Tutorial on adding sensors on a robot.")
parser.add_argument("--num_envs", type=int, default=50, help="Number of environments to spawn.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import math
import torch

import isaacsim.core.utils.torch as torch_utils
import isaacsim.util.debug_draw._debug_draw as omni_debug_draw

import isaaclab.sim as sim_utils
import isaaclab.utils.math as math_utils
from isaaclab.assets import ArticulationCfg, AssetBaseCfg, RigidObject, RigidObjectCfg
from isaaclab.markers import VisualizationMarkers
from isaaclab.markers.config import FRAME_MARKER_CFG
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sensors import (
    CameraCfg,
    ContactSensorCfg,
    FrameTransformer,
    FrameTransformerCfg,
    OffsetCfg,
    RayCasterCfg,
    patterns,
)
from isaaclab.utils import configclass
from isaaclab.utils.math import (
    combine_frame_transforms,
    convert_quat,
    is_identity_pose,
    subtract_frame_transforms,
)

from moleworks_ext.common.sim.spawners.multi_asset import MultiAssetCfg
from moleworks_ext.tasks.single_boulder_excavation.utils.multi_objects_spawning.multi_object_spawning_functions import (
    get_randomized_rocks_assets,
    get_rocks_assets,
)

##
# Pre-defined configs
##

X_LIMIT_NEG = 3
X_LIMIT_POS = 7
Y_LIMIT_NEG = -1
Y_LIMIT_POS = 1
NUM_OBJECTS_PER_ENV = 1
num_random_asset_per_asset_type = 10
RANDOMIZE = True

OBJECT_LIST = [
    RigidObjectCfg(
        prim_path=f"/World/envs/env_.*/RandomObject{i}",
        spawn=MultiAssetCfg(
            assets_cfg=get_randomized_rocks_assets(num_random_asset_per_asset_type) if RANDOMIZE else get_rocks_assets()
        ),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(0, 0, 1 + i)),
    )
    for i in range(NUM_OBJECTS_PER_ENV)
]
from moleworks_ext.tasks.single_boulder_excavation.utils.multi_objects_spawning.multi_object_spawning_functions import (
    get_randomized_rocks_assets,
    get_rocks_assets,
)


@configclass
class BoulderSceneCfg(InteractiveSceneCfg):
    """Design the scene with sensors on the robot."""

    # ground plane
    ground = AssetBaseCfg(prim_path="/World/defaultGroundPlane", spawn=sim_utils.GroundPlaneCfg())

    # lights
    dome_light = AssetBaseCfg(
        prim_path="/World/Light", spawn=sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75))
    )

    def __post_init__(self):
        # Dynamically create object_0, object_1, etc.
        for i, obj_cfg in enumerate(OBJECT_LIST):
            setattr(self, f"object_{i}", obj_cfg)
        # This allows randomization amomng the USD through envs
        self.replicate_physics = False


def compute_ellipsoid_height(center, radii, evecs):
    # The unit vector in the Z direction
    z_unit_vector = np.array([0, 0, 1])

    # The semi-principal axis corresponding to the largest radius
    max_radius_index = 0  # np.argmax(radii)  # Index of the longest axis
    longest_axis = evecs[:, max_radius_index]

    # Compute the projection of the longest axis on the Z axis
    z_projection = np.dot(longest_axis, z_unit_vector)

    # Height of the ellipsoid is twice the projection scaled by the corresponding radius
    height = 2 * abs(z_projection) * radii[max_radius_index]

    return height


def run_simulator(sim: sim_utils.SimulationContext, scene: InteractiveScene):
    """Run the simulator."""
    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    sim_time = 0.0
    count = 0

    rock = scene["object_0"]
    # Pattern: env0 obj 0,env1 obj 0, ..,envN obj 0,env0 obj 1,env1 obj 1, envN obj 1,...
    # If pattern is somehow lost we can store the env and obj number from prim path in list from spawn_multi_object_randomly_sdf_w_name
    usd_files = rock.cfg.spawn.usd_list
    scales = rock.cfg.spawn.scale_list
    axes = torch.zeros((scene.num_envs, 3, 3), device=scene.device, dtype=torch.float32)
    centers = torch.zeros((scene.num_envs, 3), device=scene.device, dtype=torch.float32)
    radiis = torch.zeros((scene.num_envs, 3), device=scene.device, dtype=torch.float32)
    heights = torch.zeros((scene.num_envs), device=scene.device, dtype=torch.float32)
    # Loop through files (Attention this is for one Object per env, otherwise see pattern)
    for i in range(len(usd_files)):
        usd_file = usd_files[i] + ".usd"
        scale = scales[i]
        center, radii, evecs, scaled_axes = find_ellipsoide_data(usd_file)
        scale = scales[i]
        axes[i, :] = torch.tensor(scaled_axes, device=scene.device, dtype=torch.float32) * scale
        centers[i, :] = torch.tensor(center, device=scene.device, dtype=torch.float32)
        radiis[i, :] = torch.tensor(radii, device=scene.device, dtype=torch.float32)
        #
        # minimum volume, we sould know how o find the height there
        center, radii, evecs, scaled_axes = find_ellipsoide_data(
            usd_file,
            csv_file="/home/<USER>/Coding/IsaacLab/source/extensions/isaaclab_tasks/omni/isaac/lab_tasks/manager_based/m545/utils/single_boulder_excavation/ellipsoid_data_min_volume.csv",
        )
        radiis[i, :] = torch.tensor(radii, device=scene.device, dtype=torch.float32)
        heights[i] = compute_ellipsoid_height(center, radii, evecs) * scale
    # Additional buffers
    ellipsoid_axes_w = torch.zeros(scene.num_envs, 9, device=scene.device)

    draw_interface = omni_debug_draw.acquire_debug_draw_interface()

    # retrieve ellipsoide data from it
    # prim_path =
    # Simulate physics
    draw_interface.clear_lines()
    while simulation_app.is_running():

        if count % 100 == 0:
            root_state = rock.data.default_root_state.clone()
            for i in range(scene.num_envs):
                root_state[i, 2] = radiis[i, 2] * scales[i]  # +centers[i,2]
                root_state[i, :3] += scene.env_origins[i, :]
                rock.write_root_state_to_sim(
                    root_state[i].reshape(1, -1), env_ids=torch.tensor([i], device=scene.device)
                )

        # -- write data to sim
        scene.write_data_to_sim()
        # perform step
        sim.step()

        # update sim-time
        sim_time += sim_dt
        count += 1
        # update buffers
        scene.update(sim_dt)
        ellipsoid_axes_w = update_measurements(rock, axes, scene.num_envs)

        # Draw lines
        draw_interface.clear_lines()
        for i in range(scene.num_envs):
            # ellipsoid axes, 3 lines
            vertices_source = torch.zeros((3, 3), device=scene.device)
            # We start at the root èpsition of the boulder
            vertices_source[:, :] = centers[i, :] + rock.data.root_pos_w[i, :]
            # Target for those 3 points
            vertices_target = torch.zeros((3, 3), device=scene.device)
            vertices_target[0, :] = centers[i, :] + rock.data.root_pos_w[i, :] + ellipsoid_axes_w[i, :3]  # * 3
            vertices_target[1, :] = centers[i, :] + rock.data.root_pos_w[i, :] + ellipsoid_axes_w[i, 3:6]  # * 3
            vertices_target[2, :] = centers[i, :] + rock.data.root_pos_w[i, :] + ellipsoid_axes_w[i, 6:]  # * 3
            num_lines = vertices_source.shape[0]
            lines_colors = [[1.0, 0.0, 0.0, 1.0]] * num_lines
            line_thicknesses = [4.0] * num_lines
            draw_interface.draw_lines(
                vertices_source.tolist(), vertices_target.tolist(), lines_colors, line_thicknesses
            )
            # oppposite ellipsoid axes
            vertices_target[0, :] = centers[i, :] + rock.data.root_pos_w[i, :] - ellipsoid_axes_w[i, :3]  # * 3
            vertices_target[1, :] = centers[i, :] + rock.data.root_pos_w[i, :] - ellipsoid_axes_w[i, 3:6]  # * 3
            vertices_target[2, :] = centers[i, :] + rock.data.root_pos_w[i, :] - ellipsoid_axes_w[i, 6:]  # * 3
            draw_interface.draw_lines(
                vertices_source.tolist(), vertices_target.tolist(), lines_colors, line_thicknesses
            )


import re


def update_measurements(rock, axes, num_envs):
    quat_w = rock.data.root_quat_w[:, :]
    axes_x_w = torch_utils.quat_rotate(quat_w[:], axes[:, 0, :])  # torch.tile(axes[0, :], (num_envs, 1)))
    axes_y_w = torch_utils.quat_rotate(quat_w[:], axes[:, 1, :])  # torch.tile(axes[1, :], (num_envs, 1)))
    axes_z_w = torch_utils.quat_rotate(quat_w[:], axes[:, 2, :])  # torch.tile(axes[2, :], (num_envs, 1)))
    ellipsoid_axes_w = torch.stack([axes_x_w, axes_y_w, axes_z_w], dim=1).reshape(-1, 9)
    return ellipsoid_axes_w


def extract_usd_number_and_scale(string):
    # Split the string at the last '/' and get the part after it
    last_part = string.split("/")[-1]

    # Split the last part by '_' to get the individual components
    parts = last_part.split("_")

    # Extract the usd_number and scale
    usd_number = parts[2]  # The 3rd element is the usd_number
    scale = float(parts[3][0] + "." + parts[3][1:])  # The 4th element is the scale

    return usd_number, scale


import csv
import numpy as np


def find_ellipsoide_data(
    filename_to_find,
    csv_file="/home/<USER>/Coding/IsaacLab/source/extensions/isaaclab_tasks/omni/isaac/lab_tasks/manager_based/m545/utils/single_boulder_excavation/ellipsoid_data_least_square.csv",
):
    with open(csv_file) as file:
        reader = csv.DictReader(file)

        for row in reader:
            if row["File"] == filename_to_find:
                center = str_to_array(row["Center"], shape=(3,))
                radii = str_to_array(row["Radii"], shape=(3,))
                evecs = str_to_array(row["Eigenvectors"], shape=(3, 3))
                scaled_axes = str_to_array(row["Scaled Axes"], shape=(3, 3))

                return center, radii, evecs, scaled_axes

        return None  # Return None if the file is not found


# Helper function to convert a comma-separated string back to a NumPy array
def str_to_array(s, shape=None):
    arr = np.array(list(map(float, s.split(","))))
    if shape:
        arr = arr.reshape(shape)
    return arr


def main():
    """Main function."""
    # Initialize the simulation context
    sim_cfg = sim_utils.SimulationCfg(dt=0.005)
    sim = sim_utils.SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view(eye=[3.5, 3.5, 3.5], target=[0.0, 0.0, 0.0])
    # design scene
    scene_cfg = BoulderSceneCfg(num_envs=args_cli.num_envs, env_spacing=5.0)
    scene = InteractiveScene(scene_cfg)
    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    # Run the simulator
    run_simulator(sim, scene)


def to_np(tensor: torch.Tensor) -> np.ndarray:
    """
    Converts a PyTorch tensor to a NumPy array.

    Args:
        tensor (torch.Tensor): The input PyTorch tensor.

    Returns:
        np.ndarray: The converted NumPy array.
    """
    # Check if the tensor is on GPU and move it to CPU if necessary
    if tensor.is_cuda:
        tensor = tensor.cpu()

    # Convert the tensor to a NumPy array
    return tensor.numpy()


if __name__ == "__main__":
    # run the main function
    main()
    # close sim app
    simulation_app.close()
