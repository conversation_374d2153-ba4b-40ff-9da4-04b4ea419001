# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test force compensation controller, boulder in bucket, plots wrong because of action scaling"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-single-boulder-raycast-shovel"
args_cli.num_envs = 1
args_cli.headless = False

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import numpy as np
import torch
import traceback

import carb
from rsl_rl.runners import OnPolicyRunner

import isaaclab_tasks  # noqa: F401
from isaaclab_tasks.utils import parse_env_cfg

from moleworks_ext.tasks.single_boulder_excavation.devices.gamepad.gamepad_control_driving_and_arm import (
    m545Gamepad_driving_and_arm,
)

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""

    

    teleop_interface = m545Gamepad_driving_and_arm(v_x_sensitivity=5, v_y_sensitivity=0.5, omega_z_sensitivity=1)
    # parse env configuration
    env_cfg = parse_env_cfg(args_cli.task, num_envs=args_cli.num_envs)
    env_cfg.sim.dt = 0.02
    # env_cfg.reset.only_above_soil = True
    env_cfg.limits.action_scaling = (1,1,1,1)
    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)
    

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")
    # reset environment
    # env.unwrapped.curriculum_excavation.set_level_and_update(2000)
    env.reset()

    # Num steps and num_reset
    num_resets = 100
    num_steps = 1000
    total_resets = (num_steps) * (num_resets) * env.unwrapped.num_envs
    done_count = torch.zeros(1, device=env.unwrapped.device)
    actions = torch.zeros(env.unwrapped.num_envs, 4)

    # run everything in inference mode
    count = 0

    robot = env.unwrapped.scene["robot"]
    boulder = env.unwrapped.scene["object_0"]

    bucket_collision_f = []
    torques = []
    desired_arm_vels = []
    achieved_arm_vels = []

    desired_arm_pos = []
    achieved_arm_pos = []

    joint_pos = torch.tensor([-0.7, 1.6, 0.4962, 1.7], device=env.unwrapped.device).reshape(1, -1)
    
    zero_vel = torch.zeros_like(joint_pos)

    default_boulder_pos = torch.tensor([4.2, 0, 0.5], device=env.unwrapped.device).reshape(1, -1)
    
    t_oscill = 10
    sim_time = 0
    inv_dyn = True

    # Set the action scaling to 0
    action_inv_dyn = zero_vel
    desired_arm_vel = action_inv_dyn
    print('Mass of the boulder: ',boulder.root_physx_view.get_masses())
    target_joint_pos = robot.data.joint_pos.clone()
    #robot.write_joint_state_to_sim(position=joint_pos, velocity=zero_vel)
    root_state = boulder.data.default_root_state.clone()
    root_state[:, :3] = default_boulder_pos[0, :].repeat(env.unwrapped.num_envs,1) + env.unwrapped.scene.env_origins
                
    with torch.inference_mode():

        # compute zero actions
        while simulation_app.is_running():
            #boulder.write_root_state_to_sim(root_state+100)
            if count == 2:
                #joint_pos, joint_vel = robot.data.default_joint_pos, robot.data.default_joint_vel
                #robot.write_root_pose_to_sim(
                #    torch.cat([robot.data.root_pos_w.clone()+ env.unwrapped.scene.env_origins[:].clone(),
                #               robot.data.root_quat_w.clone()],
                #               dim=-1)
                #    )

                env.unwrapped.m545_measurements.update_measurements()
                #robot.write_joint_state_to_sim(action_inv_dyn,velocity=zero_vel)
                env.unwrapped.des_dof_pos = robot.data.joint_pos.clone()
            if count == 10:
                boulder.write_root_state_to_sim(root_state)
                robot.write_joint_state_to_sim(position=joint_pos, velocity=zero_vel)
                env.unwrapped.m545_measurements.update_measurements()
                target_joint_pos = robot.data.joint_pos.clone()
                env.unwrapped.des_dof_pos = robot.data.joint_pos.clone()
            if inv_dyn:
                if sim_time < t_oscill:
                    action_inv_dyn = zero_vel
                    #action_inv_dyn[:, 3] = 1.4462
                    
                else:
                    action_inv_dyn[:, :] = -np.sin((sim_time - 100 * env.unwrapped.physics_dt)  *0.5* 2 * np.pi) * 0.1
                    #action_inv_dyn[:, 3] = 0
                    #action_inv_dyn[:, 3] = 1.4462
                desired_arm_vel = action_inv_dyn
                
                # velocity, steering, cabin_input, arm_input = teleop_interface.advance()
            ## apply actions
            # desired_arm_vel = torch.tensor(arm_input,device=env.device).to(dtype=torch.float32).repeat(env.num_envs,1)

            # print(arm_vel)
            curr_joint_pos = env.unwrapped.scene['robot'].data.joint_pos.clone()
            target_joint_pos += 4*desired_arm_vel.reshape(env.unwrapped.num_envs, 4) * env.unwrapped.physics_dt
            env.unwrapped.target_joint_pos = target_joint_pos
            #obs, rewards, dones, timeouts, infos = env.step(desired_arm_vel.reshape(env.unwrapped.num_envs, 4))
            obs, rewards, dones, timeouts, infos = env.step(desired_arm_vel.clone())
            #boulder.write_root_state_to_sim(root_state+100)
            #env.unwrapped.scene['robot'].write_joint_state_to_sim(position =target_joint_pos,velocity=desired_arm_vel.reshape(env.unwrapped.num_envs, 4))
            # print(robot.data.root_pos_w)
            count += 1
            sim_time = count * env.unwrapped.physics_dt * 4

            bucket_collision_f.append(env.unwrapped.m545_measurements.bucket_contact_force.clone())
            torques.append(env.unwrapped.torques.clone())
            
            desired_arm_vels.append(desired_arm_vel.clone())
            achieved_arm_vels.append(robot.data.joint_vel.clone())
            desired_arm_pos.append(target_joint_pos.clone())
            achieved_arm_pos.append(robot.data.joint_pos.clone())
            
            if count % 300 == 0:
                plot_over_iteration(bucket_collision_f, "bucket collision")
                plot_over_iteration(torques, "Torques")
                plot_trajectories(desired_arm_vels, achieved_arm_vels, "vel")
                plot_trajectories(desired_arm_pos, achieved_arm_pos, "pos")

                plt.show()
                #desired_arm_vels = []
                #achieved_arm_vels = []
                #env.reset()

    env.close()


def plot_over_iteration(tensor_list, title=""):
    tensor_list = torch.cat(tensor_list, dim=0).cpu().numpy()

    # Create a figure and axis
    fig, ax = plt.subplots()

    for i in range(tensor_list.shape[1]):
        # Plot
        ax.plot(range(1, len(tensor_list[:, i]) + 1), tensor_list[:, i], label=i)

    # Set labels and title
    ax.set_xlabel(" ")
    ax.set_ylabel(" ")
    ax.set_title(title)

    # Display legend
    ax.legend()


def plot_trajectories(commanded_velocities, achieved_velocities, title):
    """
    commanded_velocities: tensor of shape (num_steps, num_joints)
    achieved_velocities: tensor of shape (num_steps, num_joints)
    """
    commanded_velocities = torch.cat(commanded_velocities, dim=0).cpu().numpy()
    achieved_velocities = torch.cat(achieved_velocities, dim=0).cpu().numpy()
    fig, axs = plt.subplots(4, 1, figsize=(10, 10))
    fig.suptitle(title)
    for i in range(4):
        axs[i].plot(commanded_velocities[:, i], label="Commanded")
        axs[i].plot(achieved_velocities[:, i], label="Achieved")
        #axs[i].set_ylim([-1, 1])
        axs[i].legend()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
