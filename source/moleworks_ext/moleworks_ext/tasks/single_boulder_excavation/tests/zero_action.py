# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
This script is designed to analyse how the excavation agent reacts when spawned (decoupled from the env).
It is *not* intented to test terminations

"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="This script demonstrates how to simulate a bipedal robot.")
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments

args_cli = parser.parse_args()
args_cli.num_envs = 1
# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import torch
import traceback

import carb

import isaaclab.sim as sim_utils
from isaaclab.assets import Articulation

##
# Pre-defined configs
##
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import SimulationContext
from isaaclab.terrains import TerrainImporter, TerrainImporterCfg

from moleworks_ext.tasks.single_boulder_excavation.env_cfg.m545_single_boulder_excavation_cfg import (
    NUM_OBJECTS_PER_ENV,
    X_LIMIT_NEG,
    X_LIMIT_POS,
    Y_LIMIT_NEG,
    Y_LIMIT_POS,
    ExcavationSceneCfg,
)


def main():
    """Main function."""

    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device="cuda")
    sim = SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view([12, 12, 12], [0.0, 0.0, 0.0])

    # -- Spawn things into stage
    # Lights-1
    # -- Spawn things into stage, use Scence from Cfg
    scene_cfg = ExcavationSceneCfg(num_envs=args_cli.num_envs, env_spacing=15)
    scene = InteractiveScene(scene_cfg)
    robot = scene["robot"]

    # Play the simulator
    sim.reset()

    # Now we are ready!
    print("[INFO]: Setup complete...")
    print("Root state: ", robot.data.default_root_state[:, :7])

    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    sim_time = 0.0
    count = 0

    # rigid objects
    rigid_objects = [scene[f"object_{i}"] for i in range(NUM_OBJECTS_PER_ENV)]

    # Simulate physics
    while simulation_app.is_running():

        # reset
        if count % 10000 == 0:
            sim_time = 0.0
            count = 0
            # reset dof state
            joint_pos, joint_vel = robot.data.default_joint_pos, robot.data.default_joint_vel
            robot.write_joint_state_to_sim(joint_pos, joint_vel)
            robot.data.default_root_state[:, 0] = 0
            robot.data.default_root_state[:, :3] += scene.env_origins
            robot.write_root_pose_to_sim(robot.data.default_root_state[:, :7])
            robot.write_root_velocity_to_sim(robot.data.default_root_state[:, 7:])
            # Rigid object random positions
            randomized_pos = torch.zeros(scene.num_envs, NUM_OBJECTS_PER_ENV, 3, device=scene.device)
            # Fill the x entries with random values between X_LIMIT_NEG and X_LIMIT_POS
            randomized_pos[:, :, 0] = torch.FloatTensor(scene.num_envs, NUM_OBJECTS_PER_ENV).uniform_(
                X_LIMIT_NEG, X_LIMIT_POS
            )
            # Fill the y entries with random values between Y_LIMIT_NEG and Y_LIMIT_POS
            randomized_pos[:, :, 1] = torch.FloatTensor(scene.num_envs, NUM_OBJECTS_PER_ENV).uniform_(
                Y_LIMIT_NEG, Y_LIMIT_POS
            )
            # Set the z entries to 1
            randomized_pos[:, :, 2] = 1

            for i, rigid_object in enumerate(rigid_objects):
                # root state
                root_state = rigid_object.data.default_root_state.clone()
                root_state[:, :3] += randomized_pos[:, i, :].reshape(scene.num_envs, 3)
                root_state[:, :3] += scene.env_origins  # Modify as necessary
                rigid_object.write_root_state_to_sim(root_state)
            # Reset
            scene.reset()
            # reset command
            print(">>>>>>>> Reset!")
        i = 1.0
        print(
            "Object pose", scene["object_0"].data.body_pos_w[:, :, :] - scene.env_origins.reshape(scene.num_envs, 1, 3)
        )
        # velocity_input = torch.tensor([i,0,i,0,0,0], device = robot.device)
        # position_input = torch.tensor([0.0,0.0], device = robot.device)
        # velocity_input = torch.tensor([i,i,i,i], device = robot.device)
        ## apply action to the robot
        # robot.set_joint_position_target(position_input, joint_ids=[0,2])
        # robot.set_joint_velocity_target(velocity_input, joint_ids=[1,3,4,5])
        robot.write_data_to_sim()
        # perform step
        sim.step()
        # update buffers
        scene.update(sim_dt)

        # update sim-time
        sim_time += sim_dt
        count += 1


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
