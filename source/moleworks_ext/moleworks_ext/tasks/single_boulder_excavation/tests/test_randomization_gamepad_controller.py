# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-single-boulder-raycast-shovel"
args_cli.num_envs = 10
args_cli.headless = False

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback

import carb
from rsl_rl.runners import OnPolicyRunner

import isaaclab_tasks  # noqa: F401
from isaaclab_tasks.utils import parse_env_cfg
import moleworks_ext
from moleworks_ext.tasks.single_boulder_excavation.devices.gamepad.gamepad_control_driving_and_arm import (
    m545Gamepad_driving_and_arm,
)

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""

    teleop_interface = m545Gamepad_driving_and_arm(v_x_sensitivity=5, v_y_sensitivity=0.5, omega_z_sensitivity=1)
    # parse env configuration
    env_cfg = parse_env_cfg(args_cli.task, num_envs=args_cli.num_envs)
    #env_cfg.soil_parameters.type = "s_1_0"
    #env_cfg.sim.dt = 0.04
    # env_cfg.reset.only_above_soil = True

    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")
    # reset environment
    # env.unwrapped.curriculum_excavation.set_level_and_update(2000)
    env.reset()

    # Num steps and num_reset
    num_resets = 100
    num_steps = 1000
    total_resets = (num_steps) * (num_resets) * env.unwrapped.num_envs
    done_count = torch.zeros(1, device=env.unwrapped.device)
    actions = torch.rand(env.unwrapped.num_envs, 4)

    # run everything in inference mode
    count = 0

    robot = env.unwrapped.scene["robot"]
    with torch.inference_mode():

        # compute zero actions
        while simulation_app.is_running():
            velocity, steering, cabin_input, arm_input = teleop_interface.advance()
            ## apply actions
            arm_vel = torch.tensor(arm_input, device=env.unwrapped.device).to(dtype=torch.float32).repeat(env.unwrapped.num_envs, 1)
            # print(arm_vel)
            #actions = torch.zeros(env.unwrapped.num_envs, 4)
            #obs, rewards, dones, timeouts, infos = env.step(actions)
            obs, rewards, dones, timeouts, infos = env.step(arm_vel.reshape(env.unwrapped.num_envs, 4))
            #if count % 50 == 0:
            #    #print(valid_hits)
            #    env.reset()
            # print(robot.data.root_pos_w)
            count += 1

    env.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
