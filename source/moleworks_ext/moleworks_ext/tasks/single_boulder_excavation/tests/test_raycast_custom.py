# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""
import os

print("LD_PRELOAD:", os.environ.get("LD_PRELOAD"))
#os.environ.pop("LD_PRELOAD", None)
'''print("LD_PRELOAD:", os.environ.get("LD_PRELOAD"))'''

import argparse
from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-single-boulder-raycast-shovel"
args_cli.num_envs = 5
args_cli.headless = False

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback

import carb
from rsl_rl.runners import OnPolicyRunner

import isaaclab_tasks  # noqa: F401
from isaaclab_tasks.utils import parse_env_cfg
import moleworks_ext
from moleworks_ext.tasks.single_boulder_excavation.devices.gamepad.gamepad_control_driving_and_arm import (
    m545Gamepad_driving_and_arm,
)
import isaacsim.core.utils.torch as torch_utils

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""

    teleop_interface = m545Gamepad_driving_and_arm(v_x_sensitivity=5, v_y_sensitivity=0.5, omega_z_sensitivity=1)
    # parse env configuration
    env_cfg = parse_env_cfg(args_cli.task, num_envs=args_cli.num_envs)
    env_cfg.sim.dt = 0.04
    # env_cfg.reset.only_above_soil = True

    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")
    # reset environment
    # env.unwrapped.curriculum_excavation.set_level_and_update(2000)
    env.reset()

    # Num steps and num_reset
    num_resets = 100
    num_steps = 1000
    total_resets = (num_steps) * (num_resets) * env.unwrapped.num_envs
    done_count = torch.zeros(1, device=env.unwrapped.device)
    actions = torch.rand(env.unwrapped.num_envs, 4)

    # run everything in inference mode
    count = 0

    #robot = env.scene["robot"]
    visualizer =  define_markers()
    with torch.inference_mode():

        # compute zero actions
        while simulation_app.is_running():
            velocity, steering, cabin_input, arm_input = teleop_interface.advance()
            ## apply actions
            arm_vel = torch.tensor(arm_input, device=env.unwrapped.device).to(dtype=torch.float32).repeat(env.unwrapped.num_envs, 1)
            # print(arm_vel)
            actions = torch.zeros(env.unwrapped.num_envs, 4)
            obs, rewards, dones, timeouts, infos = env.step(arm_vel)

            ray_hits_sampled_idx = sample_rock_points_idx(env, target_mesh_id=0, num_samples=15) # ,0,0,0,0
            # Sort points by x-coordinate
            ray_hits_sampled_idx_sorted = sort_ray_hits_by_x(ray_hits_sampled_idx)
            ray_hits_sampled_sorted_idx_centered = ray_hits_sampled_idx_sorted-env.unwrapped.scene.env_origins.unsqueeze(1)
            
            # Center the points around the robot's root position

            mean_tensor = torch.tensor([0, 0.0, 0.0], device=env.unwrapped.device)
            std_tensor = torch.tensor([8.0, 1.0, 1.0], device=env.unwrapped.device)
            ray_hits_sampled_sorted_centered_standarized = (ray_hits_sampled_sorted_idx_centered - mean_tensor) / std_tensor


            if not args_cli.headless:
                #debug_viz_sample_points(ray_hits_sampled_sorted, visualizer)
                debug = (obs["policy"][:,-45:].reshape(env.unwrapped.num_envs,-1,3) + env.unwrapped.scene.env_origins[:, None, :]).reshape(env.unwrapped.num_envs,15,3)
                #yo = ray_cast_boulder_idx_w_noise_base(env,target_mesh_id=0, num_samples=15, noise_x_std=1.0, noise_y_std=0.1, noise_z_std=0.15, edge_percentage=0.15, std=[1.0,1.0,1.0])
                #print(obs["policy"].shape)
                #print(yo.shape)
                #debug_viz_sample_points(yo[0,:].reshape(-1,3), visualizer)
                debug_viz_sample_points(debug, visualizer)
                #debug_viz_sample_points(obs["policy"][0,:45].reshape(-1,3), visualizer)
                # check if points are generated according to a pattern
            #if count % 50 == 0:
                #for i in range((ray_hits.shape[0])):
                #    print("env ", i)
                #    print((ray_hits[i][~torch.isinf(ray_hits[i])]).shape)
                #    non_inf_ray_hits = ray_hits[i][~torch.isinf(ray_hits[i])].shape
                #print(ray_hits.shape)
                
                #mask = ~torch.isinf(ray_hits)
                #valid_hits = ray_hits[~torch.isnan(ray_hits).any(dim=1)]
                #print(valid_hits)
                #env.reset()
            # print(robot.data.root_pos_w)
            #print("step")
            count += 1
            #print("step")

    env.close()

def debug_viz_sample_points(ray_hits_sampled, vizualiser):
    marker_pos = ray_hits_sampled.reshape(-1, 3)
    dim = marker_pos.shape[0]
    zero_orientation = torch.tensor([1, 0, 0, 0], device=ray_hits_sampled.device).expand(dim, -1)
    indices = torch.zeros(dim, device=ray_hits_sampled.device)

    vizualiser.visualize(marker_pos, zero_orientation, marker_indices=indices)




def sample_rock_points_idx(env, target_mesh_id=0, num_samples=15,
                             noise_x_std=1.0, noise_y_std=0.1, noise_z_std=0.15, edge_percentage=0.15):
    """
    Vectorized sampling of points from ray_hits for each environment.
    If an environment has fewer valid (non-inf) hits than num_samples,
    the extra points are set to ([5, 0.0, 0.0] + env.unwrapped.scene.env_origins) for that environment.

    Then, using all valid points (from ray_hits), the overall minimum and maximum y values 
    are computed per environment. These define the overall y-range and, with a configurable 
    fraction (edge_percentage), the lower and upper edge zones. Among the randomly chosen 
    samples, those whose y values fall in these edge zones will get noise added in x, y, and z.
    
    For these edge points:
      - x receives a big positive noise (std = noise_x_std).
      - y receives noise (std = noise_y_std) that is forced negative if the point is near y_min 
        and positive if near y_max.
      - z is re-centered around the soil height for that environment with a little noise (std = noise_z_std).
    
    Args:
        ray_hits: Tensor of shape (B, T, 3)
                  where B = number of environments,
                        T = max number of rays per environment,
                        3 = (x, y, z).
        indices: Tensor of shape (B, T) containing mesh ids for each ray hit.
        target_mesh_id: The mesh id to filter for.
        num_samples: How many points to sample per environment.
        env: An environment object that provides:
             - env.unwrapped.scene.env_origins: Tensor of shape (B, 3).
             - env.soil_height_futures: Tensor (B, N), used to compute soil height.
             - env.m545_measurements.root_pos_w: Tensor of shape (B, ?), where index 2 is used.
        noise_x_std: Standard deviation for x noise (big, positive noise).
        noise_y_std: Standard deviation for y noise.
        noise_z_std: Standard deviation for z noise (small noise).
        edge_percentage: Fraction of the overall y-range (from all valid points) at the lower 
                         and upper extremes to be considered edges.
    
    Returns:
        A tensor of shape (B, num_samples, 3) with the modifications described above.
    """
    # Gather data
    ray_hits = env.unwrapped.scene['raycaster'].data.ray_hits_w
    indices = env.unwrapped.scene['raycaster'].data.mesh_id

    B, T, _ = ray_hits.shape

    # 1. Identify valid hits (non-inf) and points matching target_mesh_id.
    valid_mask = ~torch.isinf(ray_hits).any(dim=-1)  # (B, T)
    mesh_mask = (indices == target_mesh_id)           # (B, T)
    combined_mask = valid_mask & mesh_mask             # (B, T)

    # 2. Generate random scores; set scores for invalid points to -inf.
    random_scores = torch.rand(B, T, device=ray_hits.device)
    random_scores[~combined_mask] = float('-inf')

    # 3. Pick top 'num_samples' indices for each environment.
    _, topk_indices = random_scores.topk(num_samples, dim=1, largest=True)
    sorted_indices, _ = torch.sort(topk_indices, dim=1)

    # 4. Gather (x, y, z) points using the sorted indices.
    sorted_indices_expand = sorted_indices.unsqueeze(-1).expand(-1, -1, 3)
    sampled_points = torch.gather(ray_hits, dim=1, index=sorted_indices_expand)

    # 5. Also gather the corresponding validity of these chosen points.
    valid_sampled = torch.gather(combined_mask, dim=1, index=sorted_indices)

    # 6. Replace invalid sampled points with the default value:
    #    ([5, 0.0, 0.0] + env.unwrapped.scene.env_origins) per environment.
    default_offset = torch.tensor([0, 0.0, 0.0], device=ray_hits.device, dtype=ray_hits.dtype)
    default_value = default_offset.view(1, 1, 3) + env.unwrapped.scene.env_origins.view(B, 1, 3)
    sampled_points = torch.where(valid_sampled.unsqueeze(-1), sampled_points, default_value)

    # 7. Compute overall y statistics from all valid points (from ray_hits).
    y_all = ray_hits[..., 1]  # (B, T)
    inf_tensor = torch.tensor(float('inf'), device=ray_hits.device, dtype=ray_hits.dtype)
    ninf_tensor = torch.tensor(float('-inf'), device=ray_hits.device, dtype=ray_hits.dtype)
    y_min_all = torch.where(combined_mask, y_all, inf_tensor).min(dim=1)[0]  # (B,)
    y_max_all = torch.where(combined_mask, y_all, ninf_tensor).max(dim=1)[0]  # (B,)
    has_valid_all = combined_mask.any(dim=1)  # (B,)
    y_min_all = torch.where(has_valid_all, y_min_all, torch.zeros_like(y_min_all))
    y_max_all = torch.where(has_valid_all, y_max_all, torch.zeros_like(y_max_all))
    y_range_all = y_max_all - y_min_all  # (B,)

    # 8. Establish overall y thresholds based on edge_percentage.
    lower_threshold = y_min_all + edge_percentage * y_range_all  # (B,)
    upper_threshold = y_max_all - edge_percentage * y_range_all  # (B,)
    lower_threshold_exp = lower_threshold.unsqueeze(1)  # (B, 1)
    upper_threshold_exp = upper_threshold.unsqueeze(1)  # (B, 1)

    # Now, determine which of the randomly chosen samples are in the edge zones.
    candidate_edge_mask = valid_sampled & ((sampled_points[..., 1] <= lower_threshold_exp) |
                                             (sampled_points[..., 1] >= upper_threshold_exp))
    # For environments with zero y-range (but with valid points), treat all valid ones as edge.
    range_zero = (y_range_all.abs() < 1e-6) & has_valid_all  # (B,)
    edge_mask = torch.where(range_zero.unsqueeze(1), valid_sampled, candidate_edge_mask)

    # 9. Compute soil height per environment.
    world_heights = env.unwrapped.soil_height_futures  # e.g. (B, N)
    root_pos_w_expanded = env.unwrapped.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_heights.size(1))
    height = world_heights - root_pos_w_expanded
    soil_height_per_env = height.mean(dim=-1)  # (B,)

    # 10. Generate noise for edge points.
    # For x: big positive noise.
    noise_x = torch.abs(torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype)) * noise_x_std

    # For y: noise with sign determined by proximity to overall y_min or y_max.
    noise_y = torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype) * noise_y_std
    lower_edge_mask = edge_mask & (sampled_points[..., 1] <= lower_threshold_exp)
    upper_edge_mask = edge_mask & (sampled_points[..., 1] >= upper_threshold_exp)
    noise_y = torch.where(lower_edge_mask, -torch.abs(noise_y), noise_y)
    noise_y = torch.where(upper_edge_mask,  torch.abs(noise_y), noise_y)

    # For z: little noise, re-centered at the soil height.
    noise_z = torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype) * noise_z_std

    # 11. Apply noise to the edge points.
    sampled_points[..., 0] = sampled_points[..., 0] + noise_x * edge_mask.type_as(noise_x)
    sampled_points[..., 1] = sampled_points[..., 1] + noise_y * edge_mask.type_as(noise_y)
    soil_height_per_env_expanded = soil_height_per_env.unsqueeze(1)  # (B, 1)
    sampled_points[..., 2] = torch.where(edge_mask,
                                          soil_height_per_env_expanded + noise_z,
                                          sampled_points[..., 2])

    return sampled_points








def sort_ray_hits_by_x(ray_hits_sampled):
    """
    Sorts ray_hits_sampled by descending x-coordinate (the first column)
    for each environment (batch) independently.

    Args:
        ray_hits_sampled (torch.Tensor): shape (B, N, 3)
            B = number of environments
            N = number of points per environment
            3 = (x, y, z)

    Returns:
        sorted_hits (torch.Tensor): shape (B, N, 3),
            sorted by x descending for each environment
    """
    # Extract x-coordinates: shape (B, N)
    x_coords = ray_hits_sampled[..., 0]

    # Sort indices along dimension=1 (the point dimension) in descending order
    _, sorted_indices = x_coords.sort(dim=1, descending=True)

    # Gather the full [x, y, z] rows using sorted indices
    # sorted_indices has shape (B, N), so expand it for gathering
    sorted_hits = torch.gather(
        ray_hits_sampled,
        dim=1,
        index=sorted_indices.unsqueeze(-1).expand(-1, -1, ray_hits_sampled.size(-1))
    )

    return sorted_hits

from isaaclab.markers import VisualizationMarkers, VisualizationMarkersCfg
import isaaclab.sim as sim_utils

def define_markers() -> VisualizationMarkers:
    """Define markers with various different shapes."""
    marker_cfg = VisualizationMarkersCfg(
        prim_path="/Visuals/rock_markerrs",
        markers={
            "rock_sampled": sim_utils.SphereCfg(
                radius=0.05,
                visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0)),
            ),
        },
    )
    return VisualizationMarkers(marker_cfg)


def ray_cast_boulder_idx_w_noise_base(env, target_mesh_id=0, num_samples=15, noise_x_std=1.0, noise_y_std=0.1, noise_z_std=0.15, edge_percentage=0.15, std=[1.0,1.0,1.0]):
    """
    Vectorized sampling of points from ray_hits for each environment.
    If an environment has fewer valid (non-inf) hits than num_samples,
    the extra points are set to ([5, 0.0, 0.0] + env.unwrapped.scene.env_origins) for that environment.

    Then, using all valid points (from ray_hits), the overall minimum and maximum y values 
    are computed per environment. These define the overall y-range and, with a configurable 
    fraction (edge_percentage), the lower and upper edge zones. Among the randomly chosen 
    samples, those whose y values fall in these edge zones will get noise added in x, y, and z.
    
    For these edge points:
      - x receives a big positive noise (std = noise_x_std).
      - y receives noise (std = noise_y_std) that is forced negative if the point is near y_min 
        and positive if near y_max.
      - z is re-centered around the soil height for that environment with a little noise (std = noise_z_std).
    
    Args:
        ray_hits: Tensor of shape (B, T, 3)
                  where B = number of environments,
                        T = max number of rays per environment,
                        3 = (x, y, z).
        indices: Tensor of shape (B, T) containing mesh ids for each ray hit.
        target_mesh_id: The mesh id to filter for.
        num_samples: How many points to sample per environment.
        env: An environment object that provides:
             - env.unwrapped.scene.env_origins: Tensor of shape (B, 3).
             - env.soil_height_futures: Tensor (B, N), used to compute soil height.
             - env.m545_measurements.root_pos_w: Tensor of shape (B, ?), where index 2 is used.
        noise_x_std: Standard deviation for x noise (big, positive noise).
        noise_y_std: Standard deviation for y noise.
        noise_z_std: Standard deviation for z noise (small noise).
        edge_percentage: Fraction of the overall y-range (from all valid points) at the lower 
                         and upper extremes to be considered edges.
    
    Returns:
        A tensor of shape (B, num_samples, 3) with the modifications described above.
    """
    # Gather data
    ray_hits = env.unwrapped.scene['raycaster'].data.ray_hits_w
    indices = env.unwrapped.scene['raycaster'].data.mesh_id

    B, T, _ = ray_hits.shape

    # 1. Identify valid hits (non-inf) and points matching target_mesh_id.
    valid_mask = ~torch.isinf(ray_hits).any(dim=-1)  # (B, T)
    mesh_mask = (indices == target_mesh_id)           # (B, T)
    combined_mask = valid_mask & mesh_mask             # (B, T)

    # 2. Generate random scores; set scores for invalid points to -inf.
    random_scores = torch.rand(B, T, device=ray_hits.device)
    random_scores[~combined_mask] = float('-inf')

    # 3. Pick top 'num_samples' indices for each environment.
    _, topk_indices = random_scores.topk(num_samples, dim=1, largest=True)
    sorted_indices, _ = torch.sort(topk_indices, dim=1)

    # 4. Gather (x, y, z) points using the sorted indices.
    sorted_indices_expand = sorted_indices.unsqueeze(-1).expand(-1, -1, 3)
    sampled_points = torch.gather(ray_hits, dim=1, index=sorted_indices_expand)

    # 5. Also gather the corresponding validity of these chosen points.
    valid_sampled = torch.gather(combined_mask, dim=1, index=sorted_indices)

    # 6. Replace invalid sampled points with the default value:
    #    ([5, 0.0, 0.0] + env.unwrapped.scene.env_origins) per environment.
    default_offset = torch.tensor([0, 0.0, 0.0], device=ray_hits.device, dtype=ray_hits.dtype)
    default_value = default_offset.view(1, 1, 3) + env.unwrapped.scene.env_origins.view(B, 1, 3)
    sampled_points = torch.where(valid_sampled.unsqueeze(-1), sampled_points, default_value)

    # 7. Compute overall y statistics from all valid points (from ray_hits).
    y_all = ray_hits[..., 1]  # (B, T)
    inf_tensor = torch.tensor(float('inf'), device=ray_hits.device, dtype=ray_hits.dtype)
    ninf_tensor = torch.tensor(float('-inf'), device=ray_hits.device, dtype=ray_hits.dtype)
    y_min_all = torch.where(combined_mask, y_all, inf_tensor).min(dim=1)[0]  # (B,)
    y_max_all = torch.where(combined_mask, y_all, ninf_tensor).max(dim=1)[0]  # (B,)
    has_valid_all = combined_mask.any(dim=1)  # (B,)
    y_min_all = torch.where(has_valid_all, y_min_all, torch.zeros_like(y_min_all))
    y_max_all = torch.where(has_valid_all, y_max_all, torch.zeros_like(y_max_all))
    y_range_all = y_max_all - y_min_all  # (B,)

    # 8. Establish overall y thresholds based on edge_percentage.
    lower_threshold = y_min_all + edge_percentage * y_range_all  # (B,)
    upper_threshold = y_max_all - edge_percentage * y_range_all  # (B,)
    lower_threshold_exp = lower_threshold.unsqueeze(1)  # (B, 1)
    upper_threshold_exp = upper_threshold.unsqueeze(1)  # (B, 1)

    # Now, determine which of the randomly chosen samples are in the edge zones.
    candidate_edge_mask = valid_sampled & ((sampled_points[..., 1] <= lower_threshold_exp) |
                                             (sampled_points[..., 1] >= upper_threshold_exp))
    # For environments with zero y-range (but with valid points), treat all valid ones as edge.
    range_zero = (y_range_all.abs() < 1e-6) & has_valid_all  # (B,)
    edge_mask = torch.where(range_zero.unsqueeze(1), valid_sampled, candidate_edge_mask)

    # 9. Compute soil height per environment.
    world_heights = env.soil_height_futures  # e.g. (B, N)
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_heights.size(1))
    height = world_heights - root_pos_w_expanded
    soil_height_per_env = height.mean(dim=-1)  # (B,)

    # 10. Generate noise for edge points.
    # For x: big positive noise.
    noise_x = torch.abs(torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype)) * noise_x_std

    # For y: noise with sign determined by proximity to overall y_min or y_max.
    noise_y = torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype) * noise_y_std
    lower_edge_mask = edge_mask & (sampled_points[..., 1] <= lower_threshold_exp)
    upper_edge_mask = edge_mask & (sampled_points[..., 1] >= upper_threshold_exp)
    noise_y = torch.where(lower_edge_mask, -torch.abs(noise_y), noise_y)
    noise_y = torch.where(upper_edge_mask,  torch.abs(noise_y), noise_y)

    # For z: little noise, re-centered at the soil height.
    noise_z = torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype) * noise_z_std

    # 11. Apply noise to the edge points.
    sampled_points[..., 0] = sampled_points[..., 0] + noise_x * edge_mask.type_as(noise_x)
    sampled_points[..., 1] = sampled_points[..., 1] + noise_y * edge_mask.type_as(noise_y)
    soil_height_per_env_expanded = soil_height_per_env.unsqueeze(1)  # (B, 1)
    sampled_points[..., 2] = torch.where(edge_mask,
                                          soil_height_per_env_expanded + noise_z,
                                          sampled_points[..., 2])

    # transform points to base
    # transform to base frame
    rel_points_flat = (sampled_points - env.m545_measurements.root_pos_w[:, None, :]).reshape(env.num_envs * num_samples, 3)
    expanded_quat_flat = (
        env.unwrapped.m545_measurements.root_quat_w.unsqueeze(1).expand(-1, num_samples, -1).reshape(env.num_envs * num_samples, 4)
    )
    rotated_flat = torch_utils.quat_rotate_inverse(expanded_quat_flat, rel_points_flat)
    ray_hits_sampled_base = rotated_flat.view(env.num_envs, num_samples, 3)

    # sort alongg x
    std_tensor = torch.tensor(std, device=env.device)
    ray_hits_sampled_base_sorted = sort_ray_hits_by_x(ray_hits_sampled_base)/std_tensor

    return ray_hits_sampled_base_sorted.reshape(env.num_envs, -1)

if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
