# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Test Termination Condition for Excavation"""



from __future__ import annotations

"""Launch Isaac Sim Simulator first."""

import os

# Iterate through all environment variables and print each key=value pair
for key, value in os.environ.items():
    print(f"{key}={value}")

import argparse
import os

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Test Termination Condition for Excavation Environment")
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
parser.add_argument("--seed", type=int, default=None, help="Seed used for the environment")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-single-boulder-raycast"
args_cli.num_envs = 10
args_cli.headless = False

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import matplotlib.pyplot as plt
import torch
import traceback
import isaacsim.core.utils.torch as torch_utils

import carb
from rsl_rl.runners import OnPolicyRunner

import isaaclab_tasks  # noqa: F401
from isaaclab_tasks.utils import parse_env_cfg
import moleworks_ext
#from moleworks_ext.tasks.single_boulder_excavation.devices.gamepad.gamepad_control_driving_and_arm import (
#    m545Gamepad_driving_and_arm,
#)

DESKTOP_PATH = os.path.join(os.path.expanduser("~"), "Desktop")


def main():
    """Zero actions agent with Orbit environment."""

    #teleop_interface = m545Gamepad_driving_and_arm(v_x_sensitivity=5, v_y_sensitivity=0.5, omega_z_sensitivity=1)
    # parse env configuration
    env_cfg = parse_env_cfg(args_cli.task, num_envs=args_cli.num_envs)
    env_cfg.sim.dt = 0.04
    # env_cfg.reset.only_above_soil = True

    # create environment
    env = gym.make(args_cli.task, cfg=env_cfg)

    # print info (this is vectorized environment)
    print(f"[INFO]: Gym observation space: {env.observation_space}")
    print(f"[INFO]: Gym action space: {env.action_space}")
    # reset environment
    # env.unwrapped.curriculum_excavation.set_level_and_update(2000)
    env.reset()

    # Num steps and num_reset
    num_resets = 100
    num_steps = 1000
    total_resets = (num_steps) * (num_resets) * env.unwrapped.num_envs
    done_count = torch.zeros(1, device=env.unwrapped.device)
    actions = torch.rand(env.unwrapped.num_envs, 4)

    # run everything in inference mode
    count = 0
    num_points = 15
    robot = env.unwrapped.scene["robot"]
    visualizer =  define_markers()
    with torch.inference_mode():

        # compute zero actions
        while simulation_app.is_running():
            #velocity, steering, cabin_input, arm_input = teleop_interface.advance()
            ## apply actions
            #arm_vel = torch.tensor(arm_input, device=env.unwrapped.device).to(dtype=torch.float32).repeat(env.num_envs, 1)
            # print(arm_vel)
            actions = torch.zeros(env.unwrapped.num_envs, 4)
            obs, rewards, dones, timeouts, infos = env.step(actions)
            #obs, rewards, dones, timeouts, infos = env.step(arm_vel.reshape(env.unwrapped.num_envs, 4))
            ray_hits = env.unwrapped.scene['raycaster'].data.ray_hits_w
            ray_hits_sampled = sample_rock_points(ray_hits, num_points)

            # transform to base frame
            rel_points_flat = (ray_hits_sampled - env.unwrapped.m545_measurements.root_pos_w[:, None, :]).reshape(env.unwrapped.num_envs * num_points, 3)
            expanded_quat_flat = (
                env.unwrapped.m545_measurements.root_quat_w.unsqueeze(1).expand(-1, num_points, -1).reshape(env.unwrapped.num_envs * num_points, 4)
            )
            rotated_flat = torch_utils.quat_rotate_inverse(expanded_quat_flat, rel_points_flat)
            ray_hits_sampled_base = rotated_flat.view(env.unwrapped.num_envs, num_points, 3)
           
            # Sort points by x-coordinate
            ray_hits_sampled_sorted = sort_ray_hits_by_x(ray_hits_sampled_base)
            
            
            ray_hits_sampled_sorted_centered = ray_hits_sampled_sorted#-env.unwrapped.scene.env_origins.unsqueeze(1)
            # Center the points around the robot's root position

            meax_x = ray_hits_sampled_sorted_centered[..., 0].mean()
            meax_y = ray_hits_sampled_sorted_centered[..., 1].mean()
            meax_z = ray_hits_sampled_sorted_centered[..., 2].mean()
            print("mean rock position:", meax_x, meax_y, meax_z)

            mean_tensor = torch.tensor([5, 0.0, 0.0], device=env.unwrapped.device)
            std_tensor = torch.tensor([2.5, 1.0, 1.0], device=env.unwrapped.device)
            ray_hits_sampled_sorted_centered_standarized = (ray_hits_sampled_sorted_centered - mean_tensor) / std_tensor


            if not args_cli.headless:
                debug_viz_sample_points(ray_hits_sampled_sorted+env.unwrapped.m545_measurements.root_pos_w[:, None, :], visualizer)
                # check if points are generated according to a pattern
            if count % 50 == 0:
                #for i in range((ray_hits.shape[0])):
                #    print("env ", i)
                #    print((ray_hits[i][~torch.isinf(ray_hits[i])]).shape)
                #    non_inf_ray_hits = ray_hits[i][~torch.isinf(ray_hits[i])].shape
                #print(ray_hits.shape)
                
                mask = ~torch.isinf(ray_hits)
                #valid_hits = ray_hits[~torch.isnan(ray_hits).any(dim=1)]
                #print(valid_hits)
                env.reset()
            # print(robot.data.root_pos_w)
            print("step")
            count += 1
            #print("step")

    env.close()

def debug_viz_sample_points(ray_hits_sampled, vizualiser):
    marker_pos = ray_hits_sampled.reshape(-1, 3)
    dim = marker_pos.shape[0]
    zero_orientation = torch.tensor([1, 0, 0, 0], device=ray_hits_sampled.device).expand(dim, -1)
    indices = torch.zeros(dim, device=ray_hits_sampled.device)

    vizualiser.visualize(marker_pos, zero_orientation, marker_indices=indices)

'''def sample_rock_points(ray_hits, num_samples):
    """
    ray_hits: Tensor of shape (B, T, 3)
              B = number of environments
              T = max number of rays per environment
              3 = (x, y, z)
    num_samples: how many points to sample per environment

    Returns:
    A tensor of shape (B, num_samples, 3),
    containing num_samples random valid (not inf) points per environment,
    in ascending order of their original indices.
    """

    # Mark valid hits (not inf in any component)
    # shape: (B, T)
    valid_mask = ~torch.isinf(ray_hits).any(dim=-1)

    # Draw random numbers for each point (only valid points will compete for top-k)
    # shape: (B, T)
    random_scores = torch.rand(ray_hits.shape[0], ray_hits.shape[1], device=ray_hits.device)

    # Invalidate points that are inf by giving them a sentinel score
    random_scores[~valid_mask] = -1  # so they won't be in topk

    # Select the top 'num_samples' indices per batch by random score
    # (largest => effectively random sample among the valid points)
    _, topk_indices = random_scores.topk(num_samples, dim=1)

    # Sort those indices in ascending order to preserve the original order
    # shape: (B, num_samples)
    sorted_indices, _ = topk_indices.sort(dim=1)

    # Expand for gathering the (x,y,z) coordinates
    # shape: (B, num_samples, 3)
    sorted_indices_expand = sorted_indices.unsqueeze(-1).expand(-1, -1, ray_hits.size(-1))
    sampled_points = torch.gather(ray_hits, 1, sorted_indices_expand)

    return sampled_points'''

import torch

def sample_rock_points(ray_hits, num_samples):
    """
    Samples 'num_samples' points from ray_hits for each environment.
    If an environment has fewer valid (non-inf) hits than 'num_samples', 
    the extra points will be invalid and thus set to zero.

    Args:
        ray_hits: Tensor of shape (B, T, 3)
                  B = number of environments
                  T = max number of rays per environment
                  3 = (x, y, z)
        num_samples: how many points to sample per environment

    Returns:
        A tensor of shape (B, num_samples, 3).
        If valid points are fewer than num_samples in a given environment, 
        the extra entries are set to zero.
    """
    B, T, _ = ray_hits.shape

    # 1. Identify valid hits (not inf in any component) => shape (B, T)
    valid_mask = ~torch.isinf(ray_hits).any(dim=-1)

    # 2. Generate random scores in [0, 1) for each point
    random_scores = torch.rand(B, T, device=ray_hits.device)

    # 3. Mark invalid points with -inf so they rank last
    random_scores[~valid_mask] = float('-inf')

    # 4. Pick top 'num_samples' from each batch along dimension=1
    #    If valid points < num_samples, the remaining will be -inf
    _, topk_indices = random_scores.topk(num_samples, dim=1, largest=True)

    # (Optional) sort selected indices so you preserve original ordering
    # If you don't need ordering, you can skip this step.
    sorted_indices, _ = torch.sort(topk_indices, dim=1)

    # 5. Gather (x, y, z) => shape (B, num_samples, 3)
    sorted_indices_expand = sorted_indices.unsqueeze(-1).expand(-1, -1, 3)
    sampled_points = torch.gather(ray_hits, dim=1, index=sorted_indices_expand)

    # 6. Replace any inf values in sampled_points with 0
    #    (occurs if valid_count < num_samples in that environment)
    inf_mask = torch.isinf(sampled_points)
    sampled_points[inf_mask] = 0

    # 8. Count how many invalid data we replaced
    num_invalid = inf_mask.sum().item()  # total count of inf values replaced

    print(f"Number of invalid data replaced with 0: {num_invalid}")

    return sampled_points

def sort_ray_hits_by_x(ray_hits_sampled):
    """
    Sorts ray_hits_sampled by descending x-coordinate (the first column)
    for each environment (batch) independently.

    Args:
        ray_hits_sampled (torch.Tensor): shape (B, N, 3)
            B = number of environments
            N = number of points per environment
            3 = (x, y, z)

    Returns:
        sorted_hits (torch.Tensor): shape (B, N, 3),
            sorted by x descending for each environment
    """
    # Extract x-coordinates: shape (B, N)
    x_coords = ray_hits_sampled[..., 0]

    # Sort indices along dimension=1 (the point dimension) in descending order
    _, sorted_indices = x_coords.sort(dim=1, descending=True)

    # Gather the full [x, y, z] rows using sorted indices
    # sorted_indices has shape (B, N), so expand it for gathering
    sorted_hits = torch.gather(
        ray_hits_sampled,
        dim=1,
        index=sorted_indices.unsqueeze(-1).expand(-1, -1, ray_hits_sampled.size(-1))
    )

    return sorted_hits

from isaaclab.markers import VisualizationMarkers, VisualizationMarkersCfg
import isaaclab.sim as sim_utils

def define_markers() -> VisualizationMarkers:
    """Define markers with various different shapes."""
    marker_cfg = VisualizationMarkersCfg(
        prim_path="/Visuals/rock_markerrs",
        markers={
            "rock_sampled": sim_utils.SphereCfg(
                radius=0.05,
                visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0)),
            ),
        },
    )
    return VisualizationMarkers(marker_cfg)

if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
