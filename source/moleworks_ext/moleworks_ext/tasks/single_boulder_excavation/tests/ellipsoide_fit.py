# Load the CSV file into a NumPy array
import argparse
import matplotlib.pyplot as plt
import numpy as np

from mpl_toolkits.mplot3d import Axes3D

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Tutorial on adding sensors on a robot.")
parser.add_argument("--num_envs", type=int, default=1, help="Number of environments to spawn.")
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app
import numpy as np
from numpy.linalg import eig, inv
from scipy.spatial import ConvexHull, convex_hull_plot_2d

from pxr import Usd, UsdGeom

from moleworks_ext import MOLEWORKS_RSC_DIR

boulder_example_path = f"{MOLEWORKS_RSC_DIR}/single_boulder_excavation/boulders/rocks_rigid_objects/0043.usd"
stage = Usd.Stage.Open(boulder_example_path)
mesh_found = False
for prim in stage.Traverse():
    if prim.IsA(UsdGeom.Mesh):
        geom = UsdGeom.Mesh(prim)
        import numpy as np

        points = np.array(geom.GetPointsAttr().Get())
        mesh_found = True
        break

x = points[:, 0]
y = points[:, 1]
z = points[:, 2]


def ls_ellipsoid(xx, yy, zz):
    # finds best fit ellipsoid. Found at http://www.juddzone.com/ALGORITHMS/least_squares_3D_ellipsoid.html
    # least squares fit to a 3D-ellipsoid
    #  Ax^2 + By^2 + Cz^2 +  Dxy +  Exz +  Fyz +  Gx +  Hy +  Iz  = 1
    #
    # Note that sometimes it is expressed as a solution to
    #  Ax^2 + By^2 + Cz^2 + 2Dxy + 2Exz + 2Fyz + 2Gx + 2Hy + 2Iz  = 1
    # where the last six terms have a factor of 2 in them
    # This is in anticipation of forming a matrix with the polynomial coefficients.
    # Those terms with factors of 2 are all off diagonal elements.  These contribute
    # two terms when multiplied out (symmetric) so would need to be divided by two

    # change xx from vector of length N to Nx1 matrix so we can use hstack
    x = xx[:, np.newaxis]
    y = yy[:, np.newaxis]
    z = zz[:, np.newaxis]

    #  Ax^2 + By^2 + Cz^2 +  Dxy +  Exz +  Fyz +  Gx +  Hy +  Iz = 1
    J = np.hstack((x * x, y * y, z * z, x * y, x * z, y * z, x, y, z))
    K = np.ones_like(x)  # column of ones

    # np.hstack performs a loop over all samples and creates
    # a row in J for each x,y,z sample:
    # J[ix,0] = x[ix]*x[ix]
    # J[ix,1] = y[ix]*y[ix]
    # etc.

    JT = J.transpose()
    JTJ = np.dot(JT, J)
    InvJTJ = np.linalg.inv(JTJ)
    ABC = np.dot(InvJTJ, np.dot(JT, K))

    # Rearrange, move the 1 to the other side
    #  Ax^2 + By^2 + Cz^2 +  Dxy +  Exz +  Fyz +  Gx +  Hy +  Iz - 1 = 0
    #    or
    #  Ax^2 + By^2 + Cz^2 +  Dxy +  Exz +  Fyz +  Gx +  Hy +  Iz + J = 0
    #  where J = -1
    eansa = np.append(ABC, -1)

    return eansa


def polyToParams3D(vec, printMe):
    # gets 3D parameters of an ellipsoid. Found at http://www.juddzone.com/ALGORITHMS/least_squares_3D_ellipsoid.html
    # convert the polynomial form of the 3D-ellipsoid to parameters
    # center, axes, and transformation matrix
    # vec is the vector whose elements are the polynomial
    # coefficients A..J
    # returns (center, axes, rotation matrix)

    # Algebraic form: X.T * Amat * X --> polynomial form

    if printMe:
        print("\npolynomial\n", vec)

    Amat = np.array(
        [
            [vec[0], vec[3] / 2.0, vec[4] / 2.0, vec[6] / 2.0],
            [vec[3] / 2.0, vec[1], vec[5] / 2.0, vec[7] / 2.0],
            [vec[4] / 2.0, vec[5] / 2.0, vec[2], vec[8] / 2.0],
            [vec[6] / 2.0, vec[7] / 2.0, vec[8] / 2.0, vec[9]],
        ]
    )

    if printMe:
        print("\nAlgebraic form of polynomial\n", Amat)

    # See B.Bartoni, Preprint SMU-HEP-10-14 Multi-dimensional Ellipsoidal Fitting
    # equation 20 for the following method for finding the center
    A3 = Amat[0:3, 0:3]
    A3inv = inv(A3)
    ofs = vec[6:9] / 2.0
    center = -np.dot(A3inv, ofs)
    if printMe:
        print("\nCenter at:", center)

    # Center the ellipsoid at the origin
    Tofs = np.eye(4)
    Tofs[3, 0:3] = center
    R = np.dot(Tofs, np.dot(Amat, Tofs.T))
    if printMe:
        print("\nAlgebraic form translated to center\n", R, "\n")

    R3 = R[0:3, 0:3]
    R3test = R3 / R3[0, 0]
    # print('normed \n',R3test)
    s1 = -R[3, 3]
    R3S = R3 / s1
    (el, ec) = eig(R3S)

    recip = 1.0 / np.abs(el)
    axes = np.sqrt(recip)
    if printMe:
        print("\nAxes are\n", axes, "\n")

    inve = inv(ec)  # inverse is actually the transpose here
    if printMe:
        print("\nRotation matrix\n", inve)
    return (center, axes, inve)


# let us assume some definition of x, y and z

# get convex hull
surface = np.stack((x, y, z), axis=-1)
hullV = ConvexHull(surface)
lH = len(hullV.vertices)
hull = np.zeros((lH, 3))
for i in range(len(hullV.vertices)):
    hull[i] = surface[hullV.vertices[i]]
hull = np.transpose(hull)

# fit ellipsoid on convex hull
eansa = ls_ellipsoid(hull[0], hull[1], hull[2])  # get ellipsoid polynomial coefficients
print("coefficients:", eansa)
center, axes, inve = polyToParams3D(eansa, False)  # get ellipsoid 3D parameters
print("center:", center)
print("axes:", axes)
print("rotationMatrix:", inve)

import matplotlib.pyplot as plt

from mpl_toolkits.mplot3d import Axes3D

# Generate ellipsoid surface points
u = np.linspace(0, 2 * np.pi, 100)
v = np.linspace(0, np.pi, 100)
x_local = axes[0] * np.outer(np.cos(u), np.sin(v))
y_local = axes[1] * np.outer(np.sin(u), np.sin(v))
z_local = axes[2] * np.outer(np.ones_like(u), np.cos(v))

x_local_centered = axes[0] * np.outer(np.cos(u), np.sin(v)) + center[0]
y_local_centered = axes[1] * np.outer(np.sin(u), np.sin(v)) + center[1]
z_local_centered = axes[2] * np.outer(np.ones_like(u), np.cos(v)) + center[2]

ellipsoid_points = np.array([x_local.flatten(), y_local.flatten(), z_local.flatten()])
ellipsoid_points_centered = np.array(
    [x_local_centered.flatten(), y_local_centered.flatten(), z_local_centered.flatten()]
)

# Apply the rotation matrix
rotated_ellipsoid_points = inve @ ellipsoid_points
rotated_ellipsoid_points_centered = inve @ ellipsoid_points_centered


# Reshape the rotated points back into 2D arrays for plotting
x = rotated_ellipsoid_points[0, :].reshape(x_local.shape) + center[0]
y = rotated_ellipsoid_points[1, :].reshape(y_local.shape) + center[1]
z = rotated_ellipsoid_points[2, :].reshape(z_local.shape) + center[2]


# THAT S THE ONE
x_centered_then_rotated = rotated_ellipsoid_points_centered[0, :].reshape(x_local.shape)
y_centered_then_rotated = rotated_ellipsoid_points_centered[1, :].reshape(y_local.shape)
z_centered_then_rotated = rotated_ellipsoid_points_centered[2, :].reshape(z_local.shape)

fig = plt.figure()
ax1 = fig.add_subplot(111, projection="3d")

# Plot ellipsoid
ax1.plot_surface(x, y, z, color="g", alpha=0.5)
ax1.set_title("rotated then centered")
# Plot original point cloud
ax1.scatter(points[:, 0], points[:, 1], points[:, 2])

fig = plt.figure()
ax1 = fig.add_subplot(111, projection="3d")
ax1.set_title("centered_thenRotated")
# Plot ellipsoid
ax1.plot_surface(x_centered_then_rotated, y_centered_then_rotated, z_centered_then_rotated, color="r", alpha=0.5)
# Plot original point cloud
ax1.scatter(points[:, 0], points[:, 1], points[:, 2])


plt.show()
