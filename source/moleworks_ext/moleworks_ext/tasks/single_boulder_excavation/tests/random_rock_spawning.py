# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""This script demonstrates how to spawn multiple objects in multiple environments.
.. code-block:: bash
    # Usage
    ./isaaclab.sh -p source/standalone/demos/multi_object.py --num_envs 512
"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Demo on spawning different objects in multiple environments.")
parser.add_argument("--num_envs", type=int, default=5, help="Number of environments to spawn.")
parser.add_argument("--num_objects_per_env", type=int, default=10, help="Number of objects to spawn.")
parser.add_argument("--randomize", default=True, action="store_true", help="Randomize the objects scale.")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import traceback

import carb

import isaaclab.sim as sim_utils
from isaaclab.assets import AssetBaseCfg, RigidObjectCfg
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import SimulationContext
from isaaclab.utils import configclass

from moleworks_ext.common.sim.spawners.multi_asset import MultiAssetCfg
from moleworks_ext.tasks.single_boulder_excavation.utils.multi_objects_spawning.multi_object_spawning_functions import (
    get_randomized_rocks_assets,
    get_rocks_assets,
)
from moleworks_ext import MOLEWORKS_RSC_DIR

check_size = True
USD_PATHS = [
    #f"/home/<USER>/Documents/Omniverse_m545/rocks_rigid_objects_moleworks/0026.usd"
    f"{MOLEWORKS_RSC_DIR}/single_boulder_excavation/boulders/rocks_rigid_objects/{str(i).zfill(4)}.usd"
    for i in range(49)  # There are 50 of them in the folder
]
DENSITY = 2500 # kg/m³

OBJECT_LIST = [
    RigidObjectCfg(
        prim_path=f"/World/envs/env_.*/Objects_{i}",
        spawn=MultiAssetCfg(assets_cfg=get_randomized_rocks_assets(density=DENSITY, scale=(0.6,0.8), usd_paths=USD_PATHS) if args_cli.randomize else get_rocks_assets()),
        init_state=RigidObjectCfg.InitialStateCfg(pos=(0, 0, i)),
    )
    for i in range(args_cli.num_objects_per_env)
]


@configclass
class MultiObjectSceneCfg(InteractiveSceneCfg):
    """Configuration for a multi-object scene."""

    # ground plane
    ground = AssetBaseCfg(prim_path="/World/ground", spawn=sim_utils.GroundPlaneCfg())

    # lights
    dome_light = AssetBaseCfg(
        prim_path="/World/Light",
        spawn=sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75)),
    )

    def __post_init__(self):
        # Dynamically create object_0, object_1, etc.
        for i, obj_cfg in enumerate(OBJECT_LIST):
            setattr(self, f"object_{i}", obj_cfg)


def run_simulator(sim: SimulationContext, scene: InteractiveScene):
    """Runs the simulation loop."""
    # Extract scene entities
    rigid_objects = [scene[f"object_{i}"] for i in range(args_cli.num_objects_per_env)]
    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    count = 0
    # Simulation loop
    while simulation_app.is_running():
        # Reset
        if count % 1000 == 0:
            # reset counter
            count = 0
            # reset the scene entities
            # root state
            for i, rigid_object in enumerate(rigid_objects):
                # root state
                root_state = rigid_object.data.default_root_state.clone()
                root_state[:, :3] += scene.env_origins  # Modify as necessary
                rigid_object.write_root_state_to_sim(root_state)
                # clear internal buffers
                scene.reset()
                print("[INFO]: Resetting robot state...")
        # Write data to sim
        scene.write_data_to_sim()
        # Perform step
        sim.step()
        # Increment counter
        count += 1
        # Update buffers
        scene.update(sim_dt)


def main():
    """Main function."""
    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device="cuda")
    # sim_cfg.use_fabric = False
    sim = SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view([3.0, 0.0, 3.0], [0.0, 0.0, 0.0])
    # Design scene
    scene_cfg = MultiObjectSceneCfg(num_envs=args_cli.num_envs, env_spacing=10, replicate_physics=False)
    scene = InteractiveScene(scene_cfg)

    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    # Run the simulator
    run_simulator(sim, scene)


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
