# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""This script demonstrates how to spawn multiple objects in multiple environments.
.. code-block:: bash
    # Usage
    ./isaaclab.sh -p source/standalone/demos/multi_object.py --num_envs 512
"""

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""


import argparse

from isaaclab.app import AppLauncher

# add argparse arguments
parser = argparse.ArgumentParser(description="Demo on spawning different objects in multiple environments.")
parser.add_argument("--num_envs", type=int, default=10, help="Number of environments to spawn.")
parser.add_argument("--num_objects_per_env", type=int, default=10, help="Number of objects to spawn.")
parser.add_argument("--randomize", default=True, action="store_true", help="Randomize the objects scale.")


# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
# parse the arguments
args_cli = parser.parse_args()

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import traceback

import carb

import isaaclab.sim as sim_utils
from isaaclab.assets import AssetBaseCfg, RigidObjectCfg
from isaaclab.scene import InteractiveScene, InteractiveSceneCfg
from isaaclab.sim import SimulationContext
from isaaclab.utils import configclass
from moleworks_ext.common.sim.spawners.multi_asset.asset_randomizer_cfg import AssetRandomizerCfg
from moleworks_ext.common.sim.spawners.multi_asset.randomizations import RandomizeScaleCfg, RandomizeScaleCfgw_returned_scale
from moleworks_ext.common.sim.spawners.multi_asset import MultiAssetCfg, MultiAssetNoUSDCfg




def get_assets():
    """
    Retrieve a list of basic geometric shapes (sphere, cuboid, cylinder) with specific configurations.
    """

    # Common properties for the assets, including rigid body, mass, and collision settings
    kwargs = {
        "rigid_props": sim_utils.RigidBodyPropertiesCfg(),
        "mass_props": sim_utils.MassPropertiesCfg(mass=0.05),
        "collision_props": sim_utils.CollisionPropertiesCfg(),
    }

    # Return a list of shape configurations: Sphere, Cuboid, and Cylinder
    return [
        sim_utils.SphereCfg(
            radius=0.25,  # Radius of the sphere
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 0.0, 1.0)),  # Blue color
            **kwargs,
        ),
        sim_utils.CuboidCfg(
            size=(0.25, 0.25, 0.25),  # Dimensions of the cuboid
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(0.0, 1.0, 0.0)),  # Green color
            **kwargs,
        ),
        sim_utils.CylinderCfg(
            radius=0.2,  # Radius of the cylinder
            height=0.3,  # Height of the cylinder
            axis="Y",  # Cylinder's axis orientation
            visual_material=sim_utils.PreviewSurfaceCfg(diffuse_color=(1.0, 0.0, 0.0)),  # Red color
            **kwargs,
        ),
    ]


def get_randomized_assets(num_random_assets):
    """
    retrieve a list of geometric shapes with randomized scale configurations.
    """
    # Get the base geometric shape assets without randomization
    assets = get_assets()


    return {"asset_0":AssetRandomizerCfg(
            child_spawner_cfg=assets[0],  # Base configuration for the sphere
            randomization_cfg=RandomizeScaleCfg(
                x_range=(0.5, 1.25),  # Scale range for randomization
                equal_scale=True,  # Apply the same scale to all dimensions
            ),
            num_random_assets=num_random_assets,  # Number of random variations to create
        ),
         "asset_1":AssetRandomizerCfg(
            child_spawner_cfg=assets[1],  # Base configuration for the sphere
            randomization_cfg=RandomizeScaleCfg(
                x_range=(7, 8),  # Scale range for randomization
                equal_scale=True,  # Apply the same scale to all dimensions
            ),
            num_random_assets=num_random_assets,  # Number of random variations to create
        ),
        "asset_2":AssetRandomizerCfg(
            child_spawner_cfg=assets[2],  # Base configuration for the sphere
            randomization_cfg=RandomizeScaleCfg(
                x_range=(7, 8),  # Scale range for randomization
                equal_scale=True,  # Apply the same scale to all dimensions
            ),
            num_random_assets=num_random_assets,  # Number of random variations to create
        ),
    }

@configclass
class MultiObjectSceneCfg(InteractiveSceneCfg):
    """Configuration for a multi-object scene."""

    # ground plane
    ground = AssetBaseCfg(prim_path="/World/ground", spawn=sim_utils.GroundPlaneCfg())

    # lights
    dome_light = AssetBaseCfg(
        prim_path="/World/Light",
        spawn=sim_utils.DomeLightCfg(intensity=3000.0, color=(0.75, 0.75, 0.75)),
    )
    object_0 = RigidObjectCfg(
    prim_path="{ENV_REGEX_NS}/Objects_0",
    spawn=MultiAssetNoUSDCfg(
        assets_cfg=get_randomized_assets(num_random_assets=10),
        ),
    # ),
    init_state=RigidObjectCfg.InitialStateCfg(pos=(0.0, 7.0, 3)),
    )


def run_simulator(sim: SimulationContext, scene: InteractiveScene):
    """Runs the simulation loop."""
    # Extract scene entities
    #rigid_objects = [scene[f"object_{i}"] for i in range(args_cli.num_objects_per_env)]
    # Define simulation stepping
    sim_dt = sim.get_physics_dt()
    count = 0
    # Simulation loop
    while simulation_app.is_running():
        # Reset
        if count % 1000 == 0:
            # reset counter
            count = 0
            scene.reset()
            print("[INFO]: Resetting robot state...")
        # Write data to sim
        scene.write_data_to_sim()
        # Perform step
        sim.step()
        # Increment counter
        count += 1
        # Update buffers
        scene.update(sim_dt)


def main():
    """Main function."""
    # Load kit helper
    sim_cfg = sim_utils.SimulationCfg(device="cuda")
    # sim_cfg.use_fabric = False
    sim = SimulationContext(sim_cfg)
    # Set main camera
    sim.set_camera_view([3.0, 0.0, 3.0], [0.0, 0.0, 0.0])
    # Design scene
    scene_cfg = MultiObjectSceneCfg(num_envs=args_cli.num_envs, env_spacing=10, replicate_physics=False)
    scene = InteractiveScene(scene_cfg)

    # Play the simulator
    sim.reset()
    # Now we are ready!
    print("[INFO]: Setup complete...")
    # Run the simulator
    run_simulator(sim, scene)


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
