"""<PERSON><PERSON><PERSON> to play a checkpoint if an RL agent from RSL-RL."""

"""Launch Isaac Sim Simulator first."""

import argparse

from isaaclab.app import App<PERSON>auncher
from collections import deque
# local imports
import cli_args  # isort: skip

# add argparse arguments
parser = argparse.ArgumentParser(description="Train an RL agent with RSL-RL.")
parser.add_argument("--video", action="store_true", default=False, help="Record videos during training.")
parser.add_argument("--video_length", type=int, default=200, help="Length of the recorded video (in steps).")
parser.add_argument(
    "--disable_fabric", action="store_true", default=False, help="Disable fabric and use USD I/O operations."
)
parser.add_argument("--num_envs", type=int, default=None, help="Number of environments to simulate.")
parser.add_argument("--task", type=str, default=None, help="Name of the task.")
# append RSL-RL cli arguments
cli_args.add_rsl_rl_args(parser)
# append AppLauncher cli args
AppLauncher.add_app_launcher_args(parser)
args_cli = parser.parse_args()
args_cli.task = "Isaac-m545-single-boulder"
args_cli.num_envs = 100
args_cli.headless = True
# always enable cameras to record video
if args_cli.video:
    args_cli.enable_cameras = True

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import gymnasium as gym
import os
import torch

from rsl_rl.runners import OnPolicyRunner

from isaaclab.envs import DirectMARLEnv, multi_agent_to_single_agent
from isaaclab.utils.dict import print_dict
from isaaclab_tasks.utils import get_checkpoint_path, parse_env_cfg
from isaaclab_rl.rsl_rl import (
    RslRlOnPolicyRunnerCfg,
    RslRlVecEnvWrapper,
    export_policy_as_jit,
    export_policy_as_onnx,
)

# Import extensions to set up environment tasks
import moleworks_ext.tasks  # noqa: F401


def main():
    """Play with RSL-RL agent."""
    # parse configuration
    env_cfg = parse_env_cfg(
        args_cli.task, device=args_cli.device, num_envs=args_cli.num_envs, use_fabric=not args_cli.disable_fabric
    )
    env_cfg.sim.dt = 0.02
    env_cfg.decimation = 8
    env_cfg.soil_parameters.type = "random"
    agent_cfg: RslRlOnPolicyRunnerCfg = cli_args.parse_rsl_rl_cfg(args_cli.task, args_cli)
    
    # specify directory for logging experiments
    log_root_path = os.path.join("logs", "rsl_rl", agent_cfg.experiment_name)
    log_root_path = os.path.abspath(log_root_path)
    print(f"[INFO] Loading experiment from directory: {log_root_path}")
    #agent_cfg.load_run = "2024-11-16_13-02-14_trainrandomnocurr"
    resume_path = get_checkpoint_path(log_root_path, agent_cfg.load_run, agent_cfg.load_checkpoint)
    log_dir = os.path.dirname(resume_path)
    # create isaac environment
    env = gym.make(args_cli.task, cfg=env_cfg, render_mode="rgb_array" if args_cli.video else None)
    # wrap for video recording
    if args_cli.video:
        video_kwargs = {
            "video_folder": os.path.join(log_dir, "videos", "play"),
            "step_trigger": lambda step: step == 0,
            "video_length": args_cli.video_length,
            "disable_logger": True,
        }
        print("[INFO] Recording videos during training.")
        print_dict(video_kwargs, nesting=4)
        env = gym.wrappers.RecordVideo(env, **video_kwargs)

    # convert to single-agent instance if required by the RL algorithm
    if isinstance(env.unwrapped, DirectMARLEnv):
        env = multi_agent_to_single_agent(env)

    # wrap around environment for rsl-rl
    env = RslRlVecEnvWrapper(env)

    print(f"[INFO]: Loading model checkpoint from: {resume_path}")
    # load previously trained model
    ppo_runner = OnPolicyRunner(env, agent_cfg.to_dict(), log_dir=None, device=agent_cfg.device)
    ppo_runner.load(resume_path)

    # obtain the trained policy for inference
    policy = ppo_runner.get_inference_policy(device=env.unwrapped.device)

    # export policy to onnx/jit
    export_model_dir = os.path.join(os.path.dirname(resume_path), "exported")
    export_policy_as_jit(
        ppo_runner.alg.actor_critic, ppo_runner.obs_normalizer, path=export_model_dir, filename="policy.pt"
    )
    export_policy_as_onnx(
        ppo_runner.alg.actor_critic, normalizer=ppo_runner.obs_normalizer, path=export_model_dir, filename="policy.onnx"
    )
    
    # Data to log
    # Termination
    print('Initializing log data')
    term_counts = {}
    for term_name in env.unwrapped.termination_manager._term_names:
        term_counts[term_name] = 0
    # env id that are failing 
    env_ids_failing = torch.zeros(env.num_envs, device = env.device)
    env_ids_success = torch.zeros(env.num_envs, device = env.device)
    # useful variables for termination
    neg_prefix = "negative_termination_"
    pos_prefix = "positive_termination_"

    # reset environment
    obs, _ = env.get_observations()
    timestep = 0
    
    print('Start Evaluating')
    # simulate environment
    for i in range(1000):
        # run everything in inference mode
        with torch.inference_mode():
            # agent stepping
            actions = policy(obs)
            # env stepping
            obs, rew, dones, extras = env.step(actions)
        if args_cli.video:
            timestep += 1
            # Exit the play loop after recording one video
            if timestep == args_cli.video_length:
                break
        if i%100 ==0:
            print('Iteration ', i)

        if dones.any():
            for term_name in env.unwrapped.termination_manager._term_names:
                value = env.unwrapped.termination_manager.get_term(term_name)
                term_counts[term_name] += value.sum()
                # env id that are failing
                #dones_negative = 
                if term_name.startswith(neg_prefix):
                    env_ids_failing += value
                if term_name.startswith(pos_prefix):
                    env_ids_success += value



    total_negatives = 0 
    total_positives = 0       
    for term_name, count in term_counts.items():
        if term_name.startswith(neg_prefix):
            total_negatives += count
        elif term_name.startswith(pos_prefix):
            total_positives += count

    # Calculate totals
    total_terminations = total_positives + total_negatives + term_counts["time_outs"]

    # Prepare data for display
    table_data = []

    # Add individual terminations and their percentages
    for term_name, count in term_counts.items():
        if term_name != "time_outs":
            percentage = (count / total_terminations * 100) if total_terminations > 0 else 0
            table_data.append([term_name, count, f"{percentage:.2f}%"])

    # Add summary for positive and negative
    positive_percentage = (total_positives / total_terminations * 100) if total_terminations > 0 else 0
    negative_percentage = (total_negatives / total_terminations * 100) if total_terminations > 0 else 0
    timeout_percentage = (term_counts["time_outs"]/total_terminations*100) if total_terminations > 0 else 0

    table_data.append(["Total Positive Terminations", total_positives, f"{positive_percentage:.2f}%"])
    table_data.append(["Total Negative Terminations", total_negatives, f"{negative_percentage:.2f}%"])
    table_data.append(["Timeout", term_counts["time_outs"], f"{timeout_percentage:.2f}%"])

    # Print the table
    headers = ["Termination Type", "Count", "Percentage"]
    from tabulate import tabulate
    print(tabulate(table_data, headers, tablefmt="grid"))

    failing_indices = torch.where(env_ids_failing != 0)[0].tolist()
    #print("Env, USd file, scales, # failures:")
    #for idx in failing_indices:
    #    print(idx, env.unwrapped.boulder.cfg.spawn.usd_list[idx], env.unwrapped.boulder_measurements.scales[idx], env_ids_failing[idx])
    #print(" Success/ Fail ratio of env:")
    #for i in range(args_cli.num_envs):
    #    print(i, env_ids_success[i].item(), "/ ", env_ids_failing[i].item())
    import matplotlib.pyplot as plt
    import numpy as np
    # Plot the histogram
    plt.figure(figsize=(8, 6))
    plt.bar(np.arange(args_cli.num_envs), env_ids_failing.cpu().numpy(), edgecolor="black", color="blue")
    plt.title("Histogram of Failing Environments")
    plt.xlabel("Environment ID")
    plt.ylabel("Frequency")
    plt.xticks(range(args_cli.num_envs))  # Ensure all env_ids are shown on x-axis
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.show()
    # close the simulator
    env.close()



if __name__ == "__main__":
    # run the main function
    main()
    # close sim app
    simulation_app.close()
