import torch

def sample_rock_points_idx(ray_hits,  indices, target_mesh_id, num_samples):
    """
    Samples 'num_samples' points from ray_hits for each environment.
    If an environment has fewer valid (non-inf) hits than 'num_samples', 
    the extra points will be invalid and thus set to zero.

    Args:
        ray_hits: Tensor of shape (B, T, 3)
                  B = number of environments
                  T = max number of rays per environment
                  3 = (x, y, z)
        num_samples: how many points to sample per environment

    Returns:
        A tensor of shape (B, num_samples, 3).
        If valid points are fewer than num_samples in a given environment, 
        the extra entries are set to zero.
    """
    B, T, _ = ray_hits.shape

    # 1. Identify valid hits (not inf in any component) => shape (B, T)
    valid_mask = ~torch.isinf(ray_hits).any(dim=-1)

    mesh_mask = (indices == target_mesh_id)

    combined_mask = valid_mask & mesh_mask

    # 2. Generate random scores in [0, 1) for each point
    random_scores = torch.rand(B, T, device=ray_hits.device)

    # 3. Mark invalid points with -inf so they rank last
    random_scores[~combined_mask] = float('-inf')

    # 4. Pick top 'num_samples' from each batch along dimension=1
    #    If valid points < num_samples, the remaining will be -inf
    _, topk_indices = random_scores.topk(num_samples, dim=1, largest=True)

    # (Optional) sort selected indices so you preserve original ordering
    # If you don't need ordering, you can skip this step.
    sorted_indices, _ = torch.sort(topk_indices, dim=1)

    # 5. Gather (x, y, z) => shape (B, num_samples, 3)
    sorted_indices_expand = sorted_indices.unsqueeze(-1).expand(-1, -1, 3)
    sampled_points = torch.gather(ray_hits, dim=1, index=sorted_indices_expand)

    # 6. Replace any inf values in sampled_points with 0
    #    (occurs if valid_count < num_samples in that environment)
    inf_mask = torch.isinf(sampled_points)
    sampled_points[inf_mask] = 0

    # 8. Count how many invalid data we replaced
    num_invalid = inf_mask.sum().item()  # total count of inf values replaced

    #print(f"Number of invalid data replaced with 0: {num_invalid}")

    return sampled_points

def sample_rock_points(ray_hits, num_samples):
    """
    Samples 'num_samples' points from ray_hits for each environment.
    If an environment has fewer valid (non-inf) hits than 'num_samples', 
    the extra points will be invalid and thus set to zero.

    Args:
        ray_hits: Tensor of shape (B, T, 3)
                  B = number of environments
                  T = max number of rays per environment
                  3 = (x, y, z)
        num_samples: how many points to sample per environment

    Returns:
        A tensor of shape (B, num_samples, 3).
        If valid points are fewer than num_samples in a given environment, 
        the extra entries are set to zero.
    """
    B, T, _ = ray_hits.shape

    # 1. Identify valid hits (not inf in any component) => shape (B, T)
    valid_mask = ~torch.isinf(ray_hits).any(dim=-1)

    # 2. Generate random scores in [0, 1) for each point
    random_scores = torch.rand(B, T, device=ray_hits.device)

    # 3. Mark invalid points with -inf so they rank last
    random_scores[~valid_mask] = float('-inf')

    # 4. Pick top 'num_samples' from each batch along dimension=1
    #    If valid points < num_samples, the remaining will be -inf
    _, topk_indices = random_scores.topk(num_samples, dim=1, largest=True)

    # (Optional) sort selected indices so you preserve original ordering
    # If you don't need ordering, you can skip this step.
    sorted_indices, _ = torch.sort(topk_indices, dim=1)

    # 5. Gather (x, y, z) => shape (B, num_samples, 3)
    sorted_indices_expand = sorted_indices.unsqueeze(-1).expand(-1, -1, 3)
    sampled_points = torch.gather(ray_hits, dim=1, index=sorted_indices_expand)

    # 6. Replace any inf values in sampled_points with 0
    #    (occurs if valid_count < num_samples in that environment)
    inf_mask = torch.isinf(sampled_points)
    sampled_points[inf_mask] = 0

    # 8. Count how many invalid data we replaced
    num_invalid = inf_mask.sum().item()  # total count of inf values replaced

    #print(f"Number of invalid data replaced with 0: {num_invalid}")

    return sampled_points

def sort_ray_hits_by_x(ray_hits_sampled):
    """
    Sorts ray_hits_sampled by descending x-coordinate (the first column)
    for each environment (batch) independently.

    Args:
        ray_hits_sampled (torch.Tensor): shape (B, N, 3)
            B = number of environments
            N = number of points per environment
            3 = (x, y, z)

    Returns:
        sorted_hits (torch.Tensor): shape (B, N, 3),
            sorted by x descending for each environment
    """
    # Extract x-coordinates: shape (B, N)
    x_coords = ray_hits_sampled[..., 0]

    # Sort indices along dimension=1 (the point dimension) in descending order
    _, sorted_indices = x_coords.sort(dim=1, descending=True)

    # Gather the full [x, y, z] rows using sorted indices
    # sorted_indices has shape (B, N), so expand it for gathering
    sorted_hits = torch.gather(
        ray_hits_sampled,
        dim=1,
        index=sorted_indices.unsqueeze(-1).expand(-1, -1, ray_hits_sampled.size(-1))
    )

    return sorted_hits

def sort_ray_hits_by_y(ray_hits_sampled):
    """
    Sorts ray_hits_sampled by descending y-coordinate (the second column)
    for each environment (batch) independently.

    Args:
        ray_hits_sampled (torch.Tensor): shape (B, N, 3)
            B = number of environments
            N = number of points per environment
            3 = (x, y, z)

    Returns:
        sorted_hits (torch.Tensor): shape (B, N, 3),
            sorted by y descending for each environment
    """
    # Extract y-coordinates: shape (B, N)
    y_coords = ray_hits_sampled[..., 1]

    # Sort indices along dimension=1 (the point dimension) in descending order
    _, sorted_indices = y_coords.sort(dim=1, descending=True)

    # Gather the full [x, y, z] rows using sorted indices
    # sorted_indices has shape (B, N), so expand it for gathering
    sorted_hits = torch.gather(
        ray_hits_sampled,
        dim=1,
        index=sorted_indices.unsqueeze(-1).expand(-1, -1, ray_hits_sampled.size(-1))
    )

    return sorted_hits
