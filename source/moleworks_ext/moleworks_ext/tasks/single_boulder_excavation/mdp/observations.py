# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
import torch
from typing import TYPE_CHECKING

from isaaclab.assets import Articulation, RigidObject
from isaaclab.envs import ManagerBasedEnv
from isaaclab.managers import SceneEntityCfg
from .utils.ray_cast_utils import sample_rock_points, sort_ray_hits_by_x, sample_rock_points_idx
import isaacsim.core.utils.torch as torch_utils


# maybe just
def boulder_ellipsoid_axes_w(env: ManagerBasedEnv) -> torch.Tensor:
    # dim 9
    return env.boulder_measurements.ellipsoid_axes_w


def boulder_com_pos_w(env: ManagerBasedEnv) -> torch.Tensor:
    # dim 3
    return env.boulder_measurements.root_pos_w


# Maybe
#
def boulder_pos_b(env: ManagerBasedEnv) -> torch.Tensor:
    # dim 3
    return env.boulder_measurements.pos_b


def raycast_boulder_idx(env, num_points) -> torch.Tensor:
    # Query the ray hits from the raycaster against the boulder
    ray_hits = env.scene['raycaster'].data.ray_hits_w
    indices = env.unwrapped.scene['raycaster'].data.mesh_id # shape (num_envs, num_rays)
    # Sample the ray hits, choose 15 points randomly
    ray_hits_sampled = sample_rock_points_idx(ray_hits, indices, 0, num_points)
    # Sort the ray hits by x
    ray_hits_sampled_sorted = sort_ray_hits_by_x(ray_hits_sampled)
    # Center the ray hits by the boulder origin
    ray_hits_sampled_sorted_centered = ray_hits_sampled_sorted-env.unwrapped.scene.env_origins.unsqueeze(1)
    # Standarize the data
    mean_tensor = torch.tensor([5, 0.0, 0.0], device=env.device)
    std_tensor = torch.tensor([2.5, 1.0, 1.0], device=env.device)
    ray_hits_sampled_sorted_centered_standarized = (ray_hits_sampled_sorted_centered - mean_tensor) / std_tensor
    return ray_hits_sampled_sorted_centered_standarized.reshape(env.num_envs, -1)

def raycast_boulder_idx_base(env, num_points) -> torch.Tensor:
    # Query the ray hits from the raycaster against the boulder
    ray_hits = env.scene['raycaster'].data.ray_hits_w
    indices = env.unwrapped.scene['raycaster'].data.mesh_id # shape (num_envs, num_rays)
    # Sample the ray hits, choose 15 points randomly
    ray_hits_sampled = sample_rock_points_idx(ray_hits, indices, 0, num_points)
    
    # transform to base frame
    rel_points_flat = (ray_hits_sampled - env.m545_measurements.root_pos_w[:, None, :]).reshape(env.num_envs * num_points, 3)
    expanded_quat_flat = (
        env.unwrapped.m545_measurements.root_quat_w.unsqueeze(1).expand(-1, num_points, -1).reshape(env.num_envs * num_points, 4)
    )
    rotated_flat = torch_utils.quat_rotate_inverse(expanded_quat_flat, rel_points_flat)
    ray_hits_sampled_base = rotated_flat.view(env.num_envs, num_points, 3)
    

    # Sort the ray hits by x
    ray_hits_sampled_sorted = sort_ray_hits_by_x(ray_hits_sampled_base)
    # Center the ray hits by the boulder origin
    ray_hits_sampled_sorted_centered = ray_hits_sampled_sorted#-env.unwrapped.scene.env_origins.unsqueeze(1)
    # Standarize the data
    mean_tensor = torch.tensor([5, 0.0, 0.0], device=env.device)
    std_tensor = torch.tensor([2.5, 1.0, 1.0], device=env.device)
    ray_hits_sampled_sorted_centered_standarized = (ray_hits_sampled_sorted_centered - mean_tensor) / std_tensor
    return ray_hits_sampled_sorted_centered_standarized.reshape(env.num_envs, -1)



    
def raycast_boulder(env, num_points) -> torch.Tensor:
    # Query the ray hits from the raycaster against the boulder
    ray_hits = env.scene['raycaster'].data.ray_hits_w
    # Sample the ray hits, choose 15 points randomly
    ray_hits_sampled = sample_rock_points(ray_hits, num_points)
    # Sort the ray hits by x
    ray_hits_sampled_sorted = sort_ray_hits_by_x(ray_hits_sampled)
    # Center the ray hits by the boulder origin
    ray_hits_sampled_sorted_centered = ray_hits_sampled_sorted-env.unwrapped.scene.env_origins.unsqueeze(1)
    # Standarize the data
    mean_tensor = torch.tensor([5, 0.0, 0.0], device=env.device)
    std_tensor = torch.tensor([2.5, 1.0, 1.0], device=env.device)
    ray_hits_sampled_sorted_centered_standarized = (ray_hits_sampled_sorted_centered - mean_tensor) / std_tensor

    return ray_hits_sampled_sorted_centered_standarized.reshape(env.num_envs, -1)


def raycast_boulder_base(env, num_points) -> torch.Tensor:
    # Query the ray hits from the raycaster against the boulder
    ray_hits = env.scene['raycaster'].data.ray_hits_w
    # Sample the ray hits, choose 15 points randomly
    ray_hits_sampled = sample_rock_points(ray_hits, num_points)

    # transform to base frame
    rel_points_flat = (ray_hits_sampled - env.m545_measurements.root_pos_w[:, None, :]).reshape(env.num_envs * num_points, 3)
    expanded_quat_flat = (
        env.unwrapped.m545_measurements.root_quat_w.unsqueeze(1).expand(-1, num_points, -1).reshape(env.num_envs * num_points, 4)
    )
    rotated_flat = torch_utils.quat_rotate_inverse(expanded_quat_flat, rel_points_flat)
    ray_hits_sampled_base = rotated_flat.view(env.num_envs, num_points, 3)
    
    # Sort the ray hits by x
    ray_hits_sampled_sorted = sort_ray_hits_by_x(ray_hits_sampled_base)
    # Center the ray hits by the boulder origin
    ray_hits_sampled_sorted_centered = ray_hits_sampled_sorted#-env.unwrapped.scene.env_origins.unsqueeze(1)
    # tansform ray_hits_sampled_sorted_centeredray to base frame

    # Standarize the data
    mean_tensor = torch.tensor([5, 0.0, 0.0], device=env.device)
    std_tensor = torch.tensor([2.5, 1.0, 1.0], device=env.device)
    ray_hits_sampled_sorted_centered_standarized = (ray_hits_sampled_sorted_centered - mean_tensor) / std_tensor

    return ray_hits_sampled_sorted_centered_standarized.reshape(env.num_envs, -1)


def ray_cast_boulder_idx_w_noise_base(env, target_mesh_id=0, num_samples=15, noise_x_std=1.0, noise_y_std=0.1, noise_z_std=0.15, edge_percentage=0.15, std=[1.0,1.0,1.0]):
    """
    Vectorized sampling of points from ray_hits for each environment.
    If an environment has fewer valid (non-inf) hits than num_samples,
    the extra points are set to ([5, 0.0, 0.0] + env.unwrapped.scene.env_origins) for that environment.

    Then, using all valid points (from ray_hits), the overall minimum and maximum y values 
    are computed per environment. These define the overall y-range and, with a configurable 
    fraction (edge_percentage), the lower and upper edge zones. Among the randomly chosen 
    samples, those whose y values fall in these edge zones will get noise added in x, y, and z.
    
    For these edge points:
      - x receives a big positive noise (std = noise_x_std).
      - y receives noise (std = noise_y_std) that is forced negative if the point is near y_min 
        and positive if near y_max.
      - z is re-centered around the soil height for that environment with a little noise (std = noise_z_std).
    
    Args:
        ray_hits: Tensor of shape (B, T, 3)
                  where B = number of environments,
                        T = max number of rays per environment,
                        3 = (x, y, z).
        indices: Tensor of shape (B, T) containing mesh ids for each ray hit.
        target_mesh_id: The mesh id to filter for.
        num_samples: How many points to sample per environment.
        env: An environment object that provides:
             - env.unwrapped.scene.env_origins: Tensor of shape (B, 3).
             - env.soil_height_futures: Tensor (B, N), used to compute soil height.
             - env.m545_measurements.root_pos_w: Tensor of shape (B, ?), where index 2 is used.
        noise_x_std: Standard deviation for x noise (big, positive noise).
        noise_y_std: Standard deviation for y noise.
        noise_z_std: Standard deviation for z noise (small noise).
        edge_percentage: Fraction of the overall y-range (from all valid points) at the lower 
                         and upper extremes to be considered edges.
    
    Returns:
        A tensor of shape (B, num_samples, 3) with the modifications described above.
    """
    # Gather data
    ray_hits = env.unwrapped.scene['raycaster'].data.ray_hits_w
    indices = env.unwrapped.scene['raycaster'].data.mesh_id

    B, T, _ = ray_hits.shape

    # 1. Identify valid hits (non-inf) and points matching target_mesh_id.
    valid_mask = ~torch.isinf(ray_hits).any(dim=-1)  # (B, T)
    mesh_mask = (indices == target_mesh_id)           # (B, T)
    combined_mask = valid_mask & mesh_mask             # (B, T)

    # 2. Generate random scores; set scores for invalid points to -inf.
    random_scores = torch.rand(B, T, device=ray_hits.device)
    random_scores[~combined_mask] = float('-inf')

    # 3. Pick top 'num_samples' indices for each environment.
    _, topk_indices = random_scores.topk(num_samples, dim=1, largest=True)
    sorted_indices, _ = torch.sort(topk_indices, dim=1)

    # 4. Gather (x, y, z) points using the sorted indices.
    sorted_indices_expand = sorted_indices.unsqueeze(-1).expand(-1, -1, 3)
    sampled_points = torch.gather(ray_hits, dim=1, index=sorted_indices_expand)

    # 5. Also gather the corresponding validity of these chosen points.
    valid_sampled = torch.gather(combined_mask, dim=1, index=sorted_indices)

    # 6. Replace invalid sampled points with the default value:
    #    ([5, 0.0, 0.0] + env.unwrapped.scene.env_origins) per environment.
    default_offset = torch.tensor([0, 0.0, 0.0], device=ray_hits.device, dtype=ray_hits.dtype)
    default_value = default_offset.view(1, 1, 3) + env.unwrapped.scene.env_origins.view(B, 1, 3)
    sampled_points = torch.where(valid_sampled.unsqueeze(-1), sampled_points, default_value)

    # 7. Compute overall y statistics from all valid points (from ray_hits).
    y_all = ray_hits[..., 1]  # (B, T)
    inf_tensor = torch.tensor(float('inf'), device=ray_hits.device, dtype=ray_hits.dtype)
    ninf_tensor = torch.tensor(float('-inf'), device=ray_hits.device, dtype=ray_hits.dtype)
    y_min_all = torch.where(combined_mask, y_all, inf_tensor).min(dim=1)[0]  # (B,)
    y_max_all = torch.where(combined_mask, y_all, ninf_tensor).max(dim=1)[0]  # (B,)
    has_valid_all = combined_mask.any(dim=1)  # (B,)
    y_min_all = torch.where(has_valid_all, y_min_all, torch.zeros_like(y_min_all))
    y_max_all = torch.where(has_valid_all, y_max_all, torch.zeros_like(y_max_all))
    y_range_all = y_max_all - y_min_all  # (B,)

    # 8. Establish overall y thresholds based on edge_percentage.
    lower_threshold = y_min_all + edge_percentage * y_range_all  # (B,)
    upper_threshold = y_max_all - edge_percentage * y_range_all  # (B,)
    lower_threshold_exp = lower_threshold.unsqueeze(1)  # (B, 1)
    upper_threshold_exp = upper_threshold.unsqueeze(1)  # (B, 1)

    # Now, determine which of the randomly chosen samples are in the edge zones.
    candidate_edge_mask = valid_sampled & ((sampled_points[..., 1] <= lower_threshold_exp) |
                                             (sampled_points[..., 1] >= upper_threshold_exp))
    # For environments with zero y-range (but with valid points), treat all valid ones as edge.
    range_zero = (y_range_all.abs() < 1e-6) & has_valid_all  # (B,)
    edge_mask = torch.where(range_zero.unsqueeze(1), valid_sampled, candidate_edge_mask)

    # 9. Compute soil height per environment.
    world_heights = env.soil_height_futures  # e.g. (B, N)
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_heights.size(1))
    height = world_heights - root_pos_w_expanded
    soil_height_per_env = height.mean(dim=-1)  # (B,)

    # 10. Generate noise for edge points.
    # For x: big positive noise.
    noise_x = torch.abs(torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype)) * noise_x_std

    # For y: noise with sign determined by proximity to overall y_min or y_max.
    noise_y = torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype) * noise_y_std
    lower_edge_mask = edge_mask & (sampled_points[..., 1] <= lower_threshold_exp)
    upper_edge_mask = edge_mask & (sampled_points[..., 1] >= upper_threshold_exp)
    noise_y = torch.where(lower_edge_mask, -torch.abs(noise_y), noise_y)
    noise_y = torch.where(upper_edge_mask,  torch.abs(noise_y), noise_y)

    # For z: little noise, re-centered at the soil height.
    noise_z = torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype) * noise_z_std

    # 11. Apply noise to the edge points.
    sampled_points[..., 0] = sampled_points[..., 0] + noise_x * edge_mask.type_as(noise_x)
    sampled_points[..., 1] = sampled_points[..., 1] + noise_y * edge_mask.type_as(noise_y)
    soil_height_per_env_expanded = soil_height_per_env.unsqueeze(1)  # (B, 1)
    sampled_points[..., 2] = torch.where(edge_mask,
                                          soil_height_per_env_expanded + noise_z,
                                          sampled_points[..., 2])

    # transform points to base
    # transform to base frame
    #print(env.m545_measurements.root_pos_map)
    rel_points_flat = (sampled_points - env.m545_measurements.root_pos_map[:, None, :]).reshape(env.num_envs * num_samples, 3)
    expanded_quat_flat = (
        env.unwrapped.m545_measurements.root_quat_w.unsqueeze(1).expand(-1, num_samples, -1).reshape(env.num_envs * num_samples, 4)
    )
    rotated_flat = torch_utils.quat_rotate_inverse(expanded_quat_flat, rel_points_flat)
    ray_hits_sampled_base = rotated_flat.view(env.num_envs, num_samples, 3)

    # sort alongg x
    std_tensor = torch.tensor(std, device=env.device)
    ray_hits_sampled_base_sorted = sort_ray_hits_by_x(ray_hits_sampled_base)/std_tensor

    return ray_hits_sampled_base_sorted.reshape(env.num_envs, -1)





def ray_cast_boulder_idx_w_noise_base(env, target_mesh_id=0, num_samples=15, noise_x_std=1.0, noise_y_std=0.1, noise_z_std=0.15, edge_percentage=0.15, std=[1.0,1.0,1.0]):
    """
    Vectorized sampling of points from ray_hits for each environment.
    If an environment has fewer valid (non-inf) hits than num_samples,
    the extra points are set to ([5, 0.0, 0.0] + env.unwrapped.scene.env_origins) for that environment.

    Then, using all valid points (from ray_hits), the overall minimum and maximum y values 
    are computed per environment. These define the overall y-range and, with a configurable 
    fraction (edge_percentage), the lower and upper edge zones. Among the randomly chosen 
    samples, those whose y values fall in these edge zones will get noise added in x, y, and z.
    
    For these edge points:
      - x receives a big positive noise (std = noise_x_std).
      - y receives noise (std = noise_y_std) that is forced negative if the point is near y_min 
        and positive if near y_max.
      - z is re-centered around the soil height for that environment with a little noise (std = noise_z_std).
    
    Args:
        ray_hits: Tensor of shape (B, T, 3)
                  where B = number of environments,
                        T = max number of rays per environment,
                        3 = (x, y, z).
        indices: Tensor of shape (B, T) containing mesh ids for each ray hit.
        target_mesh_id: The mesh id to filter for.
        num_samples: How many points to sample per environment.
        env: An environment object that provides:
             - env.unwrapped.scene.env_origins: Tensor of shape (B, 3).
             - env.soil_height_futures: Tensor (B, N), used to compute soil height.
             - env.m545_measurements.root_pos_w: Tensor of shape (B, ?), where index 2 is used.
        noise_x_std: Standard deviation for x noise (big, positive noise).
        noise_y_std: Standard deviation for y noise.
        noise_z_std: Standard deviation for z noise (small noise).
        edge_percentage: Fraction of the overall y-range (from all valid points) at the lower 
                         and upper extremes to be considered edges.
    
    Returns:
        A tensor of shape (B, num_samples, 3) with the modifications described above.
    """
    # Gather data
    ray_hits = env.unwrapped.scene['raycaster'].data.ray_hits_w
    indices = env.unwrapped.scene['raycaster'].data.mesh_id

    B, T, _ = ray_hits.shape

    # 1. Identify valid hits (non-inf) and points matching target_mesh_id.
    valid_mask = ~torch.isinf(ray_hits).any(dim=-1)  # (B, T)
    mesh_mask = (indices == target_mesh_id)           # (B, T)
    combined_mask = valid_mask & mesh_mask             # (B, T)

    # 2. Generate random scores; set scores for invalid points to -inf.
    random_scores = torch.rand(B, T, device=ray_hits.device)
    random_scores[~combined_mask] = float('-inf')

    # 3. Pick top 'num_samples' indices for each environment.
    _, topk_indices = random_scores.topk(num_samples, dim=1, largest=True)
    sorted_indices, _ = torch.sort(topk_indices, dim=1)

    # 4. Gather (x, y, z) points using the sorted indices.
    sorted_indices_expand = sorted_indices.unsqueeze(-1).expand(-1, -1, 3)
    sampled_points = torch.gather(ray_hits, dim=1, index=sorted_indices_expand)

    # 5. Also gather the corresponding validity of these chosen points.
    valid_sampled = torch.gather(combined_mask, dim=1, index=sorted_indices)

    # 6. Replace invalid sampled points with the default value:
    #    ([5, 0.0, 0.0] + env.unwrapped.scene.env_origins) per environment.
    default_offset = torch.tensor([0, 0.0, 0.0], device=ray_hits.device, dtype=ray_hits.dtype)
    default_value = default_offset.view(1, 1, 3) + env.unwrapped.scene.env_origins.view(B, 1, 3)
    sampled_points = torch.where(valid_sampled.unsqueeze(-1), sampled_points, default_value)

    # 7. Compute overall y statistics from all valid points (from ray_hits).
    y_all = ray_hits[..., 1]  # (B, T)
    inf_tensor = torch.tensor(float('inf'), device=ray_hits.device, dtype=ray_hits.dtype)
    ninf_tensor = torch.tensor(float('-inf'), device=ray_hits.device, dtype=ray_hits.dtype)
    y_min_all = torch.where(combined_mask, y_all, inf_tensor).min(dim=1)[0]  # (B,)
    y_max_all = torch.where(combined_mask, y_all, ninf_tensor).max(dim=1)[0]  # (B,)
    has_valid_all = combined_mask.any(dim=1)  # (B,)
    y_min_all = torch.where(has_valid_all, y_min_all, torch.zeros_like(y_min_all))
    y_max_all = torch.where(has_valid_all, y_max_all, torch.zeros_like(y_max_all))
    y_range_all = y_max_all - y_min_all  # (B,)

    # 8. Establish overall y thresholds based on edge_percentage.
    lower_threshold = y_min_all + edge_percentage * y_range_all  # (B,)
    upper_threshold = y_max_all - edge_percentage * y_range_all  # (B,)
    lower_threshold_exp = lower_threshold.unsqueeze(1)  # (B, 1)
    upper_threshold_exp = upper_threshold.unsqueeze(1)  # (B, 1)

    # Now, determine which of the randomly chosen samples are in the edge zones.
    candidate_edge_mask = valid_sampled & ((sampled_points[..., 1] <= lower_threshold_exp) |
                                             (sampled_points[..., 1] >= upper_threshold_exp))
    # For environments with zero y-range (but with valid points), treat all valid ones as edge.
    range_zero = (y_range_all.abs() < 1e-6) & has_valid_all  # (B,)
    edge_mask = torch.where(range_zero.unsqueeze(1), valid_sampled, candidate_edge_mask)

    # 9. Compute soil height per environment.
    world_heights = env.soil_height_futures  # e.g. (B, N)
    root_pos_w_expanded = env.m545_measurements.root_pos_w[:, 2].unsqueeze(1).expand(-1, world_heights.size(1))
    height = world_heights - root_pos_w_expanded
    soil_height_per_env = height.mean(dim=-1)  # (B,)

    # 10. Generate noise for edge points.
    # For x: big positive noise.
    noise_x = torch.abs(torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype)) * noise_x_std

    # For y: noise with sign determined by proximity to overall y_min or y_max.
    noise_y = torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype) * noise_y_std
    lower_edge_mask = edge_mask & (sampled_points[..., 1] <= lower_threshold_exp)
    upper_edge_mask = edge_mask & (sampled_points[..., 1] >= upper_threshold_exp)
    noise_y = torch.where(lower_edge_mask, -torch.abs(noise_y), noise_y)
    noise_y = torch.where(upper_edge_mask,  torch.abs(noise_y), noise_y)

    # For z: little noise, re-centered at the soil height.
    noise_z = torch.randn(B, num_samples, device=ray_hits.device, dtype=ray_hits.dtype) * noise_z_std

    # 11. Apply noise to the edge points.
    sampled_points[..., 0] = sampled_points[..., 0] + noise_x * edge_mask.type_as(noise_x)
    sampled_points[..., 1] = sampled_points[..., 1] + noise_y * edge_mask.type_as(noise_y)
    soil_height_per_env_expanded = soil_height_per_env.unsqueeze(1)  # (B, 1)
    sampled_points[..., 2] = torch.where(edge_mask,
                                          soil_height_per_env_expanded + noise_z,
                                          sampled_points[..., 2])

    # transform points to base
    # transform to base frame
    #print(env.m545_measurements.root_pos_map)
    rel_points_flat = (sampled_points - env.m545_measurements.root_pos_map[:, None, :]).reshape(env.num_envs * num_samples, 3)
    expanded_quat_flat = (
        env.unwrapped.m545_measurements.root_quat_w.unsqueeze(1).expand(-1, num_samples, -1).reshape(env.num_envs * num_samples, 4)
    )
    rotated_flat = torch_utils.quat_rotate_inverse(expanded_quat_flat, rel_points_flat)
    ray_hits_sampled_base = rotated_flat.view(env.num_envs, num_samples, 3)

    # sort alongg x
    std_tensor = torch.tensor(std, device=env.device)
    ray_hits_sampled_base_sorted = sort_ray_hits_by_x(ray_hits_sampled_base)/std_tensor

    return ray_hits_sampled_base_sorted.reshape(env.num_envs, -1)



def raycast_boulder_xz_sorted_y(env, num_points) -> torch.Tensor:
    """
    Query ray hits from the raycaster, sample points, sort by y, and standardize (x, z) coordinates.

    Args:
        env: The environment containing the raycaster and scene information.
        num_points: Number of points to sample.

    Returns:
        torch.Tensor: A reshaped tensor containing standardized (x, z) sorted by y.
    """
    # Query the ray hits from the raycaster against the boulder
    ray_hits = env.scene['raycaster'].data.ray_hits_w

    # Sample the ray hits, choose `num_points` randomly
    ray_hits_sampled = sample_rock_points(ray_hits, num_points)

    # Sort the ray hits by y-coordinate
    ray_hits_sampled_sorted = sort_ray_hits_by_y(ray_hits_sampled)

    # Extract (x, z) coordinates
    ray_hits_sampled_sorted_xz = ray_hits_sampled_sorted[..., [0, 2]]  # Select x and z

    # Center the ray hits by the boulder origin
    boulder_origin_centered = env.unwrapped.scene.env_origins.unsqueeze(1)
    ray_hits_sampled_sorted_xz_centered = ray_hits_sampled_sorted_xz - boulder_origin_centered[..., [0, 2]]

    # Standardize the data
    mean_tensor = torch.tensor([5.0, 0.0], device=env.device)  # Mean for x and z
    std_tensor = torch.tensor([2.5, 1.0], device=env.device)  # Std for x and z
    ray_hits_sampled_sorted_xz_standardized = (ray_hits_sampled_sorted_xz_centered - mean_tensor) / std_tensor

    # Reshape the tensor to (num_envs, num_points * 2) for the policy
    return ray_hits_sampled_sorted_xz_standardized.reshape(env.num_envs, -1)
