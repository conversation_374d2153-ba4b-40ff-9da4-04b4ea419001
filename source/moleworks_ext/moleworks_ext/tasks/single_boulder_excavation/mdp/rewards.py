# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause
import torch
from typing import TYPE_CHECKING

import isaacsim.core.utils.torch as torch_utils

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import SceneEntityCfg

if TYPE_CHECKING:
    from moleworks_ext.tasks.excavation.excavation_env_3d import ExcavationEnv

# helpers, TODO: code them up
# def is_behind(env):
#    return True
# def is_same_height(env):
#    return True
# def is_no_boulder_in_shovel(env):
#    return True
# def is_not_full_curled(env):
#    return True
# def is_partial_curl(env):
#    return True
# def is_not_close(env):
#    return True
# def is_boulder_in_shovel(env):
#    return
#    return True
# def is_above_trough(env):
#    return True
# def is_close(env):
#    return True
# def is_upwards_movement(env):
#    return True
# def is_in_contact(env):
#    return True


# Action rate


# Distance Bucket Boulder
def reward_distance_bucket_boulder(env):
    # Reward if the bucket is close enough to the shovel
    distances = torch.norm(env.boulder_measurements.root_pos_w - env.m545_measurements.bucket_pos_w, dim=-1)
    return torch.where(
        distances < 1, torch.ones(env.num_envs, device=env.device), torch.zeros(env.num_envs, device=env.device)
    )


# Boulder height increases
def reward_boulder_height_increase(env, termination_height, termination_height_above_soil):
    # Reward if the height of the boulder increases
    boulder_pos_diff = env.boulder_measurements.root_pos_w[:, 2] - env.boulder_measurements.prev_root_pos_w[:, 2]
    boulder_height_increase = boulder_pos_diff > 0
    boulder_in_shovel = env.boulder_measurements.in_shovel > 0
    curled_enough = env.m545_measurements.bucket_ang_gac < -0.3
    # The higher the return the closest to the termination height, p(x)=ℯ^(-(abs(x-termination_height))^(2))
    desired_height = torch.maximum(torch.tensor(termination_height, device =env.device), termination_height_above_soil+env.platform_heights)
    reward = torch.exp(-(torch.abs(env.m545_measurements.bucket_pos_w[:, 2]- desired_height)) ** 2)
    return torch.where(
        boulder_height_increase & boulder_in_shovel,
        reward,
        torch.zeros(env.num_envs, device=env.device),
    )
def reward_boulder_height_increase_flat(env, termination_height):
    # Reward if the height of the boulder increases
    boulder_pos_diff = env.boulder_measurements.root_pos_w[:, 2] - env.boulder_measurements.prev_root_pos_w[:, 2]
    boulder_height_increase = boulder_pos_diff > 0
    boulder_in_shovel = env.boulder_measurements.in_shovel > 0
    curled_enough = env.m545_measurements.bucket_ang_gac < -0.3
    # The higher the return the closest to the termination height, p(x)=ℯ^(-(abs(x-termination_height))^(2))
    desired_height = termination_height
    reward = torch.exp(-(torch.abs(env.m545_measurements.bucket_pos_w[:, 2]- desired_height)) ** 2)
    return torch.where(
        boulder_height_increase & boulder_in_shovel,
        reward,
        torch.zeros(env.num_envs, device=env.device),
    )
    #boulder_pos_diff = env.boulder_measurements.root_pos_w[:, 2] - env.boulder_measurements.prev_root_pos_w[:, 2]
    #boulder_pos_diff_bounded = torch.clip(boulder_pos_diff, -0.5, 10)
#
    #return torch.where(
    #    boulder_pos_diff_bounded > 0,
    #    torch.ones(env.num_envs, device=env.device),
    #    torch.zeros(env.num_envs, device=env.device),
    #)

def reward_curled_enough(env):
    boulder_in_shovel = env.boulder_measurements.in_shovel > 0
    curled_enough = env.m545_measurements.bucket_ang_gac < -0.3
    return torch.where(
        boulder_in_shovel & curled_enough,
        torch.ones(env.num_envs, device=env.device),
        torch.zeros(env.num_envs, device=env.device),
    )

def reward_in_soil(env):
    in_soil = (env.soil.get_bucket_depth() > 0.00).squeeze()
    return torch.where(
        in_soil,
        torch.ones(env.num_envs, device=env.device),
        torch.zeros(env.num_envs, device=env.device),
    )


# Boulder in shovel
def reward_boulder_in_shovel(env):
    # Reward if boulder in shovel and hight enough
    curled_enough = env.m545_measurements.bucket_ang_gac < -0.3
    boulder_in_shovel = env.boulder_measurements.in_shovel > 0
    return torch.where(
        boulder_in_shovel,
        torch.ones(env.num_envs, device=env.device),
        torch.zeros(env.num_envs, device=env.device),
    )

def reward_depth(env):
    return torch.where(
        (env.soil.get_bucket_depth() > 0).squeeze(),
        torch.ones(env.num_envs, device=env.device),
        torch.zeros(env.num_envs, device=env.device),
    )
    

def reward_pitch_in_front(env):
    j_pitch_pos_x = env.m545_measurements.j_pitch_pos[:, 0]

    x_min_boulder = env.boulder_measurements.x_min
    return torch.where(
        j_pitch_pos_x > x_min_boulder,
        torch.ones(env.num_envs, device=env.device),
        torch.zeros(env.num_envs, device=env.device),
    )


def penalty_bucket_vel(env, max_bucket_vel):
    too_fast = env.m545_measurements.bucket_vel_norm > max_bucket_vel
    reward = torch.exp(0.5*(torch.abs(env.m545_measurements.bucket_vel_norm- max_bucket_vel)))
    #reward = torch.abs(env.m545_measurements.bucket_vel_norm- max_bucket_vel)
    return torch.where(
        too_fast,
        reward,
        torch.zeros(env.num_envs, device=env.device),
    )



def termination_reward_pos(env) -> torch.Tensor:

    pos_prefix = "positive_termination_"
    pos_term_buf = torch.zeros(env.num_envs, device=env.device, dtype=torch.bool)
    for term_name in env.termination_manager._term_names:
        value = env.termination_manager.get_term(term_name)
        if value.any():
            if term_name.startswith(pos_prefix):  # TODO: implement this
                pos_term_buf |= value

    pos_term_rew = pos_term_buf * env.cfg.terminations_boulder.pos_term_rew

    return pos_term_rew

def termination_reward_neg(env) -> torch.Tensor:

    neg_prefix = "negative_termination_"
    neg_term_buf = torch.zeros(env.num_envs, device=env.device, dtype=torch.bool)
    for term_name in env.termination_manager._term_names:
        value = env.termination_manager.get_term(term_name)
        if value.any():

            if term_name.startswith(neg_prefix):  # TODO: implement this
                neg_term_buf |= value


    neg_term_rew = neg_term_buf * env.cfg.terminations_boulder.neg_term_rew

    return neg_term_rew


"""
def reward_power(env):
    # Description: Incentivise high power usage when not close and no boulder in shovel
    # not_close = self.not_close
    # boulder_in_shovel = self.boulder_in_shovel
    clipped_power = torch.clip(torch.sum(env.m545_measurements.joint_vel * env.torques, dim=-1), min=0.0)

    return clipped_power

def reward_const(env):
    return torch.ones(env.num_envs, device=env.device)

def reward_bucket_curled(env):
    # Description: Incentivise curling inwards with bucket when boulder in shovel
    boulder_in_shovel = is_boulder_in_shovel(env)
    # give reward while not curled enough
    not_full_curled = is_not_full_curled(env)

    # angle_chang = env.m545_measurements.prev_bucket_ang_gac - env.m545_measurements.bucket_ang_gac

    # print("dof vel of pitch: ", env.m545_measurements.dof_vel[:, 3])

    # tanh_chang= torch.tanh(angle_chang)

    # return torch.where(boulder_in_shovel & not_full_curled, tanh_chang, env.zero_vec)

    tanh_curling = torch.tanh(env.m545_measurements.joint_vel[:, 3])

    return torch.where(boulder_in_shovel & not_full_curled, tanh_curling, env.zero_vec)


def reward_bucket_up(env):
    # Description: Incentivise picking up bucket when boulder in shovel
    boulder_in_shovel = is_boulder_in_shovel(env)

    # distance = env.m545_measurements.bucket_pos_w[:, 2] - (env.trough_asset.offset[:, 2] - env.trough_asset.height)
    distance = env.m545_measurements.j_pitch_pos[:, 2] - env.soil_height_futures[:, 0]

    tanh_dist = torch.tanh(distance)

    return torch.where((boulder_in_shovel), tanh_dist, env.zero_vec)

# def reward_bucket_curled_and_up(self):
#     # Description: Incentivise picking up and curling bucket when close or boulder in shovel
#     close = (
#         env.m545_measurements.bucket_pos_w[:, 0] < env.pullup_dist + env.curr_manager.curr_pullup_band
#     )  # close
#     boulder_in_shovel = self.boulder_in_shovel

#     up_vel = -env.m545_measurements.bucket_vel_w[:, 2]

#     return torch.where(close | boulder_in_shovel, up_vel, env.zero_vec)

def reward_bucket_movement_to_boulder(env):
    # Description: Incentivise moving towards boulder when no boulder in shovel
    no_boulder_in_shovel = is_no_boulder_in_shovel(env)

    distances = []
    for boulder in env.boulder_asset_list:
        distance = boulder.pos_w - env.m545_measurements.bucket_pos_w
        distances.append(distance)

    distances = torch.vstack(distances)
    distances = torch.linalg.norm(distances, dim=-1)

    min_dist = torch.min(distances, dim=0).values

    tanh_dist = torch.tanh(-min_dist)

    return torch.where((no_boulder_in_shovel), tanh_dist, env.zero_vec)

def reward_boulder_movement_to_excavator(env):
    # Description: Reward boulder movement towards the excavator
    dist_chang = []

    for boulder in env.boulder_asset_list:
        dist_change = boulder.last_pos_w[:, 0] - boulder.pos_w[:, 0]
        dist_chang.append(dist_change)

    dist_chang = torch.vstack(dist_chang)

    sum = torch.sum(dist_chang, dim=0)
    sum /= len(env.boulder_asset_list)

    tanh_dist = torch.tanh(-sum)
    return tanh_dist

def reward_boulder_height_increase(env):
    # Description: Reward boulder height increases
    height_changes = []

    for boulder in env.boulder_asset_list:
        change = boulder.pos_w[:, 2] - boulder.last_pos_w[:, 2]
        height_changes.append(change)

    height_changes = torch.vstack(height_changes)

    # Setting height change to 0 if it not increasing
    height_changes[height_changes < 0] = 0

    # return the sum of the height changes for each environment
    sum = torch.sum(height_changes, dim=0)
    sum /= len(env.boulder_asset_list)
    return sum

def reward_bucket_movement_closer(env):
    # Description: Incentivise moving towards start of trough
    boulder_in_shovel = is_boulder_in_shovel(env)
    in_contact = is_in_contact(env)

    same_height = is_same_height(env)
    not_close = is_not_close(env)

    pos_chang = env.m545_measurements.bucket_pos_w[:,0] -env.m545_measurements.prev_bucket_pos_w[:,0]

    tanh_chang= torch.tanh(-pos_chang)

    return torch.where((boulder_in_shovel & same_height & not_close & in_contact), tanh_chang, env.zero_vec)

def reward_bucket_closer_to_trough(env):
    # Description: Incentivise moving towards bottom of trough
    no_boulder_in_shovel = is_no_boulder_in_shovel(env)
    in_contact = is_in_contact(env)

    same_height = is_same_height(env)
    above_trough = is_above_trough(env)

    pos_chang = env.m545_measurements.bucket_pos_w[:,2] - env.m545_measurements.prev_bucket_pos_w[:,2]

    tanh_chang= torch.tanh(-pos_chang)

    return torch.where((no_boulder_in_shovel & same_height & above_trough & in_contact), tanh_chang, env.zero_vec)

def reward_bucket_tangent_to_trough(env):
    # Description: Incentivise moving towards bottom of trough
    no_boulder_in_shovel = is_no_boulder_in_shovel(env)

    angle_off_tang = torch.abs(env.m545_measurements.bucket_ang_gac - 1.2)
    angle_off_prev_tang = torch.abs(env.m545_measurements.prev_bucket_ang_gac - 1.2)

    angle_chang = angle_off_prev_tang - angle_off_tang

    tanh_chang= torch.tanh(angle_chang)

    return torch.where(no_boulder_in_shovel, tanh_chang, env.zero_vec)

def reward_move_x_axis(env):
    # Description: Reward for moving behind boulder in x-axis
    in_front = torch.where(is_behind(env), env.zero_vec, torch.ones(env.num_envs, device=env.device)).bool()

    no_boulder_in_shovel = is_no_boulder_in_shovel(env)

    distance = env.trough_asset.offset[:,0] - env.m545_measurements.bucket_pos_w[:, 0]

    tanh_dist = - torch.tanh(distance) - 1

    return torch.where((in_front & no_boulder_in_shovel), tanh_dist, env.zero_vec)

def reward_move_z_axis(env):
    # Description: Reward for moving on height level of boulder once behind it
    behind = is_behind(env)
    above = torch.where(is_same_height(env), env.zero_vec, torch.ones(env.num_envs, device=env.device)).bool()

    no_boulder_in_shovel = is_no_boulder_in_shovel(env)

    distance = torch.clip(env.trough_asset.offset[:,2] - env.m545_measurements.bucket_pos_w[:, 2], max=0.0)

    tanh_dist = torch.tanh(distance)

    return torch.where((above & no_boulder_in_shovel & behind), tanh_dist, env.zero_vec)

def reward_boulder_in_shovel(env):
    # Description: Reward boulders in shovel
    partial_curl = is_partial_curl(env)
    upwards_mov = is_upwards_movement(env)

    volume = torch.where(upwards_mov, env.m545_measurements.loaded_volume, -env.m545_measurements.loaded_volume)

    return torch.where(partial_curl, env.zero_vec, volume)

"""
