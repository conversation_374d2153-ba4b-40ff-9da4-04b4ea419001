# Copyright (c) 2022-2023, The ORBIT Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

import numpy as np
import torch
from typing import TYPE_CHECKING

import isaacsim.core.utils.torch as torch_utils

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import EventTermCfg, ManagerTermBase, SceneEntityCfg

from moleworks_ext.common.utils.utils import u_rand

NUM_OBJECTS_PER_ENV = 1
X_LIMIT_NEG = 3.5
X_LIMIT_POS = 7
Y_LIMIT_NEG = -0.2
Y_LIMIT_POS = 0.2


if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedEnv


def boulder_randomization_flat(env: ManagerBasedEnv, env_ids: torch.Tensor):
    z_start = torch.zeros(env.num_envs, device=env.device)
    max_z = torch.zeros(env.num_envs, device=env.device)
    max_x = torch.zeros(env.num_envs, device=env.device)
    min_x = torch.zeros(env.num_envs, device=env.device)
    if len(env_ids) == 0:
        return
    

    # now
    rigid_objects = env.scene["object_0"]
    # randomized_pos = torch.zeros(env.num_envs, NUM_OBJECTS_PER_ENV, 3, device=env.device)
    # root state
    root_state = rigid_objects.data.default_root_state.clone()
    random_positions_x = torch.rand(env_ids.shape[0], device=env.device) * (X_LIMIT_POS - X_LIMIT_NEG) + X_LIMIT_NEG
    random_positions_y = torch.rand(env_ids.shape[0], device=env.device) * (Y_LIMIT_POS - Y_LIMIT_NEG) + Y_LIMIT_NEG
    random_quaternions = random_z_axis_rotation_quaternion(env.device, env_ids.shape[0])

    root_state[env_ids, 0] = random_positions_x
    root_state[env_ids, 1] = random_positions_y
    root_state[env_ids, 3:7] = random_quaternions.clone()


    # Compute world axes using quaternion rotation
    '''axes_x_w = torch_utils.quat_rotate(root_state[:, 3:7], env.boulder_measurements.axes[:, 0, :])
    axes_y_w = torch_utils.quat_rotate(root_state[:, 3:7], env.boulder_measurements.axes[:, 1, :])
    axes_z_w = torch_utils.quat_rotate(root_state[:, 3:7], env.boulder_measurements.axes[:, 2, :])
    ellipsoid_axes_w = torch.stack([axes_x_w, axes_y_w, axes_z_w], dim=1).reshape(-1, 3, 3)

    # Compute extremities
    extremeties = torch.cat([
        ellipsoid_axes_w,  # Extremeties 1, 2, 3
        -ellipsoid_axes_w  # Extremeties 4, 5, 6
    ], dim=1)#.transpose(1, 2)  # Shape: (len(env_ids), 6, 3)

    z_values = extremeties[:, 2]  # Extract z components
    x_values = extremeties[:, 0]  # Extract x components

    # Calculate min/max values
    min_z = z_values.min(dim=1).values
    max_z = z_values.max(dim=1).values
    min_x = x_values.min(dim=1).values + root_state[:, 0]
    max_x = x_values.max(dim=1).values + root_state[:, 0]

    # Compute z_start and update root_state
    z_start =  platform_heights+ 0.5 * (min_z[env_ids] + max_z[env_ids]) +0.7
    root_state[env_ids, 2] = z_start#[env_ids]'''

    
    axes_x_w = torch_utils.quat_rotate(
        root_state[:, 3:7], env.boulder_measurements.axes[:, 0, :]
    )  # torch.tile(axes[0, :], (num_envs, 1)))
    axes_y_w = torch_utils.quat_rotate(
        root_state[:, 3:7], env.boulder_measurements.axes[:, 1, :]
    )  # torch.tile(axes[1, :], (num_envs, 1)))
    axes_z_w = torch_utils.quat_rotate(
        root_state[:, 3:7], env.boulder_measurements.axes[:, 2, :]
    )  # torch.tile(axes[2, :], (num_envs, 1)))
    ellipsoid_axes_w = torch.stack([axes_x_w, axes_y_w, axes_z_w], dim=1).reshape(-1, 9)
    for i in env_ids:
        extremety1 = ellipsoid_axes_w[i, :3]
        extremety2 = ellipsoid_axes_w[i, 3:6]
        extremety3 = ellipsoid_axes_w[i, 6:]
        extremety4 = -ellipsoid_axes_w[i, :3]
        extremety5 = -ellipsoid_axes_w[i, 3:6]
        extremety6 = -ellipsoid_axes_w[i, 6:]
        extremeties = torch.stack([extremety1, extremety2, extremety3, extremety4, extremety5, extremety6])
        # Extract the z components (the third element of each vector)
        z_values = extremeties[:, 2]
        x_values = extremeties[:, 0]
        # Find the minimum and maximum z-values
        min_z = z_values.min()
        max_z[i] = z_values.max()
        min_x[i] = x_values.min() + root_state[i, 0]
        max_x[i] = x_values.max() + root_state[i, 0]
        z_start[i] = 0.5 * (min_z + max_z[i])  # -0.704
        root_state[i, 2] = z_start[i]

    # Extremety1 = self.ellipsoid_axes_w[i,:3]
    root_state[env_ids, :3] += env.scene.env_origins[env_ids]
    rock_root_x = root_state[:,0] 
    rigid_objects.write_root_state_to_sim(root_state[env_ids], env_ids=env_ids)

    # Mass
    masses = env.boulder_measurements.original_masses
    noise_masses = (torch.rand_like(masses) - 0.5) * 2 * 0.1 * masses
    new_masses = masses + noise_masses
    env_ids = env_ids.cpu()
    rigid_objects.root_physx_view.set_masses(new_masses, indices=env_ids)
    # Friction
    mat_props = rigid_objects.root_physx_view.get_material_properties()
    mat_props[:, :, :2].uniform_(0.35, 0.6)
    rigid_objects.root_physx_view.set_material_properties(mat_props, env_ids)
    # Write new and propreties position in measurements
    env.boulder_measurements.update_measurements()
    
    return min_x, max_x, rock_root_x


def rejection_sampling_boulder_flat(
    env: ManagerBasedEnv,
    env_ids: torch.Tensor,
    asset_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
):
    # print("start reset_idx")
    """Reset selected environments.
    resetting flag for termination in  step? -> only needed if reset_stepping is enabled, not used in latest version, doublecheck
    Resetting
        - reset pid
        - reset soil/fee

    Soil resampling
        - if bucket collides with boulder
        - if enabled: sample soil with height (excavator config is then relative to the soil)
        - if enabled: sample max_depth
        - if enabled: sample pullup distancse
        - if enabled: sample soil_obstacles

        - if enabled: randomize orientation (pitch, turn?)
        - if enabled: sample arm config
            if in soil, sample random fill volume
        - set wheel support positions
    Arm Resampling
        - update measurements
        - update fee forces (check if in obstacle and change params accordingly)
        - reset again if forces too large


    Other
        Logs episode info
        Resets selected buffers

    Args:
        env_ids (list[int]): List of environment ids which must be reset
    """

    # randomize boulder and get max_pos
    min_x, max_x, rock_root_x = boulder_randomization_flat(env, env_ids)

    asset: RigidObject = env.scene[asset_cfg.name]
    if len(env_ids) == 0:
        return
    # env.pid.reset(idxs=env_ids)
    env.soil.reset(idxs=env_ids)
    # env.termination_excavation.reset(idxs=env_ids)

    if env.cfg.reset.sample_soil:
        env.soil.sample(idxs=env_ids)  # height & params & max depth, random or fixed, depending on cfg
        #env.soil.soil_height.z[env_ids,:] = platform_heights.unsqueeze(1)
        #env.soil.soil_height.offset = platform_heights.unsqueeze(1)
        #env.soil.max_depth_height.sample(idxs=env_ids)
        #env.soil.soil_parameters.sample(idxs=env_ids)

    if env.cfg.reset.sample_max_depth:
        # TODO, already in soil.sample, maybe take it out three
        pass

    if env.cfg.reset.sample_pullup_dist:
        env.pullup_dist[env_ids] = u_rand(
            len(env_ids), env.cfg.reset.pullup_dist_range[0], env.cfg.reset.pullup_dist_range[1], env.device
        )

    env.m545_measurements.joint_vel[env_ids] = 0.0  # DOF Vel
    env.m545_measurements.root_lin_vel_w[env_ids] = 0.0
    env.m545_measurements.root_ang_vel_w[env_ids] = 0.0
    env.m545_measurements.j_pitch_vel[env_ids] = 0.0
    env.m545_measurements.j_pitch_ang_vel[env_ids] = 0.0

    # rejection "sampling" using cached reset states
    # all envs to reset are rejected at the beginning
    rejected = env_ids.clone()

    # get index bands to sample from
    rejected_sampling_idx_lower, rejected_sampling_idx_upper = env.reset_cache.get_sampling_idx(rejected)

    reset_loop_count = 0
    while len(rejected) > 0:
        if reset_loop_count > 1000:
            raise ValueError("Resetting loop count > 1000")
        reset_loop_count += 1

        env.soil.reset(rejected)  # can be that before it was in soil, fill state was set but rejected

        if not env.cfg.reset.fixed_config:
            sampled = torch.round(
                u_rand(len(rejected), rejected_sampling_idx_lower, rejected_sampling_idx_upper, device=env.device)
            ).to(torch.long)

        else:
            sampled = torch.ones(len(rejected), device=env.device, dtype=torch.long)
            sampled *= env.cfg.reset.configs["idx"]  # env.cfg.reset.configs.__dict__[env.cfg.reset.fixed_config]["idx"]
            print(f"FIXED reset to sample nr. {sampled[0]}")

        env.sampled[rejected] = sampled
        #####
        ## Get Measurements from Cache
        #####
        # when slicing, does not loose reference!
        env.m545_measurements.joint_pos[rejected] = env.reset_cache.data["dof_pos"][sampled]
        env.m545_measurements.root_pos_w[rejected] = env.reset_cache.data["base_pos"][sampled]
        env.m545_measurements.root_quat_w[rejected] = env.reset_cache.data["base_quat"][sampled]

        # for idx, box_asset in enumerate(env.box_asset_list):
        #    box_asset.root_pos_w[rejected] = env.reset_cache.data["box_" + str(idx)][sampled] - 0.016
        env.m545_measurements.mm[rejected] = env.reset_cache.data["mass_matrix"][sampled]
        env.m545_measurements.bucket_jac_lin_T_dof[rejected] = env.reset_cache.data["bucket_jac_lin"][
            sampled
        ].transpose(-2, -1)
        env.m545_measurements.bucket_jac_rot_T_dof[rejected] = env.reset_cache.data["bucket_jac_rot"][
            sampled
        ].transpose(-2, -1)

        env.m545_measurements.gravity_tau[rejected] = env.reset_cache.data["gravity_torques"][sampled]

        env.m545_measurements.bucket_pos_w[rejected] = env.reset_cache.data["bucket_pos"][sampled]
        env.m545_measurements.prev_bucket_pos_w[rejected] = env.reset_cache.data["bucket_pos"][
            sampled
        ]  # prev = current
        env.m545_measurements.bp_unit_vector_w[rejected] = env.reset_cache.data["bp_unit_vec"][sampled]
        env.m545_measurements.bucket_vel_w[rejected, :] = 0.0

        # update all env fee (easier than do it per index)
        env.soil.update_1(
            rejected
        )  # until bucket state, we sample fill volume based on depth and finish the soil update

        # get indices to resample soil fill ratio, if inside soil (depth > 0)
        # only do it for the ones to resample!!!!! -> changes ssp -> might lead to invalid soil config!!!
        rejected_in_soil_idx = torch.where(env.soil.get_bucket_depth()[rejected] > env.zero_scalar)[0]

        env.soil.set_bucket_fill_state(
            torch.rand(len(rejected_in_soil_idx), 1, device=env.device),
            rejected[rejected_in_soil_idx],
            is_ratio=True,
        )

        env.soil.update_2()  # update ssp, soil_params, forces

        # check the conditions

        # If it does not work just take norm bucket com - pos_w  > max radi* scale
        bottom_plate_x = (
            env.m545_measurements.bucket_pos_w + env.m545_measurements.bp_unit_vector_w * env.cfg.bucket.a
        )[:, 0]


        
        is_front = bottom_plate_x[rejected] > (rock_root_x-env.scene.env_origins[:,0])[rejected]+0.3
        is_back = bottom_plate_x[rejected] < (rock_root_x-env.scene.env_origins[:,0])[rejected]-0.3#min_x[rejected] # 
        is_between = (bottom_plate_x[rejected] <= (rock_root_x-env.scene.env_origins[:,0])[rejected]+0.3) & (bottom_plate_x[rejected] >= (rock_root_x-env.scene.env_origins[:,0])[rejected]-0.3)
        # collides_w_boulder = (z_start[rejected].reshape(1,-1) > env.m545_measurements.bucket_pos_w[rejected,2].reshape(1,-1)).any(dim=-1, keepdim=True)
        too_close_boulder_x = torch.zeros_like(is_between)
        too_close_boulder_x[is_back] = (min_x[rejected][is_back] - bottom_plate_x[rejected][is_back]) < 0.2
        too_close_boulder_x[is_front] = (bottom_plate_x[rejected][is_front] - max_x[rejected][is_front]) < 0.2
        collides_w_boulder = (is_between | too_close_boulder_x).reshape(-1, 1)

        # env.boulder_measurements.ellipsoid_axes_w

        too_close_x = (
            torch.abs(env.m545_measurements.bucket_pos_w[rejected, 0:1])
            < env.pullup_dist[rejected].unsqueeze(-1) + env.curriculum_excavation.curr_pullup_band
        )

        # these should now never be active anymore, because we sample only in the valid range
        # wrong, its min, max based on all soil heights in env

        too_high_z = env.soil.get_bucket_depth()[rejected] < env.cfg.reset.arm_depth_limits.lower
        too_low_z = env.soil.get_bucket_depth()[rejected] > env.cfg.reset.arm_depth_limits.upper * (
            1.0 - env.cfg.reset.only_above_soil
        )

        too_small_ang = env.soil.get_ssp_ang_to_soil()[rejected] < env.cfg.reset.min_ang_bucket_to_soil
        invalid_soil_model_state = env.soil.is_state_invalid(rejected)

        # too_large_force = (
        #     torch.linalg.norm(env.soil.get_resultant_force()[rejected], dim=-1, keepdim=True)
        #     > env.cfg.reset.max_soil_force
        # )
        # too_large_moment = (
        #     torch.linalg.norm(env.soil.get_resultant_moment()[rejected], dim=-1, keepdim=True)
        #     > env.cfg.reset.max_soil_moment
        # )  # only moment in y

        force_moment = torch.linalg.norm(
            env.soil.get_resultant_force()[rejected], dim=-1, keepdim=True
        ) + torch.linalg.norm(env.soil.get_resultant_moment()[rejected], dim=-1, keepdim=True)

        # max_force = 68.0 - 6.0 * env.m545_asset.bucket_pos_w[rejected, 0:1] #50-20
        # max_force = 74.0 - 8.0 * env.m545_asset.bucket_pos_w[rejected, 0:1]  # 50-10
        # max_force = 58.0 - 6.0 * env.m545_asset.bucket_pos_w[rejected, 0:1]  # 40-10
        # max_force = 42.0 - 4.0 * env.m545_asset.bucket_pos_w[rejected, 0:1]  # 30-10
        # max_force = 26.0 - 2.0 * env.m545_asset.bucket_pos_w[rejected, 0:1]  # 30-10

        too_large_force_moment = force_moment > env.cfg.reset.max_soil_force_and_moment
        # too_large_force = force_moment > max_force * 1000.0
        violating_max_depth = (
            env.m545_measurements.bucket_pos_w[rejected, 2:]
            < env.soil.get_max_depth_height_at_pos(env.m545_measurements.bucket_pos_w[rejected, 0:1], env_ids=rejected)
            - env.cfg.terminations_boulder.max_depth_overshoot
        )

        # check joint torques at reset soil + gravity!!

        env.bucket_force_com[rejected] = env.soil.get_resultant_force()[rejected]
        env.bucket_moment_com[rejected] = env.soil.get_resultant_moment()[rejected]

        ext_f_genco_tau = torch.matmul(
            env.m545_measurements.bucket_jac_lin_T_dof[rejected], env.bucket_force_com[rejected].unsqueeze(-1)
        )
        env.ext_f_tau[rejected] = ext_f_genco_tau.squeeze(-1)[:, -env.m545_asset.num_joints :]

        ext_m_genco_tau = torch.matmul(
            env.m545_measurements.bucket_jac_rot_T_dof[rejected], env.bucket_moment_com[rejected].unsqueeze(-1)
        )
        env.ext_m_tau[rejected] = ext_m_genco_tau.squeeze(-1)[:, -env.m545_asset.num_joints :]

        # des_dof_acc = (torch.tensor([25,30,20,20], device=env.device)*env.clipped_scaled_actions[rejected])
        # inertial_therm = (env.m545_measurements.mm[rejected].matmul(des_dof_acc.view(len(rejected), 4, 1)).view(len(rejected), 4))

        env.torques[rejected] = (
            -env.m545_measurements.gravity_tau[rejected] - env.ext_f_tau[rejected] - env.ext_m_tau[rejected]
        )

        env.limits.update(env.m545_measurements.joint_pos)

        # if only within limits -> isaac crash -> probably too large forces, this is fine 0.22% done after reset + 1 step
        violating_torque_lower_limits = (
            env.torques[rejected]
            < env.limits.curr_torque_limit_lower[rejected] + env.cfg.reset.max_soil_force_and_moment
        ).any(dim=-1, keepdim=True)
        violating_torque_upper_limits = (
            env.torques[rejected]
            > env.limits.curr_torque_limit_upper[rejected] - env.cfg.reset.max_soil_force_and_moment
        ).any(dim=-1, keepdim=True)

        # narrow resetting for trenching tests
        if env.cfg.reset.narrow.enable:
            # too_small_ang; done above
            too_large_ang = env.soil.get_ssp_ang_to_soil()[rejected] > env.cfg.reset.narrow.max_ang_bucket_to_soil
            # too_small_x; done above
            too_small_x = env.m545_measurements.bucket_pos_w[rejected, 0:1] < env.cfg.reset.narrow.x_min
            too_large_x = env.m545_measurements.bucket_pos_w[rejected, 0:1] > env.cfg.reset.narrow.x_max
            too_small_z = env.m545_measurements.bucket_pos_w[rejected, 2:] < env.cfg.reset.narrow.z_min
            too_large_z = env.m545_measurements.bucket_pos_w[rejected, 2:] > env.cfg.reset.narrow.z_max
        else:
            # set all to false with correct shape
            too_large_ang = torch.zeros_like(too_close_x)
            too_small_x = too_large_ang
            too_large_x = too_large_ang
            too_small_z = too_large_ang
            too_large_z = too_large_ang

        rejected_idx = torch.nonzero(
            (
                too_close_x
                | collides_w_boulder
                # | too_high_z
                | too_low_z
                | too_small_ang
                | invalid_soil_model_state
                # | too_large_force
                # | too_large_moment
                | too_large_force_moment  # worse without
                | violating_max_depth
                | violating_torque_lower_limits  # worse without
                | violating_torque_upper_limits
                # narrow reset
                | too_large_ang
                | too_small_x
                | too_large_x
                | too_small_z
                | too_large_z
            ),
            as_tuple=True,
        )[0]

        rejected = rejected[rejected_idx]  # select which envs need another reset
        rejected_sampling_idx_lower = rejected_sampling_idx_lower[rejected_idx]
        rejected_sampling_idx_upper = rejected_sampling_idx_upper[rejected_idx]

    # Write the new state
    asset.write_root_pose_to_sim(
        torch.cat(
            (
                env.m545_measurements.root_pos_w[env_ids].clone() + env.scene.env_origins[env_ids].clone(),
                env.m545_measurements.root_quat_w[env_ids].clone(),
            ),
            dim=-1,
        ),
        env_ids=env_ids,
    )
    asset.write_root_velocity_to_sim(torch.zeros((env.num_envs, 6), device=env.device)[env_ids], env_ids=env_ids)

    asset.write_joint_state_to_sim(
        position=env.m545_measurements.joint_pos[env_ids].clone(),
        velocity=env.m545_measurements.joint_vel[env_ids].clone(),
        env_ids=env_ids,
    )
    #

    # reset measurements used in obs (and logging)
    env.actions[env_ids] = 0.0  # last action reset in step loop

    # initial torque ob (soil + gravity)
    ext_f_genco_tau = torch.matmul(
        env.m545_measurements.bucket_jac_lin_T_dof[env_ids], env.soil.get_resultant_force()[env_ids].unsqueeze(-1)
    )
    ext_f_tau = ext_f_genco_tau.squeeze(-1)[:, -env.m545_asset.num_joints :]

    ext_m_genco_tau = torch.matmul(
        env.m545_measurements.bucket_jac_rot_T_dof[env_ids], env.soil.get_resultant_moment()[env_ids].unsqueeze(-1)
    )
    ext_m_tau = ext_m_genco_tau.squeeze(-1)[:, -env.m545_asset.num_joints :]

    # inertial therm
    # des_dof_acc = (torch.tensor([25,30,20,20], device=env.device)*env.clipped_scaled_actions[env_ids])
    # inertial_therm = (env.m545_measurements.mm[env_ids].matmul(des_dof_acc.view(len(env_ids), 4, 1)).view(len(env_ids), 4))

    env.torques[env_ids] = (
        -ext_f_tau
        - ext_m_tau
        - env.m545_measurements.gravity_tau[env_ids]
        # + env.m545_asset.root_physx_view.get_coriolis_and_centrifugal_forces()[env_ids]
    )
    env.ext_f_tau[env_ids] = 0.0
    env.ext_m_tau[env_ids] = 0.0
    env.inertial_tau[env_ids] = 0.0

    env.last_fill_ratio[env_ids] = env.soil.get_fill_ratio()[env_ids]

    # for debug
    """max_x = torch.zeros(env.num_envs,device=env.device)
    min_x = torch.zeros(env.num_envs,device=env.device)
    axes_x_w = torch_utils.quat_rotate(env.boulder_measurements.root_quat_w[:,:],env.boulder_measurements.axes[:,0, :]) #torch.tile(axes[0, :], (num_envs, 1)))
    axes_y_w = torch_utils.quat_rotate(env.boulder_measurements.root_quat_w[:,:],env.boulder_measurements.axes[:,1, :]) #torch.tile(axes[1, :], (num_envs, 1)))
    axes_z_w = torch_utils.quat_rotate(env.boulder_measurements.root_quat_w[:,:],env.boulder_measurements.axes[:,2, :]) #torch.tile(axes[2, :], (num_envs, 1)))
    ellipsoid_axes_w = torch.stack([axes_x_w, axes_y_w, axes_z_w], dim=1).reshape(-1, 9)

    for i in range(env.num_envs):
        extremety1 = ellipsoid_axes_w[i,:3]
        extremety2 = ellipsoid_axes_w[i,3:6]
        extremety3 = ellipsoid_axes_w[i,6:]
        extremety4 = -ellipsoid_axes_w[i,:3]
        extremety5 = -ellipsoid_axes_w[i,3:6]
        extremety6 = -ellipsoid_axes_w[i,6:]
        extremeties = torch.stack([extremety1, extremety2, extremety3, extremety4, extremety5, extremety6])
        # Extract the z components (the third element of each vector)
        z_values = extremeties[:, 2]
        x_values = extremeties[:, 0]

        # Find the minimum and maximum z-values
        min_x[i] = x_values.min()+env.boulder_measurements.root_pos_w[i,0]-env.scene.env_origins[i,0]
        max_x[i] = x_values.max()+env.boulder_measurements.root_pos_w[i,0]-env.scene.env_origins[i,0]
    bp_plate = (env.m545_measurements.bucket_pos_w+env.m545_measurements.bp_unit_vector_w*env.cfg.bucket.a)[:,0]
    print('pre computed bp plate', bottom_plate_x)
    print('min_x', min_x)
    print('max_x', max_x)
    print('bp_plate', bp_plate)
    print('-----------------')"""


def random_z_axis_rotation_quaternion(device, length):
    """
    Generate a tensor of random rotation quaternions around the Z-axis.

    Args:
        device (torch.device): The device to store the tensor on.
        length (int): The number of quaternions to generate.

    Returns:
        torch.Tensor: A tensor of shape (length, 4), where each row represents a quaternion in wxyz convention.
    """
    # Generate random angles theta between 0 and 2*pi
    theta = torch.rand(length, device=device) * 2 * np.pi

    # Compute the components of the quaternions
    w = torch.cos(theta / 2)
    x = torch.zeros_like(w)
    y = torch.zeros_like(w)
    z = torch.sin(theta / 2)

    # Stack the components into a tensor of shape (length, 4)
    quaternions = torch.stack([w, x, y, z], dim=1)

    return quaternions


def segments_traverse_torch(y_min, y_max, y_0, y_1):
    # Ensure y_min <= y_max and y_0 <= y_1
    y_min, _ = torch.min(torch.stack((y_min, y_max), dim=1), dim=1)
    y_max, _ = torch.max(torch.stack((y_min, y_max), dim=1), dim=1)
    y_0, _ = torch.min(torch.stack((y_0, y_1), dim=1), dim=1)
    y_1, _ = torch.max(torch.stack((y_0, y_1), dim=1), dim=1)

    # Check if segments traverse (i.e., they overlap or intersect)
    no_intersection = (y_max < y_0) | (y_1 < y_min)

    # Return True for environments where segments traverse
    return ~no_intersection

    # ellipsoid axes, 3 lines
    # We start at the root of the boulder
