# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Common functions that can be used to activate certain terminations.

The functions can be passed to the :class:`isaaclab.managers.TerminationTermCfg` object to enable
the termination introduced by the function.
"""

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

from isaaclab.assets import Articulation, RigidObject
from isaaclab.managers import SceneEntityCfg
from isaaclab.sensors import ContactSensor

if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedRLEnv
    from isaaclab.managers.command_manager import CommandTerm

"""
MDP terminations.
"""


def time_out(env: ManagerBasedRLEnv) -> torch.Tensor:
    """Terminate the episode when the episode length exceeds the maximum episode length."""
    return env.episode_length_buf >= env.max_episode_length


def command_resample(env: ManagerBasedRLEnv, command_name: str, num_resamples: int = 1) -> torch.Tensor:
    """Terminate the episode based on the total number of times commands have been re-sampled.

    This makes the maximum episode length fluid in nature as it depends on how the commands are
    sampled. It is useful in situations where delayed rewards are used :cite:`rudin2022advanced`.
    """
    command: CommandTerm = env.command_manager.get_term(command_name)
    return torch.logical_and((command.time_left <= env.step_dt), (command.command_counter == num_resamples))


def negative_termination_base_vel(
    env: ManagerBasedRLEnv, asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")
) -> torch.Tensor:
    asset: Articulation = env.scene[asset_cfg.name]
    base_vel = asset.data.root_lin_vel_w
    mask = torch.linalg.norm(base_vel, dim=-1) > env.cfg.terminations_boulder.max_base_vel  # 0.1
    return mask


def negative_termination_joint_vel(
    env: ManagerBasedRLEnv, asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")
) -> torch.Tensor:
    asset: Articulation = env.scene[asset_cfg.name]
    joint_vel = asset.data.joint_vel
    mask = torch.abs(joint_vel).any(dim=-1) > env.cfg.terminations_boulder.max_joint_vel
    return mask


def negative_termination_bucket_vel(env: ManagerBasedRLEnv, max_bucket_vel) -> torch.Tensor:
    mask = env.m545_measurements.bucket_vel_norm > max_bucket_vel
    return mask


def negative_termination_boulder_height(env: ManagerBasedRLEnv) -> torch.Tensor:
    mask = env.boulder_measurements.root_quat_w[:, 2] < -1
    return mask


def negative_termination_bucket_aoa(env: ManagerBasedRLEnv) -> torch.Tensor:
    bad_aoa = env.m545_measurements.bucket_aoa < 0.0
    fast_enough = env.m545_measurements.bucket_vel_norm > 0.075
    in_soil = (env.soil.get_bucket_depth() > 0.05).squeeze()
    mask = torch.logical_and(bad_aoa, torch.logical_and(fast_enough, in_soil))
    # If not trained enough, do not terminate
    if (env.curriculum_excavation.level_boulder == 0) or (env.curriculum_excavation.level_boulder == 1) or (env.curriculum_excavation.level_boulder == 2):
        mask = torch.zeros_like(mask)
    # print('_negative_termination_bucket_aoa', mask)
    return mask


def positive_termination_boulder_in_bucket(env: ManagerBasedRLEnv, termination_height, termination_height_above_soil):
    # check if boulder is in bucket
    boulder_in_shovel = env.boulder_measurements.in_shovel > 0
    desired_height = torch.maximum(torch.tensor(termination_height, device =env.device), termination_height_above_soil+env.platform_heights)
    #print("boulder_in_shovel", boulder_in_shovel)
    high_enough = env.m545_measurements.bucket_pos_w[:, 2] > desired_height
    #print("high_enough", high_enough)
    curl = env.m545_measurements.bucket_ang_gac < -0.5
    #print("curl", curl)

    mask = boulder_in_shovel
    mask &= curl
    mask &= high_enough
    # print(mask)
    return mask

def positive_termination_boulder_in_bucket_flat(env: ManagerBasedRLEnv, termination_height):
    # check if boulder is in bucket
    boulder_in_shovel = env.boulder_measurements.in_shovel > 0
    desired_height = torch.tensor(termination_height, device =env.device)

    high_enough = env.m545_measurements.bucket_pos_w[:, 2] > desired_height

    curl = env.m545_measurements.bucket_ang_gac < -0.5

    mask = boulder_in_shovel
    mask &= curl
    mask &= high_enough
    # print(mask)
    return mask
