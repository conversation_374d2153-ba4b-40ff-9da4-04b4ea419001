# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Gamepad controller for SE(3) control."""

import numpy as np
import time
import weakref
from collections.abc import Callable
from scipy.spatial.transform.rotation import Rotation

import carb
import omni

from isaaclab.devices.device_base import DeviceBase


class m545Gamepad_driving_and_arm(DeviceBase):
    """A gamepad controller for sending SE(3) commands as delta poses and binary command (open/close).

    This class is designed to provide a gamepad controller for a robotic arm with a gripper.
    It uses the gamepad interface to listen to gamepad events and map them to the robot's
    task-space commands.

    The command comprises of two parts:

    * delta pose: a 6D vector of (x, y, z, roll, pitch, yaw) in meters and radians.
    * gripper: a binary command to open or close the gripper.

    Stick and Button bindings:
        ============================ =========================
        Description                  Stick/Button (+ve axis)
        ============================ =========================
        Operate gripper             A Button (keep pressed)
        Operate Dipper              B Button (keep pressed)
        Operate Tele                Y Button (keep pressed)
        Operate Pitch               X Button (keep pressed)
        Positive velocity            RIGHT_SHOULDER
        Negative velocity            LEFT_SHOULDER
        Drive                        D-Pad
        Cabin Turn                         Left Stick Horizontal
        ============================ =========================

    .. seealso::

        The official documentation for the gamepad interface: `Carb Gamepad Interface <https://docs.omniverse.nvidia.com/dev-guide/latest/programmer_ref/input-devices/gamepad.html>`__.

    """

    def __init__(
        self,
        v_x_sensitivity: float = 0.8,
        v_y_sensitivity: float = 0.1,
        omega_z_sensitivity: float = 1.0,
        dead_zone: float = 0.05,
    ):
        """Initialize the gamepad layer.

        Args:
            dead_zone: Magnitude of dead zone for gamepad. An event value from the gamepad less than
                this value will be ignored. Defaults to 0.01.
        """
        # turn off simulator gamepad control
        carb_settings_iface = carb.settings.get_settings()
        carb_settings_iface.set_bool("/persistent/app/omniverse/gamepadCameraControl", False)
        # store inputs
        self.v_x_sensitivity = v_x_sensitivity
        self.v_y_sensitivity = v_y_sensitivity
        self.omega_z_sensitivity = omega_z_sensitivity
        self._cabin_sensitivity = 3.0
        self._arm_sensitivity = np.array([1, 1, 1, 1])
        self.dead_zone = dead_zone
        # acquire omniverse interfaces
        self._appwindow = omni.appwindow.get_default_app_window()
        self._input = carb.input.acquire_input_interface()
        self._gamepad = self._appwindow.get_gamepad(0)
        # note: Use weakref on callbacks to ensure that this object can be deleted when its destructor is called
        self._gamepad_sub = self._input.subscribe_to_gamepad_events(
            self._gamepad,
            lambda event, *args, obj=weakref.proxy(self): obj._on_gamepad_event(event, *args),
        )
        # bindings for gamepad to command
        self._create_key_bindings()
        # command buffers
        self._wheel_velocity = np.zeros(4)
        self._steering = np.zeros(2)
        self._cabin_vel = np.zeros(1)
        self._arm_vel = np.zeros(4)
        self._additional_callbacks = dict()

        # Button pressed
        self.is_button_pressed = {
            "DPAD_UP_DOWN": False,
            "A": False,
            "B": False,
            "Y": False,
            "X": False,
            "RIGHT_SHOULDER": False,
            "LEFT_SHOULDER": False,
        }
        self.last_update_time = 0

    def __del__(self):
        """Unsubscribe from gamepad events."""
        self._input.unsubscribe_from_gamepad_events(self._gamepad, self._gamepad_sub)
        self._gamepad_sub = None

    def __str__(self) -> str:
        """Returns: A string containing the information of joystick."""
        msg = f"Gamepad Controller for SE(3): {self.__class__.__name__}\n"
        msg += f"\tDevice name: {self._input.get_gamepad_name(self._gamepad)}\n"
        msg += "\t----------------------------------------------\n"
        msg += "\tToggle gripper (open/close): X\n"
        msg += "\tMove arm along x-axis: Left Stick Up/Down\n"
        msg += "\tMove arm along y-axis: Left Stick Left/Right\n"
        msg += "\tMove arm along z-axis: Right Stick Up/Down\n"
        msg += "\tRotate arm along x-axis: D-Pad Right/Left\n"
        msg += "\tRotate arm along y-axis: D-Pad Down/Up\n"
        msg += "\tRotate arm along z-axis: Right Stick Left/Right\n"
        return msg

    """
    Operations
    """

    def reset(self):
        # default flags
        # self._close_gripper = False
        self._wheel_velocity.fill(0.0)
        self._steering.fill(0.0)
        self._cabin_vel.fill(0.0)
        self._arm_vel.fill(0.0)
        print("In reset")

    def add_callback(self, key: carb.input.GamepadInput, func: Callable):
        """Add additional functions to bind gamepad.

        A list of available gamepad keys are present in the
        `carb documentation <https://docs.omniverse.nvidia.com/dev-guide/latest/programmer_ref/input-devices/gamepad.html>`__.

        Args:
            key: The gamepad button to check against.
            func: The function to call when key is pressed. The callback function should not
                take any arguments.
        """
        self._additional_callbacks[key] = func

    def advance(self) -> tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Provides the result from gamepad event state.

        Returns:
            A tuple containing the commands.
        """

        # Check if the button is pressed, increase wheel velocity whith time
        vel_to_return = self._wheel_velocity
        if self.is_button_pressed["DPAD_UP_DOWN"]:
            time_pressed_scaling = time.time() - self.last_update_time
            if time_pressed_scaling > 1.0:
                vel_to_return = self._wheel_velocity * time_pressed_scaling
        # Arm command
        if self.is_button_pressed["RIGHT_SHOULDER"]:
            self.update_arm_velocity(direction=1)
        elif self.is_button_pressed["LEFT_SHOULDER"]:
            self.update_arm_velocity(direction=-1)
        else:
            self.reset_arm_velocity()

        return vel_to_return, self._steering, self._cabin_vel, self._arm_vel

    """
    Internal helpers.
    """

    def update_arm_velocity(self, direction):
        """Update arm velocity based on the pressed buttons and direction."""
        for i, button in enumerate(["A", "B", "Y", "X"], start=0):
            if self.is_button_pressed[button]:
                self._arm_vel[i] = direction * self._arm_sensitivity[i]

    def reset_arm_velocity(self):
        """Reset arm velocity to zero."""
        for i in range(0, 4):
            self._arm_vel[i] = 0

    def _on_gamepad_event(self, event, *args, **kwargs):
        """Subscriber callback to when kit is updated.

        Reference:
            https://docs.omniverse.nvidia.com/dev-guide/latest/programmer_ref/input-devices/gamepad.html
        """
        # check if the event is a button press
        cur_val = event.value
        if abs(cur_val) < self.dead_zone:
            cur_val = 0
        # print('cur_val', cur_val)
        # -- dpad (4 arrow buttons on the console)
        print(time.time(), event.input)
        ## -- button pressed
        if event.input == carb.input.GamepadInput.A:
            if cur_val > 0.5:
                self.is_button_pressed["A"] = True
            else:
                self.is_button_pressed["A"] = False
        if event.input == carb.input.GamepadInput.B:
            if cur_val > 0.5:
                self.is_button_pressed["B"] = True
            else:
                self.is_button_pressed["B"] = False
        if event.input == carb.input.GamepadInput.X:
            if cur_val > 0.5:
                self.is_button_pressed["X"] = True
            else:
                self.is_button_pressed["X"] = False
        if event.input == carb.input.GamepadInput.Y:
            if cur_val > 0.5:
                self.is_button_pressed["Y"] = True
            else:
                self.is_button_pressed["Y"] = False
        if event.input == carb.input.GamepadInput.RIGHT_SHOULDER:
            if cur_val > 0.5:
                self.is_button_pressed["RIGHT_SHOULDER"] = True
            else:
                self.is_button_pressed["RIGHT_SHOULDER"] = False
        if event.input == carb.input.GamepadInput.LEFT_SHOULDER:
            if cur_val > 0.5:
                self.is_button_pressed["LEFT_SHOULDER"] = True
            else:
                self.is_button_pressed["LEFT_SHOULDER"] = False

        # -- left and right stick
        if event.input in self._INPUT_STICK_VALUE_MAPPING:
            # print('In sticks')
            value, sensitivity = self._INPUT_STICK_VALUE_MAPPING[event.input]
            if (event.input == carb.input.GamepadInput.RIGHT_STICK_RIGHT) or (
                event.input == carb.input.GamepadInput.RIGHT_STICK_LEFT
            ):
                print("event type in if", event.input)
                # change the value only if the stick is moved (soft press)
                if cur_val != 0:  # because left and right callback always called
                    self._cabin_vel[0] = value * sensitivity * cur_val
            else:
                self._cabin_vel[0] = 0
        else:
            self._cabin_vel[0] = 0

        # TODO restructure the code with groups for wheels, steernig and arm to homgeinize for stick and PAD
        if event.input in self._INPUT_DPAD_VALUE_MAPPING:
            value, sensitivity = self._INPUT_DPAD_VALUE_MAPPING[event.input]
            # change the value only if button is pressed on the DPAD
            # self._wheel_velocity += direction*sens*value
            # print('Inisde wheel velocity, outside if', self._wheel_velocity)
            if (event.input == carb.input.GamepadInput.DPAD_UP) or (event.input == carb.input.GamepadInput.DPAD_DOWN):
                if cur_val > 0.5:
                    self.last_update_time = time.time()
                    self.is_button_pressed["DPAD_UP_DOWN"] = True
                    self._wheel_velocity += sensitivity * value
                else:
                    self.last_update_time = time.time()
                    self.is_button_pressed["DPAD_UP_DOWN"] = False
                    self._wheel_velocity -= sensitivity * value
            elif (event.input == carb.input.GamepadInput.DPAD_RIGHT) or (
                event.input == carb.input.GamepadInput.DPAD_LEFT
            ):
                if cur_val > 0.5:
                    self.last_update_time = time.time()
                    self._steering += sensitivity * value
                else:
                    self.last_update_time = time.time()
                    # self._steering -= sensitivity*value

            # else:
            #    self._delta_pose_raw[:, axis] = 0
        # additional callbacks
        if event.input in self._additional_callbacks:
            self._additional_callbacks[event.input]()

        # since no error, we are fine :)
        return True

    def _create_key_bindings(self):
        """Creates default key binding."""
        # map gamepad input to the element in self._delta_pose_raw
        #   the first index is the direction (0: positive, 1: negative)
        #   the second index is the axis (0: x, 1: y, 2: z, 3: roll, 4: pitch, 5: yaw)
        #   the third index is the sensitivity of the command
        # Sttick
        self._INPUT_STICK_VALUE_MAPPING = {
            # forward command
            # carb.input.GamepadInput.LEFT_STICK_UP: (0, 0, self.pos_sensitivity),
            ## backward command
            # carb.input.GamepadInput.LEFT_STICK_DOWN: (1, 0, self.pos_sensitivity),
            ## right command
            carb.input.GamepadInput.RIGHT_STICK_RIGHT: (1, self._cabin_sensitivity),
            ## left command
            carb.input.GamepadInput.RIGHT_STICK_LEFT: (-1, self._cabin_sensitivity),
            ## upward command
            carb.input.GamepadInput.RIGHT_STICK_UP: (-1, self._arm_sensitivity[0]),
            ## downward command
            carb.input.GamepadInput.RIGHT_STICK_DOWN: (1, self._arm_sensitivity[0]),
            ## yaw command (positive)
            # carb.input.GamepadInput.LEFT_STICK_RIGHT: (0, 5, self.rot_sensitivity),
            ## yaw command (negative)
            # carb.input.GamepadInput.LEFT_STICK_LEFT: (1, 5, self.rot_sensitivity),
        }
        # Dpad
        self._INPUT_DPAD_VALUE_MAPPING = {
            # pitch command (positive)
            carb.input.GamepadInput.DPAD_UP: (np.asarray([1.0, 1.0, 1.0, 1.0]), self.v_x_sensitivity * 0.8),
            # pitch command (negative)
            carb.input.GamepadInput.DPAD_DOWN: (np.asarray([-1.0, -1.0, -1.0, -1.0]), self.v_x_sensitivity * 0.8),
            ## roll command (positive)
            carb.input.GamepadInput.DPAD_RIGHT: (np.asarray([-1.0, -1.0]), self.v_y_sensitivity * 0.8),
            ## roll command (negative)
            carb.input.GamepadInput.DPAD_LEFT: (np.asarray([1.0, 1.0]), self.v_y_sensitivity * 0.8),
        }
        # Button
        self._INPUT_BUTTON_VALUE_MAPPING = {}
