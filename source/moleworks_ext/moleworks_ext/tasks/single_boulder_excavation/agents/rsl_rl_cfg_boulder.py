# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from isaaclab.utils import configclass
from isaaclab_rl.rsl_rl import (
    RslRlOnPolicyRunnerCfg,
    RslRlPpoActorCriticCfg,
    RslRlPpoAlgorithmCfg,
)


@configclass
class M545BoulderPPORunnerCfg(RslRlOnPolicyRunnerCfg):
    # Policy

    # Runner
    num_steps_per_env = 6  # per iteration
    max_iterations = 20000  # number of policy updates
    empirical_normalization = False

    # logging
    logger = "wandb"
    experiment_name = "Isaac-m545-single-boulder"
    run_name = "train"
    tags = ["moleworks", "boulder", "ppo"]
    save_interval = 250

    # Policy
    policy = RslRlPpoActorCriticCfg(
        init_noise_std=0.4,
        actor_hidden_dims=(256,256),
        critic_hidden_dims=(256,256),
        activation="lrelu",  # elu, relu, selu, crelu, lrelu, tanh, sigmoid
    )
    # Algorithm
    algorithm = RslRlPpoAlgorithmCfg(
        value_loss_coef=0.5,
        use_clipped_value_loss=True,
        clip_param=0.2,
        entropy_coef=0.005,
        num_learning_epochs=5,
        num_mini_batches=4,  # mini batch size = num_envs * nsteps / nminibatches
        learning_rate=1e-4,
        schedule="fixed",  # adaptive, fixed
        gamma=0.95,
        lam=0.95,
        desired_kl=0.00,
        max_grad_norm=0.5,
    )
