# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg
from isaaclab_assets import ISAACLAB_ASSETS_DATA_DIR

from moleworks_ext import MOLEWORKS_RSC_DIR
from moleworks_ext.common.assets.articulation_ext_force_global_cfg import Articulation_ext_force_globalCfg


# Single Boulder Excavation
_M545_DOF_ARM_USD_W_COLLISION = (
    f"{MOLEWORKS_RSC_DIR}/single_boulder_excavation/m545/model/m545_dof_arm_spread_legs_scene_short_platform.usd"#m545_dof_arm_spread_legs_scene.usd"
)
M545_DOF_ARM_W_COLLISION_CFG = Articulation_ext_force_globalCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_M545_DOF_ARM_USD_W_COLLISION,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.05,  # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=24,
            solver_velocity_iteration_count=24,  # to change
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(
            contact_offset=0.1, rest_offset=0.0
        ),  # 0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(
            0.0,
            0.0,
            0.704,
        ),  # 0.7049000000000001), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        # pos=( 0.0136, -0.0689,   0.521),
        joint_pos={
            # arm
            "J_BOOM": (
                -1.1
            ),  # -0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
            "J_DIPPER": 0.6,  # 1.57, # 2.029131, #
            "J_TELE": 0.1,  # 0.009, #1.1033065, #
            # tool (end effector)
            "J_EE_PITCH":-0.3,  # 0.48247415,#
        },
    ),  # -0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_DIPPER", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=0.95,
)

# Single Boulder Excavation
_M545_DOF_ARM_USD_W_COLLISION_FLAT = (
    f"{MOLEWORKS_RSC_DIR}/single_boulder_excavation/m545/model/m545_dof_arm_spread_legs_scene.usd"
)
M545_DOF_ARM_W_COLLISION_CFG_FLAT = Articulation_ext_force_globalCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_M545_DOF_ARM_USD_W_COLLISION_FLAT,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.05,  # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=24,
            solver_velocity_iteration_count=24,  # to change
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(
            contact_offset=0.1, rest_offset=0.0
        ),  # 0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(
            0.0,
            0.0,
            0.704,
        ),  # 0.7049000000000001), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        # pos=( 0.0136, -0.0689,   0.521),
        joint_pos={
            # arm
            "J_BOOM": (
                -1.1
            ),  # -0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
            "J_DIPPER": 0.6,  # 1.57, # 2.029131, #
            "J_TELE": 0.1,  # 0.009, #1.1033065, #
            # tool (end effector)
            "J_EE_PITCH":-0.3,  # 0.48247415,#
        },
    ),  # -0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_DIPPER", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=0.95,
)

# Single Boulder Excavation
_M545_DOF_ARM_USD_W_COLLISION_SHOVEL = (
    f"{MOLEWORKS_RSC_DIR}/single_boulder_excavation/m545/model/m545_arm_spread_w_shovel/m545_arm_spread_w_shovel.usd"#m545_dof_arm_spread_legs_scene.usd"
)
M545_DOF_ARM_W_COLLISION_SHOVEL_CFG = Articulation_ext_force_globalCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_M545_DOF_ARM_USD_W_COLLISION_SHOVEL,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.05,  # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=24,
            solver_velocity_iteration_count=24,  # to change
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(
            contact_offset=0.1, rest_offset=0.0
        ),  # 0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(
            0.0,
            0.0,
            0.704,
        ),  # 0.7049000000000001), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        # pos=( 0.0136, -0.0689,   0.521),
        joint_pos={
            # arm
            "J_BOOM": (
                -1.1
            ),  # -0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
            "J_STICK": 0.6,  # 1.57, # 2.029131, #
            "J_TELE": 0.1,  # 0.009, #1.1033065, #
            # tool (end effector)
            "J_EE_PITCH":-0.3,  # 0.48247415,#
        },
    ),  # -0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=0.95,
)