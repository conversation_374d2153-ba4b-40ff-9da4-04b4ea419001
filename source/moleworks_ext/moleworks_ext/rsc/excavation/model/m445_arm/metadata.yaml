# Metadata generated by create_usd.sh
generation_info:
  script: ./scripts/create_usd.sh
  timestamp: "2025-03-29 16:14:37"
  input_xacro: "/home/<USER>/git/m4_urdf/urdf/m445.urdf.xacro"
  isaaclab_folder: "/workspace/isaaclab"
  urdf_used_for_conversion: "/home/<USER>/git/m4_urdf/m445_20250329_161413_fixed.urdf"

collider_settings:
  # Colliders explicitly generated during URDF preprocessing (process_urdf.py)
  preprocessed_parts:
"    simple: [wheels]"
"    visual: []"
  # Default collider simplification used during USD conversion (convert_urdf.py)
  usd_conversion_default: convex_decomposition

joint_settings:
  # Parts requested to have their joints set to 'fixed' type (fix_joints.py)
"  fixed_parts: [wheels,steering,chassis,claws,cabin,tool]"
  # USD Conversion settings used:
  merge_joints_flag: "--merge-joints" # Flag used for merging fixed joints
  fix_base_link: false # Was the base link fixed during conversion?
  joint_stiffness: 0.0 # Value passed to convert_urdf.py
  joint_damping: 0.0 # Value passed to convert_urdf.py
  joint_target_type: none # Value passed to convert_urdf.py

# Note: Check Isaac Sim documentation for default behaviors or alternative methods
#       for settings like physics materials, default prim, etc., which were removed
#       due to incompatibility with the convert_urdf.py script version being used.

output_files:
  usd: "m445/m445.usd" # Relative path within the output dir structure
  metadata: "m445/metadata.yaml"
