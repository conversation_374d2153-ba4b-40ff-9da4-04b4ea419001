# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg
from isaaclab_assets import ISAACLAB_ASSETS_DATA_DIR

from moleworks_ext import MOL<PERSON><PERSON><PERSON>KS_RSC_DIR

# Driving
_MOLE_NAV_USD = f"{MOLEWORKS_RSC_DIR}/driving/mole/m445_nav/m445.usd"
MOLE_NAV_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_MOLE_NAV_USD,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1,  # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=19,
            solver_velocity_iteration_count=2,  # to change
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(
            contact_offset=0.008, rest_offset=0.0
        ),  # 0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(
            -20,
            0.0,
            0.0,
        ),  # 0.7049000000000001), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        # pos=( 0.0136, -0.0689,   0.521),
        joint_pos={
            # Steering
            "J_RF_STEER": 0.0,  # 0.48247415,#
            "J_LF_STEER": 0.0,
            "J_LH_STEER": 0.0,
            "J_RH_STEER": 0.0,
            # wheels
            "J_LF_WHEEL": (
                0.0
            ),  # -0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
            "J_LH_WHEEL": 0.0,  # 1.57, # 2.029131, #
            "J_RF_WHEEL": 0.0,  # 0.009, #1.1033065, #
            "J_RH_WHEEL": 0.0,
        },
    ),  # -0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "steering": ImplicitActuatorCfg(
            joint_names_expr=["J_RF_STEER", "J_LF_STEER", "J_LH_STEER", "J_RH_STEER"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=100000,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
        "wheels": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_WHEEL", "J_LH_WHEEL", "J_RF_WHEEL", "J_RH_WHEEL"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=100000.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)

