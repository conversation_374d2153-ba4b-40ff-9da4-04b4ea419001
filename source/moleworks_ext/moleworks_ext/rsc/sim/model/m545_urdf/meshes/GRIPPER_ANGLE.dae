<?xml version="1.0" encoding="utf-8"?>
<COLLADA xmlns="http://www.collada.org/2005/11/COLLADASchema" version="1.4.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <asset>
    <contributor>
      <author>Blender User</author>
      <authoring_tool>Blender 2.79.0 commit date:2018-03-22, commit time:14:10, hash:f4dc9f9d68b</authoring_tool>
    </contributor>
    <created>2019-01-13T13:08:48</created>
    <modified>2019-01-13T13:08:48</modified>
    <unit name="meter" meter="1"/>
    <up_axis>Z_UP</up_axis>
  </asset>
  <library_cameras>
    <camera id="Camera-camera" name="Camera">
      <optics>
        <technique_common>
          <perspective>
            <xfov sid="xfov">49.13434</xfov>
            <aspect_ratio>1.777778</aspect_ratio>
            <znear sid="znear">0.1</znear>
            <zfar sid="zfar">100</zfar>
          </perspective>
        </technique_common>
      </optics>
      <extra>
        <technique profile="blender">
          <shiftx sid="shiftx" type="float">0</shiftx>
          <shifty sid="shifty" type="float">0</shifty>
          <YF_dofdist sid="YF_dofdist" type="float">0</YF_dofdist>
        </technique>
      </extra>
    </camera>
  </library_cameras>
  <library_lights>
    <light id="Lamp-light" name="Lamp">
      <technique_common>
        <point>
          <color sid="color">1 1 1</color>
          <constant_attenuation>1</constant_attenuation>
          <linear_attenuation>0</linear_attenuation>
          <quadratic_attenuation>0.00111109</quadratic_attenuation>
        </point>
      </technique_common>
      <extra>
        <technique profile="blender">
          <type sid="type" type="int">0</type>
          <flag sid="flag" type="int">0</flag>
          <mode sid="mode" type="int">8192</mode>
          <gamma sid="blender_gamma" type="float">1</gamma>
          <red sid="red" type="float">1</red>
          <green sid="green" type="float">1</green>
          <blue sid="blue" type="float">1</blue>
          <shadow_r sid="blender_shadow_r" type="float">0</shadow_r>
          <shadow_g sid="blender_shadow_g" type="float">0</shadow_g>
          <shadow_b sid="blender_shadow_b" type="float">0</shadow_b>
          <energy sid="blender_energy" type="float">1</energy>
          <dist sid="blender_dist" type="float">29.99998</dist>
          <spotsize sid="spotsize" type="float">75</spotsize>
          <spotblend sid="spotblend" type="float">0.15</spotblend>
          <halo_intensity sid="blnder_halo_intensity" type="float">1</halo_intensity>
          <att1 sid="att1" type="float">0</att1>
          <att2 sid="att2" type="float">1</att2>
          <falloff_type sid="falloff_type" type="int">2</falloff_type>
          <clipsta sid="clipsta" type="float">1.000799</clipsta>
          <clipend sid="clipend" type="float">30.002</clipend>
          <bias sid="bias" type="float">1</bias>
          <soft sid="soft" type="float">3</soft>
          <compressthresh sid="compressthresh" type="float">0.04999995</compressthresh>
          <bufsize sid="bufsize" type="int">2880</bufsize>
          <samp sid="samp" type="int">3</samp>
          <buffers sid="buffers" type="int">1</buffers>
          <filtertype sid="filtertype" type="int">0</filtertype>
          <bufflag sid="bufflag" type="int">0</bufflag>
          <buftype sid="buftype" type="int">2</buftype>
          <ray_samp sid="ray_samp" type="int">1</ray_samp>
          <ray_sampy sid="ray_sampy" type="int">1</ray_sampy>
          <ray_sampz sid="ray_sampz" type="int">1</ray_sampz>
          <ray_samp_type sid="ray_samp_type" type="int">0</ray_samp_type>
          <area_shape sid="area_shape" type="int">1</area_shape>
          <area_size sid="area_size" type="float">0.1</area_size>
          <area_sizey sid="area_sizey" type="float">0.1</area_sizey>
          <area_sizez sid="area_sizez" type="float">1</area_sizez>
          <adapt_thresh sid="adapt_thresh" type="float">0.000999987</adapt_thresh>
          <ray_samp_method sid="ray_samp_method" type="int">1</ray_samp_method>
          <shadhalostep sid="shadhalostep" type="int">0</shadhalostep>
          <sun_effect_type sid="sun_effect_type" type="int">0</sun_effect_type>
          <skyblendtype sid="skyblendtype" type="int">1</skyblendtype>
          <horizon_brightness sid="horizon_brightness" type="float">1</horizon_brightness>
          <spread sid="spread" type="float">1</spread>
          <sun_brightness sid="sun_brightness" type="float">1</sun_brightness>
          <sun_size sid="sun_size" type="float">1</sun_size>
          <backscattered_light sid="backscattered_light" type="float">1</backscattered_light>
          <sun_intensity sid="sun_intensity" type="float">1</sun_intensity>
          <atm_turbidity sid="atm_turbidity" type="float">2</atm_turbidity>
          <atm_extinction_factor sid="atm_extinction_factor" type="float">1</atm_extinction_factor>
          <atm_distance_factor sid="atm_distance_factor" type="float">1</atm_distance_factor>
          <skyblendfac sid="skyblendfac" type="float">1</skyblendfac>
          <sky_exposure sid="sky_exposure" type="float">1</sky_exposure>
          <sky_colorspace sid="sky_colorspace" type="int">0</sky_colorspace>
        </technique>
      </extra>
    </light>
  </library_lights>
  <library_images/>
  <library_geometries>
    <geometry id="Gripper_Angle-mesh" name="Gripper Angle">
      <mesh>
        <source id="Gripper_Angle-mesh-positions">
          <float_array id="Gripper_Angle-mesh-positions-array" count="384">318.9591 82.22711 -175 318.9591 82.22711 175 48.63861 154.6593 175 48.63861 154.6593 -175 47.35569 154.9144 175 46.05043 155 175 46.05043 155 -175 47.35569 154.9144 -175 30 155 175 30 155 -175 20.25072 147.2252 175 20 145 175 20 145 -175 20.99031 149.3388 175 20.25072 147.2252 -175 22.18169 151.2349 175 20.99031 149.3388 -175 23.7651 152.8183 175 22.18169 151.2349 -175 25.66116 154.0097 175 23.7651 152.8183 -175 27.77479 154.7493 175 25.66116 154.0097 -175 27.77479 154.7493 -175 20 -118.8607 175 20 -118.8607 -175 33.42184 -128.257 175 35.55734 -127.1743 175 35.55734 -127.1743 -175 31.09019 -128.8011 175 33.42184 -128.257 -175 28.69604 -128.7753 175 31.09019 -128.8011 -175 26.37664 -128.1811 175 28.69604 -128.7753 -175 24.26495 -127.0527 175 26.37664 -128.1811 -175 22.48204 -125.4546 175 24.26495 -127.0527 -175 21.1301 -123.4785 175 22.48204 -125.4546 -175 20.28663 -121.2377 175 21.1301 -123.4785 -175 20.28663 -121.2377 -175 321.9283 64.25424 -175 321.9283 64.25424 175 321.0847 81.38716 175 322.9641 80.0865 175 321.0847 81.38716 -175 324.499 78.39311 175 322.9641 80.0865 -175 325.6094 76.39541 175 324.499 78.39311 -175 326.2372 74.19777 175 325.6094 76.39541 -175 326.3496 71.915 175 326.2372 74.19777 -175 325.9407 69.66632 175 326.3496 71.915 -175 325.032 67.56922 175 325.9407 69.66632 -175 323.6708 65.73322 175 325.032 67.56922 -175 323.6708 65.73322 -175 0.2507209 167.2252 175 0 165 175 0 -156.2869 175 0.9903113 169.3388 175 51.27166 174.6593 175 48.68347 175 175 10 175 175 3.765102 172.8183 175 2.181685 171.2349 175 5.661162 174.0097 175 49.98873 174.9144 175 7.774791 174.7493 175 371.4729 73.63021 175 372.8341 75.4662 175 369.7304 72.15124 175 368.8868 89.28414 175 366.7612 90.1241 175 374.0393 82.09477 175 373.4115 84.2924 175 373.7428 77.56331 175 374.1517 79.81199 175 370.7662 87.9835 175 372.3011 86.2901 175 6.376639 -165.6074 175 8.69604 -166.2015 175 1.130094 -160.9048 175 15.55734 -164.6005 175 4.264953 -164.4789 175 2.482035 -162.8808 175 13.42185 -165.6832 175 0.2866313 -158.664 175 11.09019 -166.2273 175 0 165 -175 0.2507209 167.2252 -175 0.9903113 169.3388 -175 3.765102 172.8183 -175 5.661162 174.0097 -175 2.181685 171.2349 -175 7.774791 174.7493 -175 10 175 -175 48.68347 175 -175 51.27166 174.6593 -175 366.7612 90.1241 -175 15.55734 -164.6005 -175 49.98873 174.9144 -175 372.3011 86.2901 -175 373.4115 84.2924 -175 371.4729 73.63021 -175 369.7304 72.15124 -175 373.7428 77.56331 -175 372.8341 75.4662 -175 370.7662 87.9835 -175 368.8868 89.28414 -175 374.0393 82.09477 -175 374.1517 79.81199 -175 2.482035 -162.8808 -175 1.130094 -160.9048 -175 11.09019 -166.2273 -175 8.69604 -166.2015 -175 4.264953 -164.4789 -175 6.376639 -165.6074 -175 0.2866313 -158.664 -175 0 -156.2869 -175 13.42185 -165.6832 -175</float_array>
          <technique_common>
            <accessor source="#Gripper_Angle-mesh-positions-array" count="128" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <source id="Gripper_Angle-mesh-normals">
          <float_array id="Gripper_Angle-mesh-normals-array" count="507">-0.2588191 -0.9659259 0 -0.06540691 -0.9978587 0 -0.1950892 -0.9807855 0 -0.1950876 -0.9807859 0 -0.06540662 -0.9978588 0 0 -1 0 0.9937123 -0.1119642 0 0.9438837 -0.3302783 0 0.8467234 -0.5320333 0 0.7071072 -0.7071064 0 0.5320327 -0.8467238 0 0.3302811 -0.9438826 0 0.111963 -0.9937124 0 0.1119627 -0.9937125 0 0.3302796 -0.9438832 0 0.7071065 -0.707107 0 0.8467246 -0.5320316 0 0.9937121 -0.1119655 0 1 0 0 -0.4522093 0.8919119 0 -0.2272417 0.9738384 0 0.01077038 0.9999421 0 0.2481517 0.9687212 0 0.4713088 0.8819683 0 0.66745 0.7446547 0 0.8253291 0.5646522 0 0.9358942 0.3522813 0 0.9928084 0.1197147 0 0.9928085 0.1197136 0 0.9358946 0.3522803 0 0.2481512 0.9687214 0 0.01077032 0.9999421 0 -0.2272412 0.9738385 0 -0.4522083 0.8919124 0 -0.5557339 0.8313602 0 -0.555734 0.8313602 0 -0.3675027 -0.9300225 0 -0.5690848 -0.822279 0 -0.7409089 -0.6716056 0 -0.8740596 -0.4858188 0 -0.9615389 -0.2746689 0 -0.9987898 -0.04918336 0 -0.9838674 0.1789 0 -0.9175551 0.3976088 0 -0.8033047 0.5955684 0 -0.6471114 0.7623955 0 -0.6471017 0.7624037 0 -0.917548 0.3976252 0 -0.9838708 0.1788811 0 -0.9987908 -0.04916387 0 -0.9615337 -0.2746869 0 -0.8740513 -0.4858338 0 -0.7409186 -0.6715948 0 -0.5690757 -0.8222852 0 -0.3675161 -0.9300172 0 -4.62305e-6 0 1 -1.51977e-7 0 1 4.19621e-6 0 1 0 0 1 -8.11314e-6 0 1 1.92732e-6 0 1 -4.36367e-6 0 1 -1.25696e-5 0 1 9.86057e-6 0 1 1.54043e-7 0 1 -6.16284e-7 0 1 2.0882e-6 0 1 5.78811e-7 0 1 -2.24754e-7 0 1 2.54846e-6 0 1 -4.63053e-6 0 1 8.11316e-6 0 1 1.85537e-6 0 1 3.72501e-7 0 1 -6.00732e-7 0 1 -8.3155e-6 0 1 1.07196e-6 0 1 3.03682e-6 0 1 -1.41752e-7 0 1 1.22036e-5 0 1 -2.12641e-6 0 1 -1.60981e-6 0 1 6.2631e-7 0 1 -1.13409e-5 0 1 8.729e-6 0 1 -1.67595e-5 0 1 0 0 -1 -9.24607e-6 0 -1 9.70897e-7 0 -1 -1.9769e-6 0 -1 5.77872e-7 0 -1 -4.63608e-6 0 -1 0 0 -1 -8.7273e-6 0 -1 -3.14719e-6 0 -1 -2.31526e-6 0 -1 -1.96191e-6 0 -1 3.27277e-6 0 -1 5.35991e-7 0 -1 1.2641e-6 0 -1 1.8203e-7 0 -1 -1.51884e-5 0 -1 -3.00232e-6 0 -1 -1.04409e-6 0 -1 4.63607e-6 0 -1 2.0442e-6 0 -1 1.15762e-6 0 -1 1.07434e-5 0 -1 1.22006e-6 0 -1 -3.69577e-6 0 -1 -2.50521e-6 0 -1 -1.60981e-6 0 -1 -1.47831e-5 0 -1 4.55524e-6 0 -1 4.69732e-7 0 -1 4.02878e-6 0 -1 -1.67594e-5 0 -1 6.25414e-7 0 -1 1.30171e-5 0 -1 2.84754e-6 0 -1 -1.13409e-5 0 -1 0 1 0 0.2588191 0.9659258 0 0.2588191 0.9659259 0 0.555734 -0.8313602 0 0.5557339 -0.8313602 0 -1 0 0 -0.9937123 0.1119647 0 -0.9438835 0.3302785 0 -0.8467237 0.5320329 0 -0.7071075 0.7071061 0 -0.5320327 0.8467238 0 -0.3302803 0.9438829 0 -0.1119627 0.9937125 0 -0.1119629 0.9937124 0 -0.3302802 0.943883 0 -0.5320326 0.8467239 0 -0.8467239 0.5320329 0 -0.9438835 0.3302785 0 0.4522144 -0.8919094 0 0.2272354 -0.9738399 0 -0.01076394 -0.9999421 0 -0.2481514 -0.9687213 0 -0.4713085 -0.8819685 0 -0.6674537 -0.7446514 0 -0.8253287 -0.5646528 0 -0.9358942 -0.3522813 0 -0.9928084 -0.1197143 0 -0.8253288 -0.5646526 0 0.4522135 -0.8919098 0 0.06540691 0.9978587 0 0.1950876 0.9807859 0 0.1950892 0.9807855 0 0.3675056 0.9300214 0 0.5690734 0.8222867 0 0.7409104 0.6716039 0 0.8740513 0.4858338 0 0.9615335 0.2746878 0 0.9987908 0.04916387 0 0.9838675 -0.1788994 0 0.9175551 -0.3976088 0 0.8033035 -0.59557 0 0.6471017 -0.7624037 0 0.6471114 -0.7623955 0 0.9987898 0.04918336 0 0.9615386 0.2746698 0 0.8740596 0.4858188 0 0.7409201 0.6715932 0 0.5690825 0.8222805 0</float_array>
          <technique_common>
            <accessor source="#Gripper_Angle-mesh-normals-array" count="169" stride="3">
              <param name="X" type="float"/>
              <param name="Y" type="float"/>
              <param name="Z" type="float"/>
            </accessor>
          </technique_common>
        </source>
        <vertices id="Gripper_Angle-mesh-vertices">
          <input semantic="POSITION" source="#Gripper_Angle-mesh-positions"/>
        </vertices>
        <triangles count="256">
          <input semantic="VERTEX" source="#Gripper_Angle-mesh-vertices" offset="0"/>
          <input semantic="NORMAL" source="#Gripper_Angle-mesh-normals" offset="1"/>
          <p>0 0 1 0 2 0 2 0 3 0 0 0 4 1 5 1 6 1 2 2 4 2 7 2 7 3 3 3 2 3 6 4 7 4 4 4 8 5 9 5 6 5 6 5 5 5 8 5 10 6 11 6 12 6 13 7 10 7 14 7 15 8 13 8 16 8 17 9 15 9 18 9 19 10 17 10 20 10 21 11 19 11 22 11 8 12 21 12 23 12 23 13 9 13 8 13 22 14 23 14 21 14 20 10 22 10 19 10 18 15 20 15 17 15 16 16 18 16 15 16 14 7 16 7 13 7 12 17 14 17 10 17 24 18 25 18 12 18 12 18 11 18 24 18 26 19 27 19 28 19 29 20 26 20 30 20 31 21 29 21 32 21 33 22 31 22 34 22 35 23 33 23 36 23 37 24 35 24 38 24 39 25 37 25 40 25 41 26 39 26 42 26 24 27 41 27 43 27 43 28 25 28 24 28 42 29 43 29 41 29 40 25 42 25 39 25 38 24 40 24 37 24 36 23 38 23 35 23 34 30 36 30 33 30 32 31 34 31 31 31 30 32 32 32 29 32 28 33 30 33 26 33 44 34 28 34 27 34 27 35 45 35 44 35 46 36 1 36 0 36 47 37 46 37 48 37 49 38 47 38 50 38 51 39 49 39 52 39 53 40 51 40 54 40 55 41 53 41 56 41 57 42 55 42 58 42 59 43 57 43 60 43 61 44 59 44 62 44 45 45 61 45 63 45 63 46 44 46 45 46 62 44 63 44 61 44 60 47 62 47 59 47 58 48 60 48 57 48 56 49 58 49 55 49 54 50 56 50 53 50 52 51 54 51 51 51 50 52 52 52 49 52 48 53 50 53 47 53 0 54 48 54 46 54 15 55 64 55 65 55 65 56 66 56 24 56 15 57 67 57 64 57 24 58 11 58 65 58 68 58 2 58 1 58 4 58 2 58 68 58 69 59 5 59 4 59 69 58 70 58 8 58 17 58 19 58 70 58 15 60 71 60 72 60 15 58 17 58 73 58 65 61 10 61 13 61 66 62 41 62 24 62 68 58 74 58 4 58 8 58 5 58 69 58 70 58 75 58 17 58 73 58 71 58 15 58 11 63 10 63 65 63 53 58 76 58 77 58 53 64 55 64 78 64 53 58 79 58 80 58 53 58 81 58 82 58 1 58 80 58 68 58 70 65 21 65 8 65 75 58 73 58 17 58 65 66 13 66 15 66 53 67 83 67 84 67 78 68 76 68 53 68 53 69 85 69 79 69 82 70 86 70 53 70 80 58 46 58 47 58 4 71 74 71 69 71 72 58 67 58 15 58 77 58 83 58 53 58 86 58 85 58 53 58 47 58 49 58 80 58 19 58 21 58 70 58 33 72 87 72 88 72 33 58 35 58 89 58 90 73 78 73 45 73 80 58 51 58 53 58 1 58 46 58 80 58 35 58 37 58 66 58 33 74 91 74 87 74 89 58 92 58 33 58 33 75 93 75 90 75 78 76 61 76 45 76 49 58 51 58 80 58 66 58 94 58 35 58 92 58 91 58 33 58 33 77 95 77 93 77 45 78 27 78 90 78 66 79 39 79 41 79 88 58 95 58 33 58 90 80 29 80 31 80 78 81 59 81 61 81 78 82 55 82 57 82 66 83 37 83 39 83 90 84 31 84 33 84 57 58 59 58 78 58 94 58 89 58 35 58 27 85 26 85 90 85 26 58 29 58 90 58 84 58 81 58 53 58 18 86 16 86 96 86 96 87 97 87 18 87 97 86 98 86 18 86 18 88 99 88 100 88 18 89 98 89 101 89 20 90 102 90 103 90 104 86 6 86 9 86 7 86 6 86 104 86 105 91 3 91 7 91 105 92 106 92 0 92 107 86 28 86 44 86 16 93 14 93 96 93 101 86 99 86 18 86 20 94 100 94 102 94 104 86 108 86 7 86 0 86 3 86 105 86 56 95 109 95 110 95 56 86 54 86 106 86 56 86 111 86 112 86 56 86 113 86 114 86 44 86 112 86 107 86 100 96 20 96 18 96 103 97 23 97 22 97 9 86 103 86 104 86 106 98 48 98 0 98 56 99 115 99 109 99 106 86 116 86 56 86 114 100 111 100 56 100 56 86 117 86 118 86 107 101 30 101 28 101 36 86 119 86 120 86 36 102 121 102 122 102 103 103 22 103 20 103 7 104 108 104 105 104 106 105 52 105 50 105 116 86 115 86 56 86 118 106 113 106 56 106 112 86 62 86 60 86 44 86 63 86 112 86 36 107 34 107 107 107 36 108 123 108 119 108 122 86 124 86 36 86 38 109 125 109 126 109 96 86 12 86 25 86 9 86 23 86 103 86 106 110 54 110 52 110 112 86 58 86 56 86 63 111 62 111 112 111 107 112 127 112 36 112 124 86 123 86 36 86 38 113 120 113 125 113 25 86 126 86 96 86 50 86 48 86 106 86 60 114 58 114 112 114 127 86 121 86 36 86 126 115 40 115 38 115 25 116 43 116 126 116 110 86 117 86 56 86 120 117 38 117 36 117 43 118 42 118 126 118 107 119 32 119 30 119 42 120 40 120 126 120 34 86 32 86 107 86 14 86 12 86 96 86 69 121 104 121 103 121 103 121 70 121 69 121 80 122 106 122 105 122 105 123 68 123 80 123 78 124 90 124 107 124 107 125 112 125 78 125 126 126 66 126 65 126 65 126 96 126 126 126 96 127 65 127 64 127 97 128 64 128 67 128 98 129 67 129 72 129 101 130 72 130 71 130 99 131 71 131 73 131 100 132 73 132 75 132 102 133 75 133 70 133 70 134 103 134 102 134 75 135 102 135 100 135 73 136 100 136 99 136 71 130 99 130 101 130 72 137 101 137 98 137 67 138 98 138 97 138 64 127 97 127 96 127 107 139 90 139 93 139 127 140 93 140 95 140 121 141 95 141 88 141 122 142 88 142 87 142 124 143 87 143 91 143 123 144 91 144 92 144 119 145 92 145 89 145 120 146 89 146 94 146 125 147 94 147 66 147 66 147 126 147 125 147 94 146 125 146 120 146 89 148 120 148 119 148 92 144 119 144 123 144 91 143 123 143 124 143 87 142 124 142 122 142 88 141 122 141 121 141 95 140 121 140 127 140 93 149 127 149 107 149 104 150 69 150 74 150 108 151 74 151 68 151 68 152 105 152 108 152 74 150 108 150 104 150 106 153 80 153 79 153 116 154 79 154 85 154 115 155 85 155 86 155 109 156 86 156 82 156 110 157 82 157 81 157 117 158 81 158 84 158 118 159 84 159 83 159 113 160 83 160 77 160 114 161 77 161 76 161 111 162 76 162 78 162 78 163 112 163 111 163 76 161 111 161 114 161 77 160 114 160 113 160 83 159 113 159 118 159 84 164 118 164 117 164 81 165 117 165 110 165 82 166 110 166 109 166 86 167 109 167 115 167 85 168 115 168 116 168 79 153 116 153 106 153</p>
        </triangles>
      </mesh>
    </geometry>
  </library_geometries>
  <library_controllers/>
  <library_visual_scenes>
    <visual_scene id="Scene" name="Scene">
      <node id="Camera" name="Camera" type="NODE">
        <matrix sid="transform">0.6859207 -0.3240135 0.6515582 7.481132 0.7276763 0.3054208 -0.6141704 -6.50764 0 0.8953956 0.4452714 5.343665 0 0 0 1</matrix>
        <instance_camera url="#Camera-camera"/>
      </node>
      <node id="Lamp" name="Lamp" type="NODE">
        <matrix sid="transform">-0.2908646 -0.7711008 0.5663932 4.076245 0.9551712 -0.1998834 0.2183912 1.005454 -0.05518906 0.6045247 0.7946723 5.903862 0 0 0 1</matrix>
        <instance_light url="#Lamp-light"/>
      </node>
      <node id="Gripper_Angle" name="Gripper_Angle" type="NODE">
        <matrix sid="transform">0.001 0 0 0 0 0.001 0 0 0 0 0.001 0 0 0 0 1</matrix>
        <instance_geometry url="#Gripper_Angle-mesh" name="Gripper_Angle"/>
      </node>
    </visual_scene>
  </library_visual_scenes>
  <scene>
    <instance_visual_scene url="#Scene"/>
  </scene>
</COLLADA>