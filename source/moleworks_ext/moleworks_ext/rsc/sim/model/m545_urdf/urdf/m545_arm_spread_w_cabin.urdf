<?xml version="1.0" encoding="utf-8"?>
<!-- =================================================================================== -->
<!-- |    This document was autogenerated by xacro from /home/<USER>/catkin_workspaces/menzi_ws/src/m545_common/m545_description/urdf/m545.urdf.xacro | -->
<!-- |    EDITING THIS FILE BY HAND IS NOT RECOMMENDED                                 | -->
<!-- =================================================================================== -->
<robot name="M545" xmlns:xacro="http://www.ros.org/wiki/xacro">
  <!--         		-->
  <!-- M545 Excavator -->
  <!--         		-->
  <!--     Front		-->
  <!-- 2*0.027604832 -->
  <!--     Hind		-->
  <!-- 1.5*-2.538998962 -->
  <!--     Chassis/Cabin		-->
  <!-- Parameters -->
<!--  <static>false</static>-->

<!--  <link name="world"/>-->
<!--  &lt;!&ndash; Fixed Based Joint &ndash;&gt;-->
<!--  <joint name="RXYZ" type="fixed">-->
<!--    <origin rpy="0.0 0.0 0.0" xyz="0.0 0.0 1.3"/>-->
<!--    <parent link="world"/>-->
<!--    <child link="BASE"/>-->
<!--  </joint>-->

  <link name="BASE">
    <visual>
      <geometry>
        <mesh filename="../meshes/BASE.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <!-- <collision>
      <geometry>
        <box size="1.15 1.8 0.475"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.0 -0.0 0.225"/>
    </collision> -->
  <!-- </link> -->
  <!-- Dummy inertia link, because KDL cannot have inertia on the base link -->
  <!-- <link name="BASE_inertia"> -->
    <inertial>
      <origin rpy="0 0 0" xyz="-0.011047979 -0.001668192 0.164556349"/>
      <mass value="1700"/>
      <inertia ixx="712.367628108" ixy="0.063171691" ixz="-0.923139486" iyy="294.811317005" iyz="0.440023384" izz="941.703378555"/>
    </inertial>
  </link>
  <!-- Fixed joint to add dummy inertia link -->
  <!-- <joint name="BASE_to_BASE_inertia" type="fixed">
    <parent link="BASE"/>
    <child link="BASE_inertia"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint> -->
  <!--         		-->
  <!-- M545 Excavator -->
  <!--         		-->
  <!--     Front		-->
  <!-- 2*0.027604832 -->
  <!--     Hind		-->
  <!-- 1.5*-2.538998962 -->
  <!--     Chassis/Cabin		-->
  <link name="LF_SWIVEL">
    <visual>
      <geometry>
        <mesh filename="../meshes/LF_SWIVEL.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.163191642 0.020092442 0.048603248"/>
      <mass value="115"/>
      <inertia ixx="4.398905519" ixy="0.435031339" ixz="0.186769766" iyy="6.276839457" iyz="-0.325429239" izz="4.062523726"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.58 0.22 0.5"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.18 0 0"/>
    </collision> -->
  </link>
  <joint name="J_LF_HAA" type="fixed">
    <parent link="BASE"/>
    <child link="LF_SWIVEL"/>
    <origin xyz="0.550 0.85 0.1335" rpy="0 0 0.524"/>
    <axis xyz="0 0 1"/>
    <!-- true if reflect=1-->
    <limit effort="1500000" lower="-0.5201" upper="0.7802" velocity="6.28"/>
    <!-- LF -->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LF_BEAM">
    <visual>
      <geometry>
        <mesh filename="../meshes/LF_BEAM.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.584124663 0.0 0.06694352"/>
      <mass value="200"/>
      <inertia ixx="3.428979601" ixy="-0.211389264" ixz="-0.112828418" iyy="32.455741081" iyz="-0.00050057" izz="30.021583564"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="1.1 0.28 0.25"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.6 0.0 -0.025"/>
    </collision> -->
  </link>
  <joint name="J_LF_HFE" type="fixed">
    <parent link="LF_SWIVEL"/>
    <child link="LF_BEAM"/>
    <origin xyz="0.28 0.0011 -0.0895" rpy="0 0.1 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.3159" upper="0.8238" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LF_PARALLEL">
    <visual>
      <geometry>
        <mesh filename="../meshes/LF_PARALLEL.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.611997693 0.0 0.014937547"/>
      <mass value="75"/>
      <inertia ixx="0.764419514" ixy="-0.000525074" ixz="0.066640819" iyy="7.442807074" iyz="-0.000675307" izz="8.063270252"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="1.3 0.28 0.1"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.6 0.0 0.01"/>
    </collision> -->
  </link>
  <joint name="J_LF_PARALLEL2" type="fixed">
    <parent link="LF_SWIVEL"/>
    <child link="LF_PARALLEL"/>
    <origin xyz="0.4249 0.0017 0.2105" rpy="0 0.1 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.3159" upper="0.8238" velocity="6.28"/>
    <mimic joint="J_LF_HFE" multiplier="1" offset="0"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LF_BEARING">
    <visual>
      <geometry>
        <mesh filename="../meshes/LF_BEARING.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.145288714 -0.057062246 0.045380647"/>
      <mass value="100"/>
      <inertia ixx="3.490730027" ixy="-0.367098034" ixz="0.050497166" iyy="2.470939089" iyz="0.283572413" izz="2.21999363"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.18 0.3 0.325"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.2 0.2 0"/>
    </collision>
    <collision>
      <geometry>
        <box size="0.275 0.275 0.525"/>
      </geometry>
      <origin rpy="0 0.5 0" xyz="0.125 0.025 0.1"/>
    </collision> -->
  </link>
  <joint name="J_LF_PARALLEL" type="fixed">
    <parent link="LF_BEAM"/>
    <child link="LF_BEARING"/>
    <origin xyz="1.2 0.0 0.0" rpy="0 -0.1 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.8238" upper="0.3159" velocity="6.28"/>
    <mimic joint="J_LF_HFE" multiplier="-1" offset="0"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LF_KNUCKLE">
    <visual>
      <geometry>
        <mesh filename="../meshes/LF_KNUCKLE.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.256156551 0.082176716 0.030358334"/>
      <mass value="270"/>
      <inertia ixx="6.9755496" ixy="0.055209664" ixz="0.00675143" iyy="10.94537266" iyz="-0.003582388" izz="12.678481354"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.35 0.15 0.35"/>
      </geometry>
      <origin rpy="0 0 0.825" xyz="0.075 0.075 0"/>
    </collision>
    <collision>
      <geometry>
        <box size="0.4 0.3 0.2"/>
      </geometry>
      <origin rpy="0 0 0.3" xyz="0.35 0 0"/>
    </collision>
    <collision>
      <geometry>
        <cylinder length="0.35" radius="0.18"/>
      </geometry>
      <origin rpy="1.57 0 0" xyz="0.275 0.2 0"/>
    </collision> -->
  </link>
  <joint name="J_LF_STEER" type="fixed">
    <parent link="LF_BEARING"/>
    <child link="LF_KNUCKLE"/>
    <origin xyz="0.235 0.298 -0.003"/>
    <axis xyz="0 0 1"/>
    <!-- true if reflect=1-->
    <limit effort="1500000" lower="-0.8517" upper="0.8115" velocity="6.28"/>
    <!-- LF -->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LF_WHEEL">
    <visual>
      <geometry>
        <mesh filename="../meshes/LF_WHEEL.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.007923201 0"/>
      <mass value="280"/>
      <inertia ixx="38.817750093" ixy="0" ixz="0" iyy="62.372189183" iyz="0" izz="38.815038925"/>
    </inertial>
    <collision>
      <geometry>
        <sphere radius="0.6296"/>
        <!-- <cylinder length="0.01" radius="0.6296"/> -->
      </geometry>
      <origin rpy="1.57 0 0" xyz="0 0 0"/>
    </collision>
  </link>
  <joint name="J_LF_WHEEL" type="fixed">
    <parent link="LF_KNUCKLE"/>
    <child link="LF_WHEEL"/>
    <origin xyz="0.27 0.471 0.003"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LF_WHEEL_CONTACT"/>
  <joint name="LF_WHEEL_TO_LF_WHEEL_CONTACT" type="fixed">
    <parent link="LF_WHEEL"/>
    <child link="LF_WHEEL_CONTACT"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <link name="LH_ROTATOR">
    <visual>
      <geometry>
        <mesh filename="../meshes/LH_ROTATOR.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.090832234 0.002744135 0.144031103"/>
      <mass value="115"/>
      <inertia ixx="3.867796397" ixy="0.203079468" ixz="0.25308155" iyy="2.672208384" iyz="0.13501603" izz="2.648139472"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.3607 0.404 0.4"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.05 0.0 0.12"/>
    </collision> -->
  </link>
  <joint name="J_LH_HFE" type="fixed">
    <parent link="BASE"/>
    <child link="LH_ROTATOR"/>
    <origin xyz="-0.640 0.755 0.060" rpy="0 -0.165 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.8343" upper="0.2618" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LH_BEAM">
    <visual>
      <geometry>
        <mesh filename="../meshes/LH_BEAM.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.48851373 -0.042755794 -0.038331283"/>
      <mass value="280"/>
      <inertia ixx="7.06360381" ixy="-2.077029642" ixz="-1.804851306" iyy="24.488420582" iyz="0.498393128" izz="27.012076439"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.95 0.45 0.3"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.5 -0.15 0.05"/>
    </collision> -->
  </link>
  <joint name="J_LH_HAA" type="fixed">
    <parent link="LH_ROTATOR"/>
    <child link="LH_BEAM"/>
    <origin xyz="-0.205 0.102 0.12" rpy="0 0 -0.35"/>
    <axis xyz="0 0 1"/>
    <!-- true if reflect=1-->
    <limit effort="1500000" lower="-0.731292957" upper="0.542797397" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LH_KNUCKLE">
    <visual>
      <geometry>
        <mesh filename="../meshes/LH_KNUCKLE.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.419372367 0.213687621 0.056306222"/>
      <mass value="370"/>
      <inertia ixx="18.225198614" ixy="-3.808498443" ixz="-0.992617577" iyy="28.156282278" iyz="2.506100865" izz="29.142770892"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.8 0.2 0.55"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.4 0.0 0.125"/>
    </collision> -->
  </link>
  <joint name="J_LH_STEER" type="fixed">
    <parent link="LH_BEAM"/>
    <child link="LH_KNUCKLE"/>
    <origin xyz="-0.975 0.0 -0.045"/>
    <axis xyz="0 0 1"/>
    <!-- true if reflect=1-->
    <limit effort="1500000" lower="-0.816743" upper="1.2863077" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LH_WHEEL">
    <visual>
      <geometry>
        <mesh filename="../meshes/LH_WHEEL.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0 0.007923201 0"/>
      <mass value="280"/>
      <inertia ixx="38.817750093" ixy="0" ixz="0" iyy="62.372189183" iyz="0" izz="38.815038925"/>
    </inertial>
    <collision>
      <geometry>
        <sphere radius="0.6296"/>
        <!-- <cylinder length="0.01" radius="0.6296"/> -->
      </geometry>
      <origin rpy="1.57 0 0" xyz="0 0 0"/>
    </collision>
  </link>
  <joint name="J_LH_WHEEL" type="fixed">
    <parent link="LH_KNUCKLE"/>
    <child link="LH_WHEEL"/>
    <origin xyz="-0.5 0.398 0.0305"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="LH_WHEEL_CONTACT"/>
  <joint name="LH_WHEEL_TO_LH_WHEEL_CONTACT" type="fixed">
    <parent link="LH_WHEEL"/>
    <child link="LH_WHEEL_CONTACT"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <link name="LH_OUTRIGGER">
    <visual>
      <geometry>
        <mesh filename="../meshes/LH_OUTRIGGER.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.555493105 0 -0.140022870"/>
      <mass value="110"/>
      <inertia ixx="2.214139767" ixy="0" ixz="0.453481428" iyy="5.356489473" iyz="0" izz="6.037643655"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.35 0.45 0.4"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.725 0 -0.2"/>
    </collision>
    <collision>
      <geometry>
        <box size="0.525 0.15 0.16"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.325 0 -0.125"/>
    </collision> -->
  </link>
  <joint name="J_LH_CLAW" type="fixed">
    <parent link="LH_KNUCKLE"/>
    <child link="LH_OUTRIGGER"/>
    <origin xyz="-0.7921 0.019 -0.0105" rpy="0 0.785 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-1.57" upper="1.57" velocity="6.28"/>
    <!-- TODO -->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <!--         		-->
  <!-- M545 Excavator -->
  <!--         		-->
  <!--     Front		-->
  <!-- 2*0.027604832 -->
  <!--     Hind		-->
  <!-- 1.5*-2.538998962 -->
  <!--     Chassis/Cabin		-->
  <link name="RF_SWIVEL">
    <visual>
      <geometry>
        <mesh filename="../meshes/RF_SWIVEL.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.163191642 -0.020092442 0.048603248"/>
      <mass value="115"/>
      <inertia ixx="4.398905519" ixy="-0.435031339" ixz="0.186769766" iyy="6.276839457" iyz="0.325429239" izz="4.062523726"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.58 0.22 0.5"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.18 0 0"/>
    </collision> -->
  </link>
  <joint name="J_RF_HAA" type="fixed">
    <parent link="BASE"/>
    <child link="RF_SWIVEL"/>
    <origin xyz="0.550 -0.85 0.1335" rpy="0 0 -0.524"/>
    <axis xyz="0 0 1"/>
    <!-- true if reflect=-1-->
    <limit effort="1500000" lower="-0.7802" upper="0.5201" velocity="6.28"/>
    <!-- RF -->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RF_BEAM">
    <visual>
      <geometry>
        <mesh filename="../meshes/RF_BEAM.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.584124663 -0.0 0.06694352"/>
      <mass value="200"/>
      <inertia ixx="3.428979601" ixy="0.211389264" ixz="-0.112828418" iyy="32.455741081" iyz="0.00050057" izz="30.021583564"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="1.1 0.28 0.25"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.6 -0.0 -0.025"/>
    </collision> -->
  </link>
  <joint name="J_RF_HFE" type="fixed">
    <parent link="RF_SWIVEL"/>
    <child link="RF_BEAM"/>
    <origin xyz="0.28 -0.0011 -0.0895" rpy="0 0.1 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.3159" upper="0.8238" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RF_PARALLEL">
    <visual>
      <geometry>
        <mesh filename="../meshes/RF_PARALLEL.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.611997693 -0.0 0.014937547"/>
      <mass value="75"/>
      <inertia ixx="0.764419514" ixy="0.000525074" ixz="0.066640819" iyy="7.442807074" iyz="0.000675307" izz="8.063270252"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="1.3 0.28 0.1"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.6 -0.0 0.01"/>
    </collision> -->
  </link>
  <joint name="J_RF_PARALLEL2" type="fixed">
    <parent link="RF_SWIVEL"/>
    <child link="RF_PARALLEL"/>
    <origin xyz="0.4249 -0.0017 0.2105" rpy="0 0.1 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.3159" upper="0.8238" velocity="6.28"/>
    <mimic joint="J_RF_HFE" multiplier="1" offset="0"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RF_BEARING">
    <visual>
      <geometry>
        <mesh filename="../meshes/RF_BEARING.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.145288714 0.057062246 0.045380647"/>
      <mass value="100"/>
      <inertia ixx="3.490730027" ixy="0.367098034" ixz="0.050497166" iyy="2.470939089" iyz="-0.283572413" izz="2.21999363"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.18 0.3 0.325"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.2 -0.2 0"/>
    </collision>
    <collision>
      <geometry>
        <box size="0.275 0.275 0.525"/>
      </geometry>
      <origin rpy="0 0.5 0" xyz="0.125 -0.025 0.1"/>
    </collision> -->
  </link>
  <joint name="J_RF_PARALLEL" type="fixed">
    <parent link="RF_BEAM"/>
    <child link="RF_BEARING"/>
    <origin xyz="1.2 -0.0 0.0" rpy="0 -0.1 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.8238" upper="0.3159" velocity="6.28"/>
    <mimic joint="J_RF_HFE" multiplier="-1" offset="0"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RF_KNUCKLE">
    <visual>
      <geometry>
        <mesh filename="../meshes/RF_KNUCKLE.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.256156551 -0.082176716 0.030358334"/>
      <mass value="270"/>
      <inertia ixx="6.9755496" ixy="-0.055209664" ixz="0.00675143" iyy="10.94537266" iyz="0.003582388" izz="12.678481354"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.35 0.15 0.35"/>
      </geometry>
      <origin rpy="0 0 -0.825" xyz="0.075 -0.075 0"/>
    </collision>
    <collision>
      <geometry>
        <box size="0.4 0.3 0.2"/>
      </geometry>
      <origin rpy="0 0 -0.3" xyz="0.35 0 0"/>
    </collision>
    <collision>
      <geometry>
        <cylinder length="0.35" radius="0.18"/>
      </geometry>
      <origin rpy="1.57 0 0" xyz="0.275 -0.2 0"/>
    </collision> -->
  </link>
  <joint name="J_RF_STEER" type="fixed">
    <parent link="RF_BEARING"/>
    <child link="RF_KNUCKLE"/>
    <origin xyz="0.235 -0.298 -0.003"/>
    <axis xyz="0 0 1"/>
    <!-- true if reflect=-1-->
    <limit effort="1500000" lower="-0.8115" upper="0.8517" velocity="6.28"/>
    <!-- RF -->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RF_WHEEL">
    <visual>
      <geometry>
        <mesh filename="../meshes/RF_WHEEL.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.007923201 0"/>
      <mass value="280"/>
      <inertia ixx="38.817750093" ixy="0" ixz="0" iyy="62.372189183" iyz="0" izz="38.815038925"/>
    </inertial>
    <collision>
      <geometry>
        <!-- <cylinder length="0.01" radius="0.6296"/> -->
        <sphere radius="0.6296"/>
      </geometry>
      <origin rpy="1.57 0 0" xyz="0 0 0"/>
    </collision>
  </link>
  <joint name="J_RF_WHEEL" type="fixed">
    <parent link="RF_KNUCKLE"/>
    <child link="RF_WHEEL"/>
    <origin xyz="0.27 -0.471 0.003"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RF_WHEEL_CONTACT"/>
  <joint name="RF_WHEEL_TO_RF_WHEEL_CONTACT" type="fixed">
    <parent link="RF_WHEEL"/>
    <child link="RF_WHEEL_CONTACT"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <link name="RH_ROTATOR">
    <visual>
      <geometry>
        <mesh filename="../meshes/RH_ROTATOR.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.090832234 -0.002744135 0.144031103"/>
      <mass value="115"/>
      <inertia ixx="3.867796397" ixy="-0.203079468" ixz="0.25308155" iyy="2.672208384" iyz="-0.13501603" izz="2.648139472"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.3607 0.404 0.4"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.05 -0.0 0.12"/>
    </collision> -->
  </link>
  <joint name="J_RH_HFE" type="fixed">
    <parent link="BASE"/>
    <child link="RH_ROTATOR"/>
    <origin xyz="-0.640 -0.755 0.060" rpy="0 -0.165 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.8343" upper="0.2618" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RH_BEAM">
    <visual>
      <geometry>
        <mesh filename="../meshes/RH_BEAM.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.48851373 0.042755794 -0.038331283"/>
      <mass value="280"/>
      <inertia ixx="7.06360381" ixy="2.077029642" ixz="-1.804851306" iyy="24.488420582" iyz="-0.498393128" izz="27.012076439"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.95 0.45 0.3"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.5 0.15 0.05"/>
    </collision> -->
  </link>
  <joint name="J_RH_HAA" type="fixed">
    <parent link="RH_ROTATOR"/>
    <child link="RH_BEAM"/>
    <origin xyz="-0.205 -0.102 0.12" rpy="0 0 0.35"/>
    <axis xyz="0 0 1"/>
    <!-- true if reflect=-1-->
    <limit effort="1500000" lower="-0.542797397" upper="0.731292957" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RH_KNUCKLE">
    <visual>
      <geometry>
        <mesh filename="../meshes/RH_KNUCKLE.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.419372367 -0.213687621 0.056306222"/>
      <mass value="370"/>
      <inertia ixx="18.225198614" ixy="3.808498443" ixz="-0.992617577" iyy="28.156282278" iyz="-2.506100865" izz="29.142770892"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.8 0.2 0.55"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.4 -0.0 0.125"/>
    </collision> -->
  </link>
  <joint name="J_RH_STEER" type="fixed">
    <parent link="RH_BEAM"/>
    <child link="RH_KNUCKLE"/>
    <origin xyz="-0.975 -0.0 -0.045"/>
    <axis xyz="0 0 1"/>
    <!-- true if reflect=-1-->
    <limit effort="1500000" lower="-1.2863077" upper="0.816743" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RH_WHEEL">
    <visual>
      <geometry>
        <mesh filename="../meshes/RH_WHEEL.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0 -0.007923201 0"/>
      <mass value="280"/>
      <inertia ixx="38.817750093" ixy="0" ixz="0" iyy="62.372189183" iyz="0" izz="38.815038925"/>
    </inertial>
    <collision>
      <geometry>
        <sphere radius="0.6296"/>
        <!-- <cylinder length="0.01" radius="0.6296"/> -->
      </geometry>
      <origin rpy="1.57 0 0" xyz="0 0 0"/>
    </collision>
  </link>
  <joint name="J_RH_WHEEL" type="fixed">
    <parent link="RH_KNUCKLE"/>
    <child link="RH_WHEEL"/>
    <origin xyz="-0.5 -0.398 0.0305"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="RH_WHEEL_CONTACT"/>
  <joint name="RH_WHEEL_TO_RH_WHEEL_CONTACT" type="fixed">
    <parent link="RH_WHEEL"/>
    <child link="RH_WHEEL_CONTACT"/>
    <origin rpy="0 0 0" xyz="0 0 0"/>
  </joint>
  <link name="RH_OUTRIGGER">
    <visual>
      <geometry>
        <mesh filename="../meshes/RH_OUTRIGGER.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.555493105 0 -0.140022870"/>
      <mass value="110"/>
      <inertia ixx="2.214139767" ixy="0" ixz="0.453481428" iyy="5.356489473" iyz="0" izz="6.037643655"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.35 0.45 0.4"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.725 0 -0.2"/>
    </collision>
    <collision>
      <geometry>
        <box size="0.525 0.15 0.16"/>
      </geometry>
      <origin rpy="0 0 0" xyz="-0.325 0 -0.125"/>
    </collision> -->
  </link>
  <joint name="J_RH_CLAW" type="fixed">
    <parent link="RH_KNUCKLE"/>
    <child link="RH_OUTRIGGER"/>
    <origin xyz="-0.7921 -0.019 -0.0105" rpy="0 0.785 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-1.57" upper="1.57" velocity="6.28"/>
    <!-- TODO -->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="CABIN">
    <visual>
      <geometry>
        <mesh filename="../meshes/CABIN.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="-0.3727 0.1 0.9551"/>
      <mass value="4400"/>
      <inertia ixx="2485.29" ixy="0" ixz="0" iyy="1640.58" iyz="0" izz="1600.99"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="1.6 2.0 0.5"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0.25"/>
    </collision>
    <collision>
      <geometry>
        <box size="1.7 1.45 1.3"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.0 0.25 1.1"/>
    </collision>
    <collision>
      <geometry>
        <cylinder length="2.0" radius="0.425"/>
      </geometry>
      <origin rpy="1.57 0 0" xyz="-0.8 0 0.6"/>
    </collision> -->
  </link>
  <joint name="J_TURN" type="continuous">
    <parent link="BASE"/>
    <child link="CABIN"/>
    <origin xyz="0 0 0.516" rpy="0 0 0"/>
    <axis xyz="0 0 1"/>
    <limit effort="1500000" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="BOOM">
    <visual>
      <geometry>
        <mesh filename="../meshes/BOOM.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="1.38936 -0.0004 0.0178046"/>
      <mass value="1400"/>
      <inertia ixx="62.7074" ixy="2.3197" ixz="89.1783" iyy="3291.58" iyz="0.0295" izz="3291.58"/>
    </inertial>
    <!-- <collision> -->
      <!-- <geometry> -->
        <!-- <cylinder length="3.0" radius="0.2"/> -->
        <!-- <box size="3 0.4 0.25"/> -->
      <!-- </geometry> -->
      <!-- <origin rpy="0 1.570796 0" xyz="1.3 0.0 -0.05"/> -->
      <!-- <origin rpy="0 0 0" xyz="1.3 0.0 -0.05"/> -->
    <!-- </collision> -->
  </link>
  <joint name="J_BOOM" type="revolute">
    <parent link="CABIN"/>
    <child link="BOOM"/>
    <origin xyz="0.703 -0.292 0.5494"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-1.34" upper="0.44" velocity="6.28"/>
<!--    <limit effort="1500000" lower="-1.0" upper="0.4" velocity="0.01"/>-->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="STICK">
    <visual>
      <geometry>
        <mesh filename="../meshes/STICK.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0492387 0.000321755 0.0561925"/>
      <mass value="1222.46"/>
      <inertia ixx="12.198" ixy="0.2074" ixz="1.257" iyy="388.5" iyz="-0.0002" izz="388.5"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="2.7 0.25 0.35"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.15 0.0 0.32"/>
    </collision> -->
  </link>
  <joint name="J_STICK" type="revolute">
    <parent link="BOOM"/>
    <child link="STICK"/>
    <origin xyz="2.84 0.0 0.0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="0.58" upper="2.78" velocity="6.28"/>
<!--    <limit effort="1500000" lower="0.58" upper="1.0" velocity="0.1"/>-->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="TELE">
    <visual>
      <geometry>
        <mesh filename="../meshes/TELE.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="1.57108 0.0 0.315267"/>
      <mass value="526.462"/>
      <inertia ixx="48.2112" ixy="-4.2332" ixz="40.1757" iyy="2345.62" iyz="3.0797" izz="2345.62"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="2.8 0.25 0.3"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.6 0.0189 0.0"/>
    </collision>
    <collision>
      <geometry>
        <box size="1.8 0.45 0.4"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.85 0.1 0.375"/>
    </collision> -->
  </link>
  <joint name="J_TELE" type="prismatic">
    <parent link="STICK"/>
    <child link="TELE"/>
    <origin xyz="0 0 0.369"/>
    <axis xyz="1 0 0"/>
    <limit effort="1500000" lower="0" upper="1.8" velocity="10"/>
<!--    <limit effort="1500000" lower="0" upper="1.8" velocity="0.01"/>-->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="ROTO_BASE">
    <visual>
      <geometry>
        <mesh filename="../meshes/ROTO_BASE.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.059285 0.00330444 0.387339"/>
      <mass value="128"/>
      <inertia ixx="5.868" ixy="-0.038" ixz="0.131" iyy="4.22285" iyz="0.115" izz="2.543"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.44 0.65 0.77"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.101 -0.001 0.301"/>
    </collision> -->
  </link>
  <joint name="J_EE_PITCH" type="revolute">
    <parent link="TELE"/>
    <child link="ROTO_BASE"/>
    <origin xyz="2.0266 0 0"/>
    <axis xyz="0 1 0"/>
    <limit effort="1500000" lower="-0.59" upper="2.32" velocity="6.28"/>
<!--    <limit effort="1500000" lower="-0.35" upper="2.0" velocity="0.1"/>-->
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="ROTO">
    <visual>
      <geometry>
        <mesh filename="../meshes/ROTO.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0.0225"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0749009 -0.00404749  0.00135156"/>
      <mass value="260.0"/>
      <inertia ixx="5.289" ixy="0.019" ixz="-0.019" iyy="3.868" iyz="-0.124" izz="2.193"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.27 0.56 0.68"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.051 0.009 0.0"/>
    </collision> -->
  </link>
  <joint name="J_EE_ROLL" type="fixed">
    <parent link="ROTO_BASE"/>
    <child link="ROTO"/>
    <origin xyz="0.263 0 0.342"/>
    <axis xyz="0 0 1"/>
    <limit effort="40500" lower="-0.698" upper="0.698" velocity="6.28"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="ENDEFFECTOR">
    <visual>
      <geometry>
        <mesh filename="../meshes/ENDEFFECTOR.dae"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0 0 0"/>
    </visual>
    <inertial>
      <origin rpy="0 0 0" xyz="0.0987 0.0000 0.0266"/>
      <mass value="125"/>
      <inertia ixx="1.806" ixy="0.0" ixz="-0.027" iyy="1.391" iyz="0.0" izz="0.848"/>
    </inertial>
    <!-- <collision>
      <geometry>
        <box size="0.27 0.56 0.68"/>
      </geometry>
      <origin rpy="0 0 0" xyz="0.127 0 0.044"/>
    </collision> -->
  </link>
  <joint name="J_EE_YAW" type="fixed">
    <parent link="ROTO"/>
    <child link="ENDEFFECTOR"/>
    <origin xyz="0.147 0 0"/>
    <axis xyz="1 0 0"/>
    <limit effort="8200" velocity="0.898"/>
    <dynamics damping="0.0" friction="0.0"/>
  </joint>
  <link name="SHOVEL">
    <inertial>
      <origin rpy="0 0 0" xyz="0.15 0.00015 0.147"/>
      <mass value="480"/>
      <inertia ixx="90.956" ixy="-0.03157" ixz="-7.54324" iyy="70.6513" iyz="-0.01441" izz="70.6513"/>
    </inertial>
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <mesh filename="../meshes/SHOVEL.dae"/>
      </geometry>
    </visual>
    <!-- <collision> -->
      <!-- <origin rpy="0 -0.25 0" xyz="0.40 0 0.21"/> -->
      <!-- <geometry> -->
        <!-- <box size="0.75 1.5 0.9"/> -->
      <!-- </geometry> -->
    <!-- </collision> -->
  </link>
  <joint name="J_ROTO3EE" type="fixed">
    <parent link="ENDEFFECTOR"/>
    <child link="SHOVEL"/>
    <origin xyz="0.148 0 -0.149"/>
  </joint>
  <link name="SHOVEL_BOX_ORIGIN">
<!--    <visual>-->
<!--      <origin xyz="-0.0 0.0 0.0"/>-->
<!--      <geometry>-->
<!--        <sphere radius="0.05" />-->
<!--      </geometry>-->
<!--      <material name="red"/>-->
<!--    </visual>-->
  </link>
  <joint name="J_SHOVEL_BOX_ORIGIN" type="fixed">
    <parent link="SHOVEL"/>
    <child link="SHOVEL_BOX_ORIGIN"/>
    <origin xyz="0.40 0 0.21" rpy="0 -0.25 0"/>
  </joint>
  <link name="SHOVEL_BOX_BOTTOM_ORIGIN_BACK_D1">
<!--    <visual>-->
<!--      <origin xyz="-0.0 0.0 0.0"/>-->
<!--      <geometry>-->
<!--        <sphere radius="0.08" />-->
<!--      </geometry>-->
<!--      <material name="red"/>-->
<!--    </visual>-->
  </link>
  <joint name="J_SHOVEL_BOX_BOTTOM_ORIGIN_BACK_D1" type="fixed">
    <parent link="SHOVEL_BOX_ORIGIN"/>
    <child link="SHOVEL_BOX_BOTTOM_ORIGIN_BACK_D1"/>
    <origin xyz="0.375 0.75 0.45"/>
  </joint>
  <link name="SHOVEL_BOX_BOTTOM_D1">
<!--    <visual>-->
<!--      <origin xyz="-0.0 0.0 0.0"/>-->
<!--      <geometry>-->
<!--        <sphere radius="0.08" />-->
<!--      </geometry>-->
<!--      <material name="red"/>-->
<!--    </visual>-->
  </link>
  <joint name="J_SHOVEL_BOX_BOTTOM_D1" type="fixed">
    <parent link="SHOVEL_BOX_ORIGIN"/>
    <child link="SHOVEL_BOX_BOTTOM_D1"/>
    <origin xyz="0.375 0.75 -0.45"/>
  </joint>
  <link name="SHOVEL_BOX_BOTTOM3"/>
  <joint name="J_SHOVEL_BOX_BOTTOM3" type="fixed">
    <parent link="SHOVEL_BOX_ORIGIN"/>
    <child link="SHOVEL_BOX_BOTTOM3"/>
    <origin xyz="0.375 -0.75 -0.45"/>
  </joint>
  <link name="SHOVEL_BOX_BOTTOM_D2">
<!--    <visual>-->
<!--      <origin xyz="-0.0 0.0 0.0"/>-->
<!--      <geometry>-->
<!--        <sphere radius="0.08" />-->
<!--      </geometry>-->
<!--      <material name="red"/>-->
<!--    </visual>-->
  </link>
  <joint name="J_SHOVEL_BOX_BOTTOM_D2" type="fixed">
    <parent link="SHOVEL_BOX_ORIGIN"/>
    <child link="SHOVEL_BOX_BOTTOM_D2"/>
    <origin xyz="0.375 -0.75 0.45"/>
  </joint>
  <link name="SHOVEL_BOX_BACK_D2">
<!--    <visual>-->
<!--      <origin xyz="-0.0 0.0 0.0"/>-->
<!--      <geometry>-->
<!--        <sphere radius="0.08" />-->
<!--      </geometry>-->
<!--      <material name="red"/>-->
<!--    </visual>-->
  </link>
  <joint name="J_SHOVEL_BOX_BACK_D2" type="fixed">
    <parent link="SHOVEL_BOX_ORIGIN"/>
    <child link="SHOVEL_BOX_BACK_D2"/>
    <origin xyz="-0.375 -0.75 0.45"/>
  </joint>
  <link name="SHOVEL_BOX_BACK_ORIGIN">
<!--    <visual>-->
<!--      <origin xyz="-0.0 0.0 0.0"/>-->
<!--      <geometry>-->
<!--        <sphere radius="0.08" />-->
<!--      </geometry>-->
<!--      <material name="red"/>-->
<!--    </visual>-->
  </link>
  <joint name="J_SHOVEL_BOX_BACK_ORIGIN" type="fixed">
    <parent link="SHOVEL_BOX_ORIGIN"/>
    <child link="SHOVEL_BOX_BACK_ORIGIN"/>
    <origin xyz="-0.375 0.75 0.45"/>
  </joint>
  <link name="ENDEFFECTOR_CONTACT"/>
  <joint name="SHOVEL_TO_ENDEFFECTOR_CONTACT" type="fixed">
    <parent link="SHOVEL"/>
    <child link="ENDEFFECTOR_CONTACT"/>
    <origin rpy="0 -0.244 0" xyz="0.86 0 -0.14"/>
  </joint>
  <link name="SHOVEL_BACK_CONTACT"/>
  <joint name="SHOVEL_TO_SHOVEL_BACK_CONTACT" type="fixed">
    <parent link="ENDEFFECTOR_CONTACT"/>
    <child link="SHOVEL_BACK_CONTACT"/>
    <origin rpy="0 0 0" xyz="0.0 0 0.9"/>
  </joint>
  <link name="SHOVEL_BUCKET_CYLINDER_ORIGIN"/>
  <joint name="SHOVEL_TO_SHOVEL_CYLINDER_ORIGIN" type="fixed">
    <parent link="ENDEFFECTOR_CONTACT"/>
    <child link="SHOVEL_BUCKET_CYLINDER_ORIGIN"/>
    <origin rpy="0 0 0" xyz="-0.375 0 0.525"/>
  </joint>
  <link name="IMU_link">
  </link>
  <link name="IMU_base_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.012"/>
      <geometry>
        <box size="0.046 0.045 0.024"/>
      </geometry>
      <material name="red"/>
    </visual>
    <!-- <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.012"/>
      <geometry>
        <box size="0.046 0.045 0.024"/>
      </geometry>
    </collision> -->
    <!--<xacro:inertial_cuboid m="${m_imu}" w="${l_imu}" h="${h_imu}"
          d="${w_imu}"/>-->
  </link>
  <joint name="IMU_base_joint" type="fixed">
    <parent link="BASE"/>
    <child link="IMU_base_link"/>
    <origin rpy="-1.570796327 1.570796327 0.0" xyz="0.0 0.0 0.0"/>
  </joint>
  <joint name="IMU_joint" type="fixed">
    <parent link="IMU_base_link"/>
    <child link="IMU_link"/>
    <origin rpy="0 0 0" xyz="-0.0107 -0.00505 -0.00705"/>
  </joint>
  <link name="IMU_CABIN_link">
  </link>
  <link name="IMU_CABIN_base_link">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 -0.012"/>
      <geometry>
        <box size="0.046 0.045 0.024"/>
      </geometry>
      <material name="red"/>
    </visual>
    <!-- <collision>
      <origin rpy="0 0 0" xyz="0 0 -0.012"/>
      <geometry>
        <box size="0.046 0.045 0.024"/>
      </geometry>
    </collision> -->
    <!--<xacro:inertial_cuboid m="${m_imu}" w="${l_imu}" h="${h_imu}"
          d="${w_imu}"/>-->
  </link>
  <joint name="IMU_CABIN_base_joint" type="fixed">
    <parent link="CABIN"/>
    <child link="IMU_CABIN_base_link"/>
    <origin rpy="3.14159265359 0.0 -0.637045" xyz="0.32963 0.30007 0.035"/>
  </joint>
  <joint name="IMU_CABIN_joint" type="fixed">
    <parent link="IMU_CABIN_base_link"/>
    <child link="IMU_CABIN_link"/>
    <origin rpy="0 0 0" xyz="-0.0107 -0.00505 -0.00705"/>
  </joint>
  <link name="GNSS_L">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.1" radius="0.001"/>
      </geometry>
      <material name="red"/>
    </visual>
    <!-- <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.1" radius="0.001"/>
      </geometry>
    </collision> -->
    <inertial>
      <mass value="0.001"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
  </link>
  <joint name="GNSS_L_joint" type="fixed">
    <parent link="CABIN"/>
    <child link="GNSS_L"/>
    <origin xyz="-.909 .67 2.079"/>
  </joint>
  <link name="GNSS_R">
    <visual>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.1" radius="0.001"/>
      </geometry>
      <material name="red"/>
    </visual>
    <!-- <collision>
      <origin rpy="0 0 0" xyz="0 0 0"/>
      <geometry>
        <cylinder length="0.1" radius="0.001"/>
      </geometry>
    </collision> -->
    <inertial>
      <mass value="0.001"/>
      <origin xyz="0 0 0"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
  </link>
  <joint name="GNSS_R_joint" type="fixed">
    <parent link="CABIN"/>
    <child link="GNSS_R"/>
    <origin xyz="-.909 -.5 2.079"/>
  </joint>
</robot>
