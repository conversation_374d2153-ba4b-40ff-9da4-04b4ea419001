import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg
from moleworks_ext.common.assets.articulation_ext_force_global_cfg import Articulation_ext_force_globalCfg
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg, InverseDynamicsController
from moleworks_ext import MOLEWORKS_RSC_DIR
from moleworks_ext.common.assets.articulation_ext_force_global_cfg import Articulation_ext_force_globalCfg

_M545_DOF_ARM_USD = f"{MOLEWORKS_RSC_DIR}/excavation/model/m545_arm_spread_merged/m545_arm_spread_merged.usd" # -0.704 terrain 
_M545_DOF_ARM_USD_EE = f"{MOLEWORKS_RSC_DIR}/excavation/model/m545_dof_arm/m545_dof_arm_new.usd"

M545_DOF_ARM_MERGED_CFG = Articulation_ext_force_globalCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_M545_DOF_ARM_USD,
        activate_contact_sensors=False,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0)#0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.00), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        #pos=( 0.0136, -0.0689,   0.521),
        # joint_pos={
        #     # arm
        #     "J_BOOM": -0.8792816,#-0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
        #     "J_STICK":2.029131,# 1.57, # 2.029131, #
        #     "J_TELE": 1.1033065, #0.009, #1.1033065, #
        #     # tool (end effector)
        #     "J_EE_PITCH": 0.48247415 # 0.48247415,# 
        # },
        # Joint positions: tensor([[-0.8513,  2.0418,  0.8863,  0.2871]]
        # joint_pos = {
        #     "J_BOOM": -0.0454,
        #     "J_STICK": 0.5988,
        #     "J_TELE": 1.0978,
        #     "J_EE_PITCH": -1.0036
        # }
        joint_pos = {
            "J_BOOM": -0.8474,
            "J_STICK": 2.040,
            "J_TELE": 0.891,
            "J_EE_PITCH": 1.887 # negative less curled
        }
    ), #-0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)

M545_DOF_ARM_FULL_URDF = f"{MOLEWORKS_RSC_DIR}/sim/model/m545_urdf_isaac/urdf/custom_fixed_m545_expanded.urdf"

M545_DOF_ARM_FULL_URDF_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UrdfFileCfg(
        asset_path=M545_DOF_ARM_FULL_URDF,
        fix_base=False,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0) #0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.704), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        #pos=( 0.0136, -0.0689,   0.521),
        # joint_pos={
        #     # arm
        #     "J_BOOM": -0.8792816,#-0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
        #     "J_STICK":2.029131,# 1.57, # 2.029131, #
        #     "J_TELE": 1.1033065, #0.009, #1.1033065, #
        #     # tool (end effector)
        #     "J_EE_PITCH": 0.48247415 # 0.48247415,# 
        # },
        # Joint positions: tensor([[-0.8513,  2.0418,  0.8863,  0.2871]]
        joint_pos = {
            "J_BOOM": -0.8513,
            "J_STICK": 2.0418,
            "J_TELE": 0.8863,
            "J_EE_PITCH": 0.2871
        }
    ), #-0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=0.97,
)
from moleworks_ext.common.assets.articulation_ext_force_global_cfg import Articulation_ext_force_globalCfg

M545_DOF_ARM_FULL_USD = f"{MOLEWORKS_RSC_DIR}/sim/model/m545_urdf_isaac/urdf/custom_fixed_m545_expanded/custom_fixed_m545_expanded.usd"

M545_DOF_ARM_FULL_USD_CFG = Articulation_ext_force_globalCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=M545_DOF_ARM_FULL_USD,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=32,
            solver_velocity_iteration_count=32,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0)#0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.704), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        #pos=( 0.0136, -0.0689,   0.521),
        # joint_pos={
        #     # arm
        #     "J_BOOM": -0.8792816,#-0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
        #     "J_STICK":2.029131,# 1.57, # 2.029131, #
        #     "J_TELE": 1.1033065, #0.009, #1.1033065, #
        #     # tool (end effector)
        #     "J_EE_PITCH": 0.48247415 # 0.48247415,# 
        # },
        # Joint positions: tensor([[-0.8513,  2.0418,  0.8863,  0.2871]]
        joint_pos = {
            "J_BOOM": -0.8513,
            "J_STICK": 2.0418,
            "J_TELE": 0.8863,
            "J_EE_PITCH": 0.2871
        }
    ), #-0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=0.97,
)

M545_DOF_ARM_FULL_URDF = f"{MOLEWORKS_RSC_DIR}/sim/model/m545_urdf_isaac/urdf/custom_fixed_m545_expanded.urdf"

M545_DOF_ARM_FULL_URDF_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UrdfFileCfg(
        asset_path=M545_DOF_ARM_FULL_URDF,
        fix_base=False,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0) #0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, -0.18), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        #pos=( 0.0136, -0.0689,   0.521),
        # joint_pos={
        #     # arm
        #     "J_BOOM": -0.8792816,#-0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
        #     "J_STICK":2.029131,# 1.57, # 2.029131, #
        #     "J_TELE": 1.1033065, #0.009, #1.1033065, #
        #     # tool (end effector)
        #     "J_EE_PITCH": 0.48247415 # 0.48247415,# 
        # },
        # Joint positions: tensor([[-0.8513,  2.0418,  0.8863,  0.2871]]
        joint_pos = {
            "J_BOOM": -0.8513,
            "J_STICK": 2.0418,
            "J_TELE": 0.8863,
            "J_EE_PITCH": 0.2871
        }
    ), #-0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)

M545_DOF_ARM_FULL_USD = f"{MOLEWORKS_RSC_DIR}/sim/model/m545_arm_spread/m545_arm_spread.usd"

M545_DOF_ARM_FULL_CFG = Articulation_ext_force_globalCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=M545_DOF_ARM_FULL_USD,
        activate_contact_sensors=False,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0) #0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, -0.18), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        #pos=( 0.0136, -0.0689,   0.521),
        # joint_pos={
        #     # arm
        #     "J_BOOM": -0.8792816,#-0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
        #     "J_STICK":2.029131,# 1.57, # 2.029131, #
        #     "J_TELE": 1.1033065, #0.009, #1.1033065, #
        #     # tool (end effector)
        #     "J_EE_PITCH": 0.48247415 # 0.48247415,# 
        # },
        # Joint positions: tensor([[-0.8513,  2.0418,  0.8863,  0.2871]]
        joint_pos = {
            "J_BOOM": -0.8513,
            "J_STICK": 2.0418,
            "J_TELE": 0.8863,
            "J_EE_PITCH": 0.2871
        }
    ), #-0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)

M545_DOF_ARM_W_CABIN_USD = f"{MOLEWORKS_RSC_DIR}/sim/model/m545_dof_arm_w_cabin/m545_dof_arm_w_cabin.usd"
M545_DOF_ARM_W_CABIN_URDF = f"{MOLEWORKS_RSC_DIR}/sim/model/m545_urdf/urdf/m545_arm_spread_w_cabin.urdf"

M545_DOF_ARM_W_CABIN_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UrdfFileCfg(
        asset_path=M545_DOF_ARM_W_CABIN_URDF,
        fix_base=False,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0)#0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.00), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        #pos=( 0.0136, -0.0689,   0.521),
        # joint_pos={
        #     # arm
        #     "J_BOOM": -0.8792816,#-0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], 
        # device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
        #     "J_STICK":2.029131,# 1.57, # 2.029131, #
        #     "J_TELE": 1.1033065, #0.009, #1.1033065, #
        #     # tool (end effector)
        #     "J_EE_PITCH": 0.48247415 # 0.48247415,# 
        # },
        # Joint positions: tensor([[-0.8513,  2.0418,  0.8863,  0.2871]]
        joint_pos = {
            "J_TURN": 0.0011,
            "J_BOOM": -0.8792816,
            "J_STICK": 2.029131,
            "J_TELE": 1.1033065,
            "J_EE_PITCH": 0.2871
        }
    ), # -0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "cabin": ImplicitActuatorCfg(
            joint_names_expr=["J_TURN"],
            effort_limit=1e10,
            velocity_limit=100.0,
            stiffness=0,
            damping=1e8,
        ),
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)


M545_DOF_ARM_W_CABIN_ROTO_USD = f"{MOLEWORKS_RSC_DIR}/sim/model/m545_dof_arm_w_cabin_roto/m545_dof_arm_w_cabin_roto.usd"
M545_DOF_ARM_W_CABIN_ROTO_URDF = f"{MOLEWORKS_RSC_DIR}/sim/model/m545_urdf/urdf/m545_dof_arm_w_cabin_roto.urdf"

M545_DOF_ARM_W_CABIN_ROTO_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UrdfFileCfg(
        asset_path=M545_DOF_ARM_W_CABIN_ROTO_URDF,
        fix_base=False,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0)#0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.00), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        #pos=( 0.0136, -0.0689,   0.521),
        # joint_pos={
        #     # arm
        #     "J_BOOM": -0.8792816,#-0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
        #     "J_STICK":2.029131,# 1.57, # 2.029131, #
        #     "J_TELE": 1.1033065, #0.009, #1.1033065, #
        #     # tool (end effector)
        #     "J_EE_PITCH": 0.48247415 # 0.48247415,# 
        # },
        # Joint positions: tensor([[-0.8513,  2.0418,  0.8863,  0.2871]]
        joint_pos={
            "J_TURN": 0.0,
            "J_BOOM": -0.8513,
            "J_STICK": 2.0418,
            "J_TELE": 0.8863,
            "J_EE_PITCH": 0.2871,
            "J_EE_ROLL": -0.0001,
            "J_EE_YAW": 0.0000
        }
    ),
    actuators={
        "cabin": ImplicitActuatorCfg(
            joint_names_expr=["J_TURN"],
            effort_limit=1e10,
            velocity_limit=100.0,
            stiffness=0,
            damping=1e8,
        ),
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
        "rotortilt": ImplicitActuatorCfg(
            joint_names_expr=["J_EE_ROLL", "J_EE_YAW"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=1e8,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)


M545_DRIVE_CABIN_ARM_USD = f"{MOLEWORKS_RSC_DIR}/sim/m545/model/m545_drive_cabin_arm.usd"


M545_DRIVE_CABIN_ARM_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=M545_DRIVE_CABIN_ARM_USD,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1,  # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,  # to change
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(
            contact_offset=0.008, rest_offset=0.0
        ),  # 0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(
            0.0,
            0.0,
            0.0,
        ),  # 0.7049000000000001), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        # pos=( 0.0136, -0.0689,   0.521),
        joint_pos={
            "J_TURN": 0.0,
            "J_BOOM": (
                -0.8792816
            ),  # -0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
            "J_STICK": 2.029131,  # 1.57, # 2.029131, #
            "J_TELE": 1.1033065,  # 0.009, #1.1033065, #
            # tool (end effector)
            "J_EE_PITCH": 0.48247415,  # 0.48247415,#
            # Steering
            "J_RF_STEER": 0.0,  # 0.48247415,#
            "J_LF_STEER": 0.0,
            # wheels
            "J_LF_WHEEL": (
                0.0
            ),  # -0.78, #-0.8792816,#  cache: tensor([[-0.4114,  1.8799,  1.2953, -0.3482]], device='cuda:0'), gym: [-0.8792816, 2.029131, 1.1033065, 0.48247415]
            "J_LH_WHEEL": 0.0,  # 1.57, # 2.029131, #
            "J_RF_WHEEL": 0.0,  # 0.009, #1.1033065, #
            "J_RH_WHEEL": 0.0,
        },
    ),  # -0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "cabin": ImplicitActuatorCfg(
            joint_names_expr=["J_TURN"],
            effort_limit=1e10,
            velocity_limit=100.0,
            stiffness=0,
            damping=1e8,
            # dampingping=0.,
            # stif=1e6,
        ),
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e15,
            velocity_limit=100.0,
            stiffness=0,
            damping=1e20,
            # dampingping=0.,
            # stif=1e6,
        ),
        "steering": ImplicitActuatorCfg(
            joint_names_expr=["J_RF_STEER", "J_LF_STEER"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=100000,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
        "wheels": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_WHEEL", "J_LH_WHEEL", "J_RF_WHEEL", "J_RH_WHEEL"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=100000.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)

_M545_USD = f"{MOLEWORKS_RSC_DIR}/sim/model/m545/m545.usd"

M545_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_M545_USD,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=1.0,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=32,
            solver_velocity_iteration_count=24,
            sleep_threshold=0.0, stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(
            contact_offset=0.008, rest_offset=0.0
        ),  # 0.
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.0),
        joint_pos={
            # base
            "J_LF_HAA": -0.0033,
            "J_LF_HFE": 0.00,
            "J_LF_PARALLEL": 0.00,
            "J_LF_PARALLEL2": 0.00,
            "J_LF_STEER": -0.0015,
            "J_LF_WHEEL": -0.0023,
            "J_RF_HAA": 0.0010,
            "J_RF_HFE": 0.00,
            "J_RF_PARALLEL": 0.00,
            "J_RF_PARALLEL2": 0.00,
            "J_RF_STEER": 0.0010,
            "J_RF_WHEEL": -0.0017,
            "J_LH_HFE": -0.0,
            "J_LH_HAA": 0.0025,
            "J_LH_STEER": 0.0006,
            "J_LH_WHEEL": -0.2714,
            "J_RH_HFE": 0.0,
            "J_RH_HAA": 0.0007,
            "J_RH_STEER": 0.0002,
            "J_RH_WHEEL": -0.2749,
            # arm
            "J_TURN": -0.0011,
            "J_BOOM": -0.8792816,
            "J_STICK": 2.029131,
            "J_TELE": 1.1033065,
            # tool (end effector)
            "J_EE_PITCH": 0.48247415,
            "J_EE_ROLL": -0.0001,
            "J_EE_YAW": 0.0000
        },
        joint_vel={".*": 0.0},
    ),
    actuators={
        "mimic_joints": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_PARALLEL", "J_LF_PARALLEL2", "J_RF_PARALLEL", "J_RF_PARALLEL2"],
            effort_limit=0.0,
            velocity_limit=0.0,
            stiffness=0.0,
            damping=0.0,
            friction=0.0,
        ),
        "chassis": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_HAA", "J_LF_HFE", "J_RF_HAA", "J_RF_HFE","J_LH_HFE", "J_LH_HAA", "J_RH_HFE", "J_RH_HAA"],
            effort_limit=1e9,
            velocity_limit=100.0,
            stiffness=1e7,
            damping=1e5,
        ),
        "steering": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_STEER", "J_RF_STEER", "J_LH_STEER", "J_RH_STEER"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=1e7,
            damping=1e5,
        ),
        "wheels": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_WHEEL", "J_LH_WHEEL", "J_RF_WHEEL", "J_RH_WHEEL"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0,
            damping=1e7,
        ),
        "cabin": ImplicitActuatorCfg(
            joint_names_expr=["J_TURN"],
            effort_limit=1e10,
            velocity_limit=100.0,
            stiffness=0,
            damping=100000,
        ),
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=5e5,
            velocity_limit=100.0,
            stiffness=0,
            damping=0,
        ),
        "rotortilt": ImplicitActuatorCfg(
            joint_names_expr=["J_EE_ROLL", "J_EE_YAW"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=1e5,
            damping=1e4,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)
import numpy as np

# # of shape (num_robots, num_dof, 2)
joint_pos_limits = [
    [-3.14, 3.14],
    [-1.34, 0.44],
    [0.58, 2.78],
    [0.0, 1.8],
    [-0.35, 2.28]
]

joint_vel_limits = [
    [-1, 1],
    [-0.5, 0.5],
    [-0.6, 0.6],
    [-0.4, 0.4],
    [-0.8, 0.8]
]

joint_efforts_limits = [
    [-2e6, 2e6],
    [-2e6, 2e6],
    [-1e6, 1e6],
    [-1e6, 1e6],
    [-1e6, 1e6]
]

# controller config 
INV_DYN_CFG = InverseDynamicsControllerCfg(
    command_type="vel",
    k_p=[0, 0, 0, 0, 0],
    k_d=[20, 20, 30, 20, 20],
    dof_limits=joint_vel_limits,
    dof_efforts_limits=joint_efforts_limits,
)

INV_DYN_POS_CFG = InverseDynamicsControllerCfg(
    command_type="pos",
    k_p=[30, 26, 20, 30, 20],
    k_d=[30, 26, 20, 30, 20],
    dof_limits=joint_pos_limits,
    dof_efforts_limits=joint_efforts_limits,
)