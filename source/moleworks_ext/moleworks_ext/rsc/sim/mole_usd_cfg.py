import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg
from moleworks_ext.common.assets.articulation_ext_force_global_cfg import Articulation_ext_force_globalCfg
from moleworks_ext.common.controllers.inverse_dynamics import InverseDynamicsControllerCfg, InverseDynamicsController
from moleworks_ext import MOLEWORKS_RSC_DIR
from moleworks_ext.common.assets.articulation_ext_force_global_cfg import Articulation_ext_force_globalCfg

_MOLE_USD = f"{MOLEWORKS_RSC_DIR}/sim/model/m445/m445.usd"

MOLE_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_MOLE_USD,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=1.0,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=32,
            solver_velocity_iteration_count=24,
            sleep_threshold=0.0, stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(
            contact_offset=0.008, rest_offset=0.0
        ),  # 0.
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.0),
        joint_pos={
            # wheels
            "J_LF_WHEEL": 0.0,
            "J_RF_WHEEL": 0.0,
            "J_LH_WHEEL": 0.0,
            "J_RH_WHEEL": 0.0,
            # steering 
            "J_LF_STEER": 0.0,
            "J_RF_STEER": 0.0,
            "J_LH_STEER": 0.0,
            "J_RH_STEER": 0.0,
            # base
            "J_LF_HAA": -0.0033,
            "J_LF_HFE": 0.00,
            "J_LF_PARALLEL1": 0.00,
            "J_LF_PARALLEL2": 0.00,
            "J_LH_PARALLEL1": 0.00,
            "J_LH_PARALLEL2": 0.00,
            "J_RF_HAA": 0.0010,
            "J_RF_HFE": 0.00,
            "J_RF_PARALLEL1": 0.00,
            "J_RF_PARALLEL2": 0.00,
            "J_RH_PARALLEL1": 0.00,
            "J_RH_PARALLEL2": 0.00,
            "J_LH_HFE": 0.0,
            "J_LH_HAA": 0.0025,
            "J_RH_HFE": 0.0,
            "J_RH_HAA": 0.0007,
            # cabin
            "J_TURN": -0.0011,
            # arm
            "J_BOOM": -0.8792816,
            "J_STICK": 2.029131,
            "J_TELE": 1.1033065,
            "J_EE_PITCH": 0.48247415,
            # tool
            "J_EE_ROLL": -0.0001,
            "J_EE_YAW": 0.0000
        },
        joint_vel={".*": 0.0},
    ),
    actuators={
        "mimic_joints": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_PARALLEL1", "J_LF_PARALLEL2", "J_RF_PARALLEL1", "J_RF_PARALLEL2", "J_LH_PARALLEL1", "J_LH_PARALLEL2", "J_RH_PARALLEL1", "J_RH_PARALLEL2"],
            effort_limit=0.0,
            velocity_limit=0.0,
            stiffness=0.0,
            damping=0.0,
            friction=0.0,
        ),
        "chassis": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_HAA", "J_LF_HFE", "J_RF_HAA", "J_RF_HFE","J_LH_HFE", "J_LH_HAA", "J_RH_HFE", "J_RH_HAA"],
            effort_limit=1e9,
            velocity_limit=100.0,
            stiffness=1e7,
            damping=1e5,
        ),
        "steering": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_STEER", "J_RF_STEER", "J_LH_STEER", "J_RH_STEER"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=1e7,
            damping=1e5,
        ),
        "wheels": ImplicitActuatorCfg(
            joint_names_expr=["J_LF_WHEEL", "J_LH_WHEEL", "J_RF_WHEEL", "J_RH_WHEEL"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0,
            damping=1e7,
        ),
        "cabin": ImplicitActuatorCfg(
            joint_names_expr=["J_TURN"],
            effort_limit=1e10,
            velocity_limit=100.0,
            stiffness=0,
            damping=100000,
        ),
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE"],
            effort_limit=5e5,
            velocity_limit=100.0,
            stiffness=0,
            damping=0,
        ),
        "rotortilt": ImplicitActuatorCfg(
            joint_names_expr=["J_EE_ROLL", "J_EE_YAW"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=1e5,
            damping=1e4,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)


_MOLE_DOF_ARM_USD = f"{MOLEWORKS_RSC_DIR}/excavation/model/m445_arm/m445.usd"

MOLE_DOF_ARM_MERGED_CFG = Articulation_ext_force_globalCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_MOLE_DOF_ARM_USD,
        activate_contact_sensors=False,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0)#0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.0), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        joint_pos = {
            "J_BOOM": -0.8474,
            "J_STICK": 2.040,
            "J_TELE": 0.891,
            "J_EE_PITCH": 1.887 # negative less curled
        }
    ), #-0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=5e5,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)


_MOLE_DOF_ARM_W_CABIN_USD = f"{MOLEWORKS_RSC_DIR}/sim/model/m445_arm_w_cabin/m445.usd" # -0.704 terrain 

MOLE_DOF_ARM_W_CABIN_MERGED_CFG = Articulation_ext_force_globalCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=_MOLE_DOF_ARM_W_CABIN_USD,
        activate_contact_sensors=False,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            retain_accelerations=False,
            linear_damping=0.0,
            angular_damping=0.0,
            max_linear_velocity=1000.0,
            max_angular_velocity=1000.0,
            max_depenetration_velocity=0.1, # 0.1
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=8,
            solver_velocity_iteration_count=4,
            sleep_threshold=0.0,
            stabilization_threshold=0.0,
        ),
        collision_props=sim_utils.CollisionPropertiesCfg(contact_offset=0.008, rest_offset=0.0)#0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.0), #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        joint_pos = {
            "J_TURN": 0.0011,
            "J_BOOM": -0.8792816,
            "J_STICK": 2.029131,
            "J_TELE": 1.1033065,
            "J_EE_PITCH": 0.2871
        }
    ), #-0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "cabin": ImplicitActuatorCfg(
            joint_names_expr=["J_TURN"],
            effort_limit=1e10,
            velocity_limit=100.0,
            stiffness=0,
            damping=1e8,
        ),
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH"],
            effort_limit=1e7,
            velocity_limit=100.0,
            stiffness=0.0,
            damping=0.0,
            # dampingping=0.,
            # stif=1e6,
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)