# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for the Mujoco Humanoid robot."""

from __future__ import annotations

import numpy as np

import isaaclab.sim as sim_utils
from isaaclab.actuators import DelayedPDActuatorCfg, IdealPDActuatorCfg, ImplicitActuatorCfg
from isaaclab.assets import ArticulationCfg
from isaaclab.utils.assets import ISAAC_NUCLEUS_DIR

from moleworks_ext import MOLEWORKS_RSC_DIR

MATERIAL_HANDLER_USD = (
    f"{MOLEWORKS_RSC_DIR}/material_handling/liebherr_material_handler/lh40_with_inertia_and_limits.usd"
)

MATERIAL_HANDLER_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        usd_path=MATERIAL_HANDLER_USD,
        activate_contact_sensors=True,
        rigid_props=sim_utils.RigidBodyPropertiesCfg(disable_gravity=False, retain_accelerations=False),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(enabled_self_collisions=False),
        collision_props=sim_utils.CollisionPropertiesCfg(
            contact_offset=0.008, rest_offset=0.0
        ),  # 0.008, rest_offset=0.0
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(
            0.0,
            0.0,
            7.0492e-01,
        ),  #  5.2219e-01 for m545_dof_arm_no_collision, 7.0492e-01 for m545_floating_arm_only_reduced_cd_new
        # pos=( 0.0136, -0.0689,   0.521),
        joint_pos={
            # Slew
            "joint_slew": 0.0,
            # Arm
            "joint_boom": 1.1,
            "joint_stick": -1.2,
            # tool (end effector)
            "joint_hanger": -1.2157e00,  # -np.pi/2-(1.1-1.2),
            "joint_bearingfork": 0.0,
        },
    ),  # -0.8792816 ,  2.029131  ,  1.1033065 ,  0.48247415
    actuators={
        "slew": ImplicitActuatorCfg(
            joint_names_expr=["joint_slew"],
            # effort_limit=1e10,
            stiffness=0.0,
            damping=1e10,
        ),
        "boom": ImplicitActuatorCfg(joint_names_expr=["joint_boom"], stiffness=0.0, damping=1e10),
        "stick": ImplicitActuatorCfg(
            joint_names_expr=["joint_stick"],
            stiffness=0.0,
            damping=1e10,
            # friction=1e10,
        ),
        "hanger": ImplicitActuatorCfg(
            joint_names_expr=["joint_hanger"],
            stiffness=0.0,
            damping=0.001,
            friction=0.001,
            # velocity_limit = 1
        ),
        "bearing_fork": ImplicitActuatorCfg(
            joint_names_expr=["joint_bearingfork"],
            stiffness=0.0,
            damping=0.001,
            friction=0.001,
            # velocity_limit = 1
        ),
    },
    soft_joint_pos_limit_factor=1.0,
)
