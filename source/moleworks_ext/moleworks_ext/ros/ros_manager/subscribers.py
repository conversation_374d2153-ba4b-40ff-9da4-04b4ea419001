# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import functools
from abc import ABC
from typing import TYPE_CHECKING

import carb
import torch
from isaaclab.envs import ManagerBasedEnv
from rclpy.node import Node
from rclpy.qos import QoSProfile
from sensor_msgs.msg import JointState
from std_msgs.msg import Bool

if TYPE_CHECKING:
    from . import subscribers_cfg


class SubscriberTerm(ABC):
    def __init__(
        self, cfg: subscribers_cfg.SubscriberTermCfg, node: Node, env: ManagerBasedEnv, qos_profile: QoSProfile
    ):
        """Initialize the ros subscriber term.

        Args:
            cfg: The configuration object.
            node: The ros node instance.
            env: The Isaac Lab environment.
            qos_profile: The quality of service profile for ROS communications.
        """
        # store the inputs
        self.cfg = cfg
        self._node = node
        self._env = env
        self._qos_profile = qos_profile

        # resolve scene entity
        self.cfg.asset_cfg.resolve(env.scene)

        # store articulation handle
        self._asset = self._env.scene[self.cfg.asset_cfg.name]

        # initialize message to be empty for subscribers not associated with any action
        self.msg_converted = torch.zeros(0)

    def close(self):
        del self._env


class JointCommandSubscriber(SubscriberTerm):
    def __init__(
        self, cfg: subscribers_cfg.JointCommandSubscriberCfg, node: Node, env: ManagerBasedEnv, qos_profile: QoSProfile
    ):
        """Initialize the joint command subscriber term.

        Args:
            cfg: The configuration object of type JointCommandSubscriberCfg.
            node: The ros node instance.
            env: The Isaac Lab environment.
            qos_profile: The quality of service profile for ROS communications.

        Raises:
            ValueError: If the joint state action terms are not in the order of position, velocity, and effort.
                This is necessary because we need to know how to concatenate the action terms.
            ValueError: If the joint names in each of the action terms are not identical.
            ValueError: If the total size of the combined action terms is not the same as the total_action_dim.

        """
        super().__init__(cfg, node, env, qos_profile)
        self.cfg: subscribers_cfg.JointCommandSubscriberCfg
        joint_state_action_terms = [
            self.cfg.joint_position_action_name,
            self.cfg.joint_velocity_action_name,
            self.cfg.joint_effort_action_name,
        ]
        all_action_terms = self._env.action_manager.active_terms

        # Remove any action terms that are not those belonging to joint state
        joint_state_action_terms_order = [term for term in joint_state_action_terms if term in all_action_terms]

        # Verify that the order of the joint state action terms is correct
        if joint_state_action_terms_order != joint_state_action_terms:
            raise ValueError(
                "Joint state action terms should be in the order of position, velocity, and effort."
                f"Current order is {joint_state_action_terms_order}."
            )

        prev_joint_names = None
        total_action_dim = 0
        # Verify that joint names are identical and in same order in all action terms
        for joint_state_action_term in joint_state_action_terms:
            action_term = self._env.action_manager.get_term(joint_state_action_term)
            _, joint_names = action_term._asset.find_joints(action_term.cfg.joint_names)
            total_action_dim += len(joint_names)

            if prev_joint_names is None:
                prev_joint_names = joint_names
                self.joint_names = joint_names
                self.size = len(self.joint_names)
            else:
                if prev_joint_names != joint_names:
                    raise ValueError(
                        f"Joint names in each of the action terms: {joint_state_action_terms_order} are not identical."
                    )

        # Total size of the combined action terms should be the same as the total_action_dim
        if total_action_dim != self._env.action_manager.total_action_dim:
            raise ValueError(
                "Total size of the combined action terms should be the same as the total_action_dim."
                f"Total size of the combined action terms is {total_action_dim},"
                f"but should be {self._env.action_manager.total_action_dim}"
            )

        self.msg_converted = torch.zeros(total_action_dim)

        self.subscriber = node.create_subscription(
            msg_type=cfg.msg_type,
            topic=cfg.topic,
            callback=functools.partial(self.from_joint_state_to_torch),
            qos_profile=self._qos_profile,
        )

    def from_joint_state_to_torch(self, msg: JointState):
        """Callback function for the Joint Command subscriber term that converts JointState msg position, velocity, and effort
        to torch.tensor used in actions. The order of the joints, position, velocity, and effort is preserved.

        Args:
            msg: Received JointState message to fill the action tensor.
        """
        # Reorder the joint names, position, velocity, and effort according to the joint names in the action definition
        name_to_msg_order = {name: index for index, name in enumerate(msg.name)}

        # TODO: Can we optimize this with the stacking operation below?
        position = torch.tensor([msg.position[name_to_msg_order[n]] for n in self.joint_names])
        velocity = torch.tensor([msg.velocity[name_to_msg_order[n]] for n in self.joint_names])
        effort = torch.tensor([msg.effort[name_to_msg_order[n]] for n in self.joint_names])

        # Stack order is dependent on the orderring of the action definition
        self.msg_converted = torch.hstack([position, velocity, effort])


class JointPositionCommandSubscriber(SubscriberTerm):
    def __init__(
        self,
        cfg: subscribers_cfg.JointPositionCommandSubscriberCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
    ):
        """Initialize the Joint Position Command subscriber term.

        Args:
            cfg: The configuration object of type JointPositionCommandSubscriberCfg.
            node: The ros node instance.
            env: The Isaac Lab environment.
            qos_profile: The quality of service profile for ROS communications.
        """
        super().__init__(cfg, node, env, qos_profile)
        self.cfg: subscribers_cfg.JointPositionCommandSubscriberCfg

        # Collect joint names in order and allocate action size
        if self.cfg.action_name is not None:  # if action_name provided allocate accordingly
            action_term = self._env.action_manager.get_term(self.cfg.action_name)
            _, self.joint_names = action_term._asset.find_joints(action_term.cfg.joint_names)
            self.size = len(self.joint_names)
        else:  # else assume total action dimension
            self.size = self._env.action_manager.total_action_dim
            self.joint_names = None

        self.msg_converted = torch.zeros(self.size)

        self.subscriber = node.create_subscription(
            msg_type=cfg.msg_type,
            topic=cfg.topic,
            callback=functools.partial(self.from_joint_position_to_torch),
            qos_profile=self._qos_profile,
        )

    def from_joint_position_to_torch(self, msg: JointState):
        """Callback function for the Joint Position Command subscriber term that converts JointState msg position
        to torch.tensor used in actions. The order of the joints and positions in the action definition is preserved.

        Args:
            msg: received JointState message
        """
        if self.joint_names is not None:
            name_to_msg_order = {name: index for index, name in enumerate(msg.name)}
            position_ordered = [msg.position[name_to_msg_order[n]] for n in self.joint_names]
        else:
            position_ordered = msg.position

        self.msg_converted[:] = torch.tensor(position_ordered)


class JointEffortCommandSubscriber(SubscriberTerm):
    def __init__(
        self,
        cfg: subscribers_cfg.JointEffortCommandSubscriberCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
    ):
        """Initialize the Joint Effort Command subscriber term.

        Args:
            cfg: The configuration object of type JointEffortCommandSubscriberCfg.
            node: The ros node instance.
            env: The Isaac Lab environment.
            qos_profile: The quality of service profile for ROS communications.
        """
        super().__init__(cfg, node, env, qos_profile)
        self.cfg: subscribers_cfg.JointEffortCommandSubscriberCfg

        # Collect joint names in order and allocate action size
        if self.cfg.action_name is not None:  # if action_name provided allocate accordingly
            action_term = self._env.action_manager.get_term(self.cfg.action_name)
            _, self.joint_names = action_term._asset.find_joints(action_term.cfg.joint_names)
            self.size = len(self.joint_names)
        else:  # else assume total action dimension
            self.size = self._env.action_manager.total_action_dim
            self.joint_names = None

        self.msg_converted = torch.zeros(self.size)

        self.subscriber = node.create_subscription(
            msg_type=cfg.msg_type,
            topic=cfg.topic,
            callback=functools.partial(self.from_joint_effort_to_torch),
            qos_profile=self._qos_profile,
        )

    def from_joint_effort_to_torch(self, msg: JointState):
        """Member Callback function for the Joint Effort Command subscriber term that converts JointState msg effort
        to torch.tensor used in actions. The order of the joints in the action definition is preserved.

        Args:
            msg: received JointState message
        """

        if self.joint_names is not None:
            name_to_msg_order = {name: index for index, name in enumerate(msg.name)}
            effort_ordered = [msg.effort[name_to_msg_order[n]] for n in self.joint_names]
        else:
            effort_ordered = msg.effort

        self.msg_converted[:] = torch.tensor(effort_ordered)


class PDGainsSubscriber(SubscriberTerm):
    """Subscriber term to set P and D gains for the specified asset's actuators.

    Args:
        cfg: The configuration object of type PDGainsSubscriberCfg.
        node: The ros node instance.
        env: The Isaac Lab environment.
        qos_profile: The quality of service profile for ROS communications.
    """

    def __init__(
        self, cfg: subscribers_cfg.PDGainsSubscriberCfg, node: Node, env: ManagerBasedEnv, qos_profile: QoSProfile
    ):
        super().__init__(cfg, node, env, qos_profile)
        self.cfg: subscribers_cfg.PDGainsSubscriberCfg

        self.subscriber = node.create_subscription(
            msg_type=cfg.msg_type,
            topic=cfg.topic,
            callback=functools.partial(self.set_pd_gains, cfg.asset_name),
            qos_profile=self._qos_profile,
        )

        # Store the mapping from joint name to actuator name and joint_id
        self.joint_to_actuator_order = {}
        for actuator_name, actuator in env.scene.articulations[cfg.asset_name].actuators.items():
            for joint_id, joint_name in enumerate(actuator.joint_names):
                self.joint_to_actuator_order[joint_name] = {"actuator_name": actuator_name, "joint_id": joint_id}

    def set_pd_gains(self, asset_name, msg: JointState):
        """Set the P and D gains for the specified asset's actuators.

        Args:
            asset_name: The name of the asset to set the P and D gains for.
            msg: The message containing the P and D gains for the asset's actuators.
                It is assumed that the P and D gains are passed in the JointState
                message in the position and velocity fields, respectively. The order
                of these gains is specified by msg.name.

        """
        # Extract the P and D gains from the message
        stiffness_gains = torch.tensor(msg.position)
        damping_gains = torch.tensor(msg.velocity)

        articulation = self._env.scene.articulations[asset_name]

        # Construct the dictionary to store the new actuator gains
        new_actuator_gains = {}
        for actuator_name, actuator in articulation.actuators.items():
            new_actuator_gains[actuator_name] = {
                "stiffness": torch.zeros(actuator.num_joints),
                "damping": torch.zeros(actuator.num_joints),
            }

        # Reorder the P and D gains according to the joint names in the action definition
        for i, joint_name in enumerate(msg.name):
            # Determine which actuator the joint belongs to and the index into the actuator
            actuator_name = self.joint_to_actuator_order[joint_name]["actuator_name"]
            index_into_actuator = self.joint_to_actuator_order[joint_name]["joint_id"]

            # Set the new P and D gains for the joint in the actuator
            new_actuator_gains[actuator_name]["stiffness"][index_into_actuator] = stiffness_gains[i]
            new_actuator_gains[actuator_name]["damping"][index_into_actuator] = damping_gains[i]

        # Update actuator gains to new values
        for actuator_name, actuator in articulation.actuators.items():
            carb.log_info(f"Setting PD gains for actuator {actuator_name} to {new_actuator_gains[actuator_name]}")
            actuator.stiffness = new_actuator_gains[actuator_name]["stiffness"]
            actuator.damping = new_actuator_gains[actuator_name]["damping"]


class ResetSubscriber(SubscriberTerm):
    def __init__(
        self, cfg: subscribers_cfg.ResetSubscriberCfg, node: Node, env: ManagerBasedEnv, qos_profile: QoSProfile
    ):
        """Initialize the reset subscriber term.

        Example usage publishing from command-line with topic '/reset':
            .. code-block:: bash
                ros2 topic pub --once /reset std_msgs/Bool "{'data': true}"

        Args:
            cfg: The configuration object of type ResetSubscriberCfg.
            node: The ros node instance.
            env: The Isaac Lab environment.
            qos_profile: The quality of service profile for ROS communications
        """
        super().__init__(cfg, node, env, qos_profile)
        self.cfg: subscribers_cfg.ResetSubscriberCfg

        self.subscriber = node.create_subscription(
            msg_type=cfg.msg_type,
            topic=cfg.topic,
            callback=functools.partial(self.reset_env),
            qos_profile=self._qos_profile,
        )

    def reset_env(self, msg: Bool):
        """Resets the environment.

        Args:
            msg: The message that triggers the reset, if the data field
               is set to true
        """
        if msg.data is True:
            self._env.reset()
        else:
            carb.log_warn("Reset message received with data field set to False. Ignoring the reset request.")
