# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from dataclasses import MISSING

from geometry_msgs.msg import (
    PoseStamped,
    TransformStamped,
    TwistStamped,
    Vector3Stamped,
)
from isaaclab.managers import SceneEntityCfg
from isaaclab.sensors.frame_transformer.frame_transformer_cfg import OffsetCfg
from isaaclab.utils import configclass
from rosgraph_msgs.msg import Clock
from sensor_msgs.msg import CameraInfo, Image, JointState
from std_msgs.msg import Float32MultiArray

from . import publishers


@configclass
class PublisherTermCfg:
    class_type: object = MISSING
    """PublisherTerm tied to this configuration"""
    msg_type: object = None
    """Ros message type to be used by the publisher"""
    topic: str = MISSING
    """Ros topic name to be published to"""
    substep: int | None = 1
    """Set the publish substepping n. For every n physics steps this term will publish its messages.
        substep = None will result in publishing once at beginning of simulation."""
    asset_cfg: SceneEntityCfg = SceneEntityCfg("robot")
    """Defines which asset is used. Can optionally be used to define which joints shall be published."""


@configclass
class ClockPublisherCfg(PublisherTermCfg):
    class_type = publishers.ClockPublisher
    """PublisherTerm tied to this configuration"""
    msg_type = Clock
    """Ros message type to be used by the publisher"""
    topic = "/clock"
    """Ros topic name to be published to"""


@configclass
class JointStatePublisherCfg(PublisherTermCfg):
    class_type = publishers.JointStatePublisher
    """PublisherTerm tied to this configuration"""
    msg_type = JointState
    """Ros message type to be used by the publisher"""
    relative: bool = False
    """Boolean to publish either absolute joint states or relative joint states about a default offset"""


@configclass
class LinkPosePublisherCfg(PublisherTermCfg):
    class_type = publishers.LinkPosePublisher
    """PublisherTerm tied to this configuration"""
    msg_type = PoseStamped
    """Ros message type to be used by the publisher"""
    link_name: str | None = None
    """Name of the rigid body link in the Articulation described in the asset_cfg, if not set the base link will be used"""
    world_frame: bool = False
    """If the pose is defined in the world frame or the base frame"""
    offset: OffsetCfg | None = None
    """If the published value needs to be offset w.r.t the link value: The math is :
        offset_pos_w = link_pos_w + torch.matmul(link_rot_w, offset.pos)
        offset_orient_w = math_utils.quat_mul(offset.rot,link_orient_w)
        where link_orient_w and link_rot_w are the quaternion and rotation matrix representation of the link's orientation.
    """


@configclass
class BasePosePublisherCfg(PublisherTermCfg):
    class_type = publishers.BasePosePublisher
    """PublisherTerm tied to this configuration"""
    msg_type = PoseStamped
    """Ros message type to be used by the publisher"""


@configclass
class BaseTwistPublisherCfg(PublisherTermCfg):
    class_type = publishers.BaseTwistPublisher
    """PublisherTerm tied to this configuration"""
    msg_type = TwistStamped
    """Ros message type to be used by the publisher"""
    world_frame: bool = MISSING
    """Whether to publish messages in base or world frame"""


@configclass
class ProjectedGravityPublisherCfg(PublisherTermCfg):
    class_type = publishers.ProjectedGravityPublisher
    """PublisherTerm tied to this configuration"""
    msg_type = Vector3Stamped
    """Ros message type to be used by the publisher"""


@configclass
class RGBImagePublisherCfg(PublisherTermCfg):
    class_type = publishers.RGBImagePublisher
    """PublisherTerm tied to this configuration"""
    msg_type = Image
    """Ros message type to be used by the publisher"""
    encoding = "rgb8"
    """Color encoding to be used in the message: rgb8 or bgr8"""


@configclass
class DepthImagePublisherCfg(PublisherTermCfg):
    class_type = publishers.DepthImagePublisher
    """PublisherTerm tied to this configuration"""
    msg_type = Image
    """Ros message type to be used by the publisher"""
    encoding = "mono8"
    """Depth encoding to be used in the message: mono8, mono16, or 32FC1 """
    threshold: tuple[float, float] | None = (0.0, 20.0)
    """(Minimum , Maximum) depth in meters to limit depth measurements to"""
    scale: float = 1.0
    """Scaling coefficient between meters and uint8(mono8) and uint16(mono16) integer ranges"""


@configclass
class CameraInfoPublisherCfg(PublisherTermCfg):
    class_type = publishers.CameraInfoPublisher
    """PublisherTerm tied to this configuration"""
    msg_type = CameraInfo
    """Ros message type to be used by the publisher"""


@configclass
class TFBroadcasterCfg(PublisherTermCfg):
    class_type = publishers.TFBroadcaster
    """TF Broadcaster term tied to this configuration"""
    topic = "/tf"
    """Topic the TransformBroadcaster is publishing to (for reference only, not configurable)."""
    msg_type = TransformStamped
    """Message type used in TF broadcaster(for reference only, not configurable)."""
    asset_cfg: SceneEntityCfg | list[SceneEntityCfg] = MISSING
    """Defines a list of Articulations and Sensors."""
    additional_prim_paths: list[str] | None = None
    """List of additional prim paths to prims whose TFs that are to be broadcasted."""
    env_path: str = "/World/envs/env_.*"
    """Path for additional_prim_paths to be relative to."""
    child_frame: str | None = None
    """Name of the child frame of the TF"""
@configclass
class BasicTFBroadcasterCfg(PublisherTermCfg):
    class_type = publishers.BasicTFBroadcaster
    """TF Broadcaster term tied to this configuration"""
    topic = "/tf"
    """Topic the TransformBroadcaster is publishing to (for reference only, not configurable)."""
    msg_type = TransformStamped
    parent_frame: str = "map"
    child_frame: str = "base_link"



@configclass
class StaticTFBroadcasterCfg(TFBroadcasterCfg):
    class_type = publishers.StaticTFBroadcaster
    """TF Broadcaster term tied to this configuration"""
    topic = "/static_tf"
    """Topic the StaticTransformBroadcaster is publishing to (for reference only, not configurable)."""
    substep = None
    """The interval upon which static transformations will be broadcast.
    If None, transformations will be published once, at the time ROS is enabled in EvalSim GUI.
    """
    frame_id_path: str | None = None
    """Path to prim (assuming relative to self.env_path) the user
     wants to publish static TF w.r.t. If None, the frame_id of the message
     will be w.r.t. the prim parent."""


@configclass
class ContactForcePublisherCfg(PublisherTermCfg):
    class_type = publishers.ContactForcePublisher
    msg_type = Float32MultiArray
    """ROS message type"""
    sensor_cfg: SceneEntityCfg = MISSING


##
# Observation based publisher configs
##


@configclass
class ObservationPublisherCfg(PublisherTermCfg):
    class_type = publishers.ObservationPublisher
    """PublisherTerm tied to this configuration"""
    obs_group: str = MISSING
    """Observation term to attached to this publisher"""


@configclass
class JointStateObsPublisherCfg(ObservationPublisherCfg):
    class_type = publishers.JointStateObsPublisher
    """PublisherTerm tied to this configuration"""
    msg_type = JointState
    """Ros message type to be used by the publisher"""
    relative: bool = False
    """Boolean to publish either absolute joint states or relative joint states about a default offset"""
    position_obs: str | None = "joint_pos"
    """Joint position observation term"""
    velocity_obs: str | None = "joint_vel"
    """Joint velocity observation term"""
    effort_obs: str | None = "joint_effort"
    """Joint effort observation term"""


@configclass
class LinkPoseObsPublisherCfg(ObservationPublisherCfg):
    class_type = publishers.LinkPoseObsPublisher
    """PublisherTerm tied to this configuration"""
    msg_type = PoseStamped
    """Ros message type to be used by the publisher"""
    link_pose_obs: str = MISSING
    """Name of the link pose observation using the same Articulation described in the asset_cfg"""


@configclass
class TwistObsPublisherCfg(ObservationPublisherCfg):
    class_type = publishers.TwistObsPublisher
    """PublisherTerm tied to this configuration"""
    msg_type = TwistStamped
    """Ros message type to be used by the publisher"""
    frame_id: str = MISSING
    """Name of reference frame of the observations"""
    lin_vel_obs: str = MISSING
    """Name of linear velocity observation term"""
    ang_vel_obs: str = MISSING
    """Name of angular velocity observation term"""
