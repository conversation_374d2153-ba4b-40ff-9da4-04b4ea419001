from typing import Dict, List, TYPE_CHECKING

import functools
import carb
import torch
from isaaclab.envs import ManagerBasedEnv
from isaaclab.utils import configclass
from rclpy.node import Node
from rclpy.qos import QoSProfile
from std_msgs.msg import Bool

from m545_highlevel_interfaces.msg import M545ActuatorCommands
from moleworks_ext.ros.ros_manager.subscribers import SubscriberTerm
from moleworks_ext.ros.ros_manager.subscribers_cfg import SubscriberTermCfg
from moleworks_ext.ros.ros_manager.menzi.menzi_subscribers import MenziCommandsSubscriber

@configclass
class MenziCommandsSubscriberCfg(SubscriberTermCfg):
    """Config class to apply position, velocity, and effort commands from a JointState message."""

    class_type: type = MenziCommandsSubscriber
    msg_type: type = M545ActuatorCommands
    topic: str = "m545_actuator_commands"