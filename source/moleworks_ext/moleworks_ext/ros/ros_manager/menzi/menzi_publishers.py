from __future__ import annotations

from typing import TYPE_CHECKING
import torch
from isaaclab.envs import ManagerBasedEnv
from rclpy.node import Node
from rclpy.qos import QoSProfile
from std_msgs.msg import Header
from m545_lowlevel_interfaces.msg import M545ActuatorState
from m545_highlevel_interfaces.msg import M545Measurements
from moleworks_ext.ros.ros_manager.publishers import PublisherTerm

if TYPE_CHECKING:
    from . import menzi_publishers_cfg

# same as in m545_description package
# TODO: J_STICK or J_DIPPER?
JOINT_ORDERED_NAMES = [
    "J_LF_HAA", "J_LF_HFE", "J_LF_STEER", "J_LF_WHEEL", "J_LH_HFE", "J_LH_HAA",
    "J_LH_STEER", "J_LH_CLAW", "J_LH_WHEEL", "J_<PERSON>_HAA", "J_<PERSON>_HFE", "J_RF_STEER",
    "J_RF_WHEEL", "J_RH_HFE", "J_RH_HAA", "J_RH_STEER", "J_RH_CLAW", "J_RH_WHEEL",
    "J_TURN", "J_BOOM", "J_STICK", "J_TELE", "J_EE_PITCH", "J_EE_ROLL", "J_EE_YAW"
]


class MenziMeasurementsPublisher(PublisherTerm):
    """Publishes the M545Measurements message with actuator states of an articulation."""

    def __init__(
        self,
        cfg: "menzi_publishers_cfg.MenziMeasurementsPublisherCfg",
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the Menzi Measurement publisher term.

        Args:
            cfg: The configuration object of type MenziMeasurementPublisherCfg.
            node: The ROS2 node instance to tie this publisher to.
            env: The Isaac Lab ManagerBased environment.
            qos_profile: The quality of service profile for ROS communications.
            env_idx: The index of the environment, defaults to 0. Used when multiple environments are managed.
        """
        # Call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)

        # Store joint indices and names
        self.joint_names = self.asset.joint_names
        self.joint_indices = self.asset.find_joints(self.joint_names)[0]

        # Initialize the message
        self._msg = self._initialize_msg()  # Changed from self.msg to self._msg

    def _initialize_msg(self):
        """Initialize the M545Measurements message with default values."""
        msg = M545Measurements()
        msg.header = Header()
        msg.actuators = []
        msg.imus = []
        return msg

    def publish(self, obs: torch.tensor | None = None):
        """Publish the M545Measurements message with current joint states.

        Args:
            obs: Optional observation tensor, not used in this publisher.
        """
        # Set the header timestamp
        self._msg.header.stamp = self.get_node_clock_msg()

        # Create a mapping from joint name to its data
        joint_data = {}
        joint_pos = self.asset.data.joint_pos[self._env_idx, self.joint_indices]
        joint_vel = self.asset.data.joint_vel[self._env_idx, self.joint_indices]
        applied_torque = self.asset.data.applied_torque[self._env_idx, self.joint_indices]
        
        
        for idx, joint_name in enumerate(self.joint_names):
            joint_data[joint_name] = {
                'position': joint_pos[idx].item(),
                'velocity': joint_vel[idx].item(),
                'effort': applied_torque[idx].item()
            }

        # Build the actuator states in the specified order, this is nine because u
        # get the same stream as in the machine
        actuator_states = []
        for joint_name in JOINT_ORDERED_NAMES:
            actuator_state = M545ActuatorState()
            actuator_state.joint_name = joint_name
            if joint_name in joint_data:
                data = joint_data[joint_name]
                actuator_state.position = data['position']
                actuator_state.velocity = data['velocity']
                actuator_state.effort = data['effort']
            else:
                # Handle joints that might not be present in the simulation
                actuator_state.position = 0.0
                actuator_state.velocity = 0.0
                actuator_state.effort = 0.0
            actuator_state.pressure_a = 0.0
            actuator_state.pressure_b = 0.0
            actuator_state.status = M545ActuatorState.STATUS_OPERATIONAL
            actuator_states.append(actuator_state)

        # Update the message actuators
        self._msg.actuators = actuator_states

        # Publish the message
        self.publisher.publish(self._msg)