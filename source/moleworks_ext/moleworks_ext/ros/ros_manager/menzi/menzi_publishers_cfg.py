from __future__ import annotations

from typing import List
from isaaclab.utils import configclass
from moleworks_ext.ros.ros_manager.publishers_cfg import PublisherTermCfg
from m545_highlevel_interfaces.msg import M545Measurements
from moleworks_ext.ros.ros_manager.menzi.menzi_publishers import MenziMeasurementsPublisher

@configclass
class MenziMeasurementsPublisherCfg(PublisherTermCfg):
    """Configuration class for MenziMeasurementsPublisher"""
    
    class_type = MenziMeasurementsPublisher
    """PublisherTerm tied to this configuration"""
    
    msg_type = M545Measurements
    """ROS message type to be used by the publisher"""
    
    joint_names: List[str] = []
    """List of joint names to include in the measurement"""