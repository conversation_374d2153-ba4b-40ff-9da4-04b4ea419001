import functools
from typing import TYPE_CHECKING


import carb
import torch
from isaaclab.envs import ManagerBasedEnv
from rclpy.node import Node
from rclpy.qos import QoSProfile

# Assuming M545ActuatorCommands and M545ActuatorCommand are properly imported
from m545_highlevel_interfaces.msg import M545ActuatorCommands
from moleworks_ext.ros.ros_manager.subscribers import SubscriberTerm
from moleworks_ext.common.utils import utils

if TYPE_CHECKING:
    from . import menzi_subscribers_cfg


class MenziCommandsSubscriber(SubscriberTerm):
    def __init__(
        self, cfg: "menzi_subscribers_cfg.MenziCommandsSubscriberCfg", node: Node, env: ManagerBasedEnv, qos_profile: QoSProfile
    ):
        """Initialize the M545 Actuator Command subscriber term.

        Args:
            cfg: The configuration object of type M545ActuatorCommandSubscriberCfg.
            node: The ROS node instance.
            env: The Isaac Lab environment.
            qos_profile: The quality of service profile for ROS communications.
        """
        super().__init__(cfg, node, env, qos_profile)
        self.cfg = cfg

        # Define the joint order based on M545JointEnum and mapJointEnumToName_
        # Collect joint names in order and allocate action size
        # Store joint indices and names
        self.joint_names = self._asset.joint_names

        # Map joint names to their indices in the action tensor
        self.joint_name_to_action_index = utils.create_joint_index_mapping(self._env.action_manager)

        # Initialize the action tensor for a single mode
        self.msg_converted = torch.zeros(len(self.joint_names))  # single mode tensor
        self.current_mode = None  # Track the current mode being used
        
        # Track last message timestamp for debugging
        self.last_msg_timestamp = None
        self.msg_count = 0

        # Create subscriber with modified QoS profile for better real-time performance
        # Ensure the QoS is configured for real-time control
        self.subscriber = node.create_subscription(
            msg_type=cfg.msg_type,
            topic=cfg.topic,
            callback=functools.partial(self.from_m545_actuator_commands_to_torch),
            qos_profile=self._qos_profile,
        )

    def from_m545_actuator_commands_to_torch(self, msg: M545ActuatorCommands):
        """Callback function that converts M545ActuatorCommands msg to torch tensor for actions.

        Args:
            msg: Received M545ActuatorCommands message to fill the action tensor.
        """
        # Get timestamp from the header
        timestamp = msg.header.stamp
        # Calculate time difference if we have a previous timestamp
        if self.last_msg_timestamp is not None:
            time_diff_sec = ((timestamp.sec - self.last_msg_timestamp.sec) + 
                           (timestamp.nanosec - self.last_msg_timestamp.nanosec) / 1e9)
            carb.log_info(f"Time since last message: {time_diff_sec:.4f} seconds")
        
        self.last_msg_timestamp = timestamp
        self.msg_count += 1
        
        # Reset the mode for each new message to avoid stale mode information
        # affecting new commands with potentially different modes
        self.current_mode = None
        
        # carb.log_info(f"Received actuator commands with timestamp: {timestamp}, message #{self.msg_count}")
        
        for actuator_cmd in msg.actuators:
            joint_name = actuator_cmd.joint_name
            mode = actuator_cmd.mode

            if joint_name not in self.joint_name_to_action_index:
                carb.log_warn(f"Received command for unknown joint '{joint_name}'. Ignoring.")
                continue

            joint_idx = self.joint_name_to_action_index[joint_name]

            # Check if mode is changing
            if self.current_mode is None:
                self.current_mode = mode
            elif mode != self.current_mode:
                carb.log_warn(f"Mixed modes in single command not supported. Expected {self.current_mode}, got {mode}.")
                continue

            # Raise an error if piston commands are received
            if mode in [4, 5, 6]:  # Modes 4, 5, 6 are piston commands
                carb.log_error(f"Piston commands for joint '{joint_name}' are not implemented yet!")
                raise NotImplementedError(f"Piston commands for joint '{joint_name}' are not implemented yet!")

            # Update the action tensor based on the mode
            if mode == -1 or mode == 1:  # MODE_NONE or MODE_JOINTPOSITION
                self.msg_converted[joint_idx] = actuator_cmd.position
            elif mode == 2:  # MODE_JOINTVELOCITY
                self.msg_converted[joint_idx] = actuator_cmd.velocity
            elif mode == 3:  # MODE_JOINTTORQUE
                self.msg_converted[joint_idx] = actuator_cmd.effort
            elif mode == 7:  # MODE_CURRENT
                self.msg_converted[joint_idx] = actuator_cmd.current
            else:
                carb.log_warn(f"Unsupported mode {mode} for joint '{joint_name}'. Ignoring.")

    def get_action_tensor(self) -> torch.Tensor:
        """Retrieve the current action tensor.

        Returns:
            torch.Tensor: The action tensor populated with actuator commands.
        """
        return self.msg_converted.clone()
