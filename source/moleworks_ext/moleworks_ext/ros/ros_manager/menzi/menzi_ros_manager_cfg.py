from isaaclab.managers import SceneEntityCfg
from isaaclab.utils import configclass
from moleworks_ext.ros.ros_manager import (
    JointPositionCommandSubscriberCfg,
    JointStateObsPublisherCfg,
    LinkPosePublisherCfg,
    RosManagerCfg,
    TFBroadcasterCfg,
    ClockPublisherCfg,
    ContactForcePublisherCfg,
    BasicTFBroadcasterCfg
    
)
from moleworks_ext.ros.ros_manager.menzi.menzi_publishers_cfg import MenziMeasurementsPublisherCfg
from moleworks_ext.ros.ros_manager.menzi.menzi_subscribers_cfg import MenziCommandsSubscriberCfg
from moleworks_ext.ros.ros_manager.omnigraph_ros_cfg import OmniGraphCameraTermCfg, OmniGraphLidarTermCfg
from moleworks_ext.ros.ros_manager.publishers_cfg import StaticTFBroadcasterCfg, ObservationPublisherCfg
from std_msgs.msg import Float32MultiArray
ENTITY = SceneEntityCfg("robot")
CAMERA = SceneEntityCfg("camera")
LIDAR = SceneEntityCfg("lidar")
CONTACT_SENSOR = SceneEntityCfg("contact_forces")

@configclass
class M545RosManagerCfg(RosManagerCfg):
    # subscribers for joint commands
    joint_commands = MenziCommandsSubscriberCfg(
        topic="m545/actuator_commands"
    )

    # publishers for robot state
    joint_state = MenziMeasurementsPublisherCfg(
        topic="m545/measurements",
    )

    base_pose = LinkPosePublisherCfg(
        topic="base_pose",
        link_name="BASE",
        world_frame='map',
        asset_cfg=ENTITY, 
    )

    # contact_forces = ContactForcePublisherCfg(
    #    topic="contact_forces",
    #    sensor_cfg=CONTACT_SENSOR
    #)
    
    # camera = OmniGraphCameraTermCfg(
    #     asset_cfg=CAMERA,
    #     namespace="camMainView",
    #     enable_rgb=True,
    #     enable_depth=True,
    #     enable_info=True,
    #     rgb_topic="/image_raw",
    #     depth_topic="/depth",
    #     info_topic="/camera_info",
    # )

    lidar = OmniGraphLidarTermCfg(
        asset_cfg=LIDAR,
        namespace="lidar1",
        enable_laser_scan=True,
        enable_point_cloud=True,
        laser_scan_topic="/laser_scan",
        point_cloud_topic="/point_cloud",
    )

    # tf_broadcaster_robot = TFBroadcasterCfg(
    #     asset_cfg=ENTITY,
    #     substep=1
    # )

    tf_broadcaster_cam = TFBroadcasterCfg(
        asset_cfg=CAMERA,
        child_frame="camMainView",
        substep=1
    )

    tf_broadcaster_lidar = TFBroadcasterCfg(
        asset_cfg=LIDAR,
        child_frame="os_sensor",
        substep=1
    )

    tf_broadcaster_lidar = TFBroadcasterCfg(
        asset_cfg=LIDAR,
        child_frame="os1_lidar",
        substep=1
    )

    tf_broadcaster_odom_base_link = BasicTFBroadcasterCfg(
        parent_frame="map",
        child_frame="base_link",
        substep=1
    ) 


    # static_tf_broadcaster_cam = StaticTFBroadcasterCfg(asset_cfg=CAMERA)

    # static_tf_broadcaster = StaticTFBroadcasterCfg(
    #     asset_cfg=ENTITY,
    #     frame_id_path="Robot/CABIN/CAMERA_MAIN",
    #     substep=1
    # )

    # clock_publisher = ClockPublisherCfg()




@configclass
class ExcavationRosManagerCfg(RosManagerCfg):
    # subscribers for joint commands
    joint_commands = MenziCommandsSubscriberCfg(
        topic="m545/actuator_commands"
    )

    # publishers for robot state
    joint_state = MenziMeasurementsPublisherCfg(
        topic="m545/measurements",
    )

    # base_pose = LinkPosePublisherCfg(
    #     topic="base_pose",
    #     link_name="BASE",
    #     world_frame='map',
    #     asset_cfg=ENTITY, 
    # )

    # contact_forces = ContactForcePublisherCfg(
    #     topic="contact_forces",
    #     sensor_cfg=CONTACT_SENSOR
    # )

    lidar = OmniGraphLidarTermCfg(
        asset_cfg=LIDAR,
        namespace="ouster",
        enable_laser_scan=True,
        enable_point_cloud=True,
        laser_scan_topic="/laser_scan",
        point_cloud_topic="/points",
    )

    # tf_broadcaster_robot = TFBroadcasterCfg(
    #     asset_cfg=ENTITY,
    #     substep=1
    # )

    tf_broadcaster_lidar = TFBroadcasterCfg(
        asset_cfg=LIDAR,
        child_frame="os1_lidar",
        substep=1
    )

    tf_broadcaster_odom_base_link = BasicTFBroadcasterCfg(
        parent_frame="map",
        child_frame="base_link",
        substep=1
    ) 

    observation_publisher = ObservationPublisherCfg(
        topic="observation",
        obs_group="policy",
        msg_type=Float32MultiArray,
    )




@configclass
class ExcavationBoulderRosManagerCfg(RosManagerCfg):
    # subscribers for joint commands
    joint_commands = MenziCommandsSubscriberCfg(
        topic="m545/actuator_commands"
    )

    # publishers for robot state
    joint_state = MenziMeasurementsPublisherCfg(
        topic="m545/measurements",
    )

    # base_pose = LinkPosePublisherCfg(
    #     topic="base_pose",
    #     link_name="BASE",
    #     world_frame='map',
    #     asset_cfg=ENTITY, 
    # )

    # contact_forces = ContactForcePublisherCfg(
    #     topic="contact_forces",
    #     sensor_cfg=CONTACT_SENSOR
    # )

    lidar = OmniGraphLidarTermCfg(
        asset_cfg=LIDAR,
        namespace="ouster",
        enable_laser_scan=True,
        enable_point_cloud=True,
        laser_scan_topic="/laser_scan",
        point_cloud_topic="/points",
    )
    camera = OmniGraphCameraTermCfg(
        asset_cfg=CAMERA,
        namespace="camMainView",
        enable_rgb=True,
        enable_depth=True,
        enable_info=True,
        rgb_topic="/image_raw",
        depth_topic="/depth",
        info_topic="/camera_info",
    )

    # tf_broadcaster_robot = TFBroadcasterCfg(
    #     asset_cfg=ENTITY,
    #     substep=1
    # )

    tf_broadcaster_cam = TFBroadcasterCfg(
        asset_cfg=CAMERA,
        child_frame="camMainView",
        substep=1
    )

    tf_broadcaster_lidar = TFBroadcasterCfg(
        asset_cfg=LIDAR,
        child_frame="os1_lidar",
        substep=1
    )

    tf_broadcaster_odom_base_link = BasicTFBroadcasterCfg(
        parent_frame="map",
        child_frame="base_link",
        substep=1
    ) 

    # static_tf_broadcaster_cam = StaticTFBroadcasterCfg(asset_cfg=CAMERA)

    #static_tf_broadcaster = StaticTFBroadcasterCfg(
    #    asset_cfg=ENTITY,
    #    frame_id_path="Robot/CABIN/CAMERA_MAIN",
    #    substep=1
    #)

    observation_publisher = ObservationPublisherCfg(
        topic="observation",
        obs_group="policy",
        msg_type=Float32MultiArray,
    )

    clock_publisher = ClockPublisherCfg()

    # lockstep_timeout = 1 # seconds