# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import math
import warnings
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

import carb
import isaacsim.core.utils.prims as prims_utils
import isaaclab.utils.math as math_utils
import torch
from geometry_msgs.msg import TransformStamped
from isaacsim.core.prims import XFormPrim
from isaaclab.assets import Articulation, RigidObject
from isaaclab.envs import ManagerBasedEnv
from isaaclab.sensors import Camera, ContactSensor, SensorBase
from moleworks_ext.ros.utils import ros_conversions
from rclpy.node import Node
from rclpy.qos import QoSProfile
from std_msgs.msg import Float32MultiArray, MultiArrayDimension, MultiArrayLayout
from tf2_ros import StaticTransformBroadcaster, TransformBroadcaster
from moleworks_ext.common.sensors.rtx_lidar import RtxLidar
if TYPE_CHECKING:
    from . import publishers_cfg


class PublisherTerm(ABC):
    """Base class for ROS2 publishers

    The PublisherTerm class is used to define a base class for other publishers to inherit from.
    The cfg associated with this publisher (and those inherited) will define parameters on how the
    message is created and published acros the ROS network.

    Each inherited publisher definition must define a publishing function."""

    def __init__(
        self,
        cfg: publishers_cfg.PublisherTermCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the ros publisher term.

        Args:
            cfg: The configuration object of type PublisherTermCfg.
            node: The ros node instance to tie this publisher to.
            env: The Isaac Lab ManagerBased environment.
            qos_profile: The quality of service profile for ROS communications.
            env_idx: The index of the environment, defaults to 0. Used when multiple environments are managed.
        """
        # store the inputs
        self._cfg = cfg
        self._node = node
        self._env = env
        self._qos_profile = qos_profile
        self._env_idx = env_idx

        # resolve scene entity
        self.cfg.asset_cfg.resolve(env.scene)
        print("asset cfg: ", self.cfg.asset_cfg)

        # store articulation handle
        self._asset = self._env.scene[self.cfg.asset_cfg.name]

        # set up ros message
        self._msg = self._initialize_msg()

        # set up publisher
        self.publisher = node.create_publisher(
            msg_type=cfg.msg_type, topic=self.topic_name, qos_profile=self._qos_profile
        )

    def close(self):
        del self._env
        del self._asset

    @property
    def cfg(self) -> publishers_cfg.PublisherTermCfg:
        return self._cfg

    @property
    def msg(self) -> publishers_cfg.PublisherTermCfg.msg_type:
        return self._msg

    @property
    def asset(self) -> Articulation:
        return self._asset

    @property
    def topic_name(self):
        return self.cfg.topic.replace("{asset_name}", self.cfg.asset_cfg.name)

    def _initialize_msg(self):
        """Initialize ros message with zeros."""
        return self.cfg.msg_type()

    @abstractmethod
    def publish(self, obs: torch.tensor | None):
        """Publish an messages through ROS."""
        raise NotImplementedError

    def get_node_clock_msg(self):  # -> Time:
        return self._node.get_clock().now().to_msg()


class ClockPublisher(PublisherTerm):
    """Publishes the current time of the simulation environment."""

    def __init__(
        self,
        cfg: publishers_cfg.ClockPublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the clock publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)

    def publish(self, obs: torch.tensor | None = None):
        time = self._env.sim.current_time
        self.msg.clock.sec = math.floor(time)
        self.msg.clock.nanosec = math.floor((time - math.floor(time)) * 1e9)
        self.publisher.publish(self.msg)


class JointStatePublisher(PublisherTerm):
    """Publishes the position, velocity, and effort of an articulation

    The articulation defined in JointStatePublisherCfg.asset_cfg is used to populate these messages.
    This publisher directly pulls from the simulation and publishes to a sensor_msgs.JointState message."""

    def __init__(
        self,
        cfg: publishers_cfg.JointStatePublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the joint state publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)

        # initialize size of joint_state
        self.joint_state = torch.zeros_like(
            torch.vstack((
                self.asset.data.joint_pos[self._env_idx, self.cfg.asset_cfg.joint_ids],
                self.asset.data.joint_vel[self._env_idx, self.cfg.asset_cfg.joint_ids],
                self.asset.data.applied_torque[self._env_idx, self.cfg.asset_cfg.joint_ids],
            ))
        )
        # deprecation warning
        dep_msg = (
            "The JointStatePublisher will be removed in a future release. Please use 'JointStateObsPublisher' instead."
        )
        warnings.warn(dep_msg, DeprecationWarning)
        carb.log_warn(dep_msg)

    def publish(self, obs: torch.tensor | None = None):
        # set joint names
        self.msg.name = (
            self.cfg.asset_cfg.joint_names if self.cfg.asset_cfg.joint_names else self.asset.data.joint_names
        )

        self.joint_state[0] = self.asset.data.joint_pos[self._env_idx, self.cfg.asset_cfg.joint_ids]
        self.joint_state[1] = self.asset.data.joint_vel[self._env_idx, self.cfg.asset_cfg.joint_ids]
        self.joint_state[2] = self.asset.data.applied_torque[self._env_idx, self.cfg.asset_cfg.joint_ids]
        # if the relative flag is enabled, subtract default joint position and velocities from the raw simulation values.
        # this is used by controllers that expected joint states as differences about a static offset
        if self.cfg.relative:
            self.joint_state[0] -= self.asset.data.default_joint_pos[self._env_idx, self.cfg.asset_cfg.joint_ids]
            self.joint_state[1] -= self.asset.data.default_joint_vel[self._env_idx, self.cfg.asset_cfg.joint_ids]

        ros_conversions.torch_to_joint_state(msg=self.msg, obs=self.joint_state, time=self.get_node_clock_msg())
        # publish message
        self.publisher.publish(self.msg)


class LinkPosePublisher(PublisherTerm):
    """Publishes the position and orientation of a link.

    Configuration parameters are used to chose the link of interest and whether the pose
    is in the world or the articulation's base frame."""

    def __init__(
        self,
        cfg: publishers_cfg.LinkPosePublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the link pose publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)
        self.link_name = self.cfg.link_name or self.asset.body_names[0]
        self.link_id = self._get_link_id(self.link_name)
        self.link_pose = torch.zeros(7)
        self.frame_id = "map" if self.cfg.world_frame else self.asset.body_names[0]

        # deprecation warning
        dep_msg = (
            "The LinkPosePublisher will be removed in a future release. Please use 'LinkPoseObsPublisher' instead."
        )
        warnings.warn(dep_msg, DeprecationWarning)
        carb.log_warn(dep_msg)

    def _get_link_id(self, link_name: str) -> int:
        """Retrieve the index of a link name within the asset's body names.

        Args:
            link_name (str): The name of the link to retrieve the index for.

        Returns:
            int: The index of the link name.

        Raises:
            ValueError: If the link name is not found in the asset's body names.
        """
        try:
            return self.asset.body_names.index(link_name)
        except ValueError:
            err_msg = f"Body name: '{link_name}' is not a valid body of asset type: '{self.asset}'"
            carb.log_error(err_msg)
            raise ValueError(err_msg)

    def _calculate_link_pose(self, link_pos_w, link_orient_w) -> torch.tensor:
        """Calculate the pose of a link in the world frame or base frame.

        Args:
            link_pos_w (torch.Tensor): Position of the link in the world frame.
            link_orient_w (torch.Tensor): Orientation of the link in the world frame.

        Returns:
            torch.tensor: The calculated pose of the link.
        """
        if self.cfg.offset:
            link_rot_w = math_utils.matrix_from_quat(link_orient_w)
            return self._calculate_offset_link_pose(link_pos_w, link_orient_w, link_rot_w)
        return self._calculate_standard_link_pose(link_pos_w, link_orient_w)

    def _calculate_offset_link_pose(self, link_pos_w, link_orient_w, link_rot_w) -> torch.tensor:
        """Calculate the pose of a link with an offset applied.

        Args:
            link_pos_w (torch.Tensor): Position of the link in the world frame.
            link_orient_w (torch.Tensor): Orientation of the link in the world frame.
            link_rot_w (torch.Tensor): Rotation matrix of the link in the world frame.

        Returns:
            torch.tensor: The calculated pose of the link with offset applied.
        """
        offset_link_pos_w = link_pos_w + torch.matmul(link_rot_w, torch.Tensor(self.cfg.offset.pos))
        offset_link_orient_w = math_utils.quat_mul(torch.Tensor(self.cfg.offset.rot), link_orient_w)
        if self.cfg.world_frame:
            return torch.cat((offset_link_pos_w, offset_link_orient_w), dim=0)
        return self._calculate_base_frame_pose(offset_link_pos_w, offset_link_orient_w)

    def _calculate_standard_link_pose(self, link_pos_w, link_orient_w) -> torch.tensor:
        """Calculate the standard pose of a link without any offsets.

        Args:
            link_pos_w (torch.Tensor): Position of the link in the world frame.
            link_orient_w (torch.Tensor): Orientation of the link in the world frame.

        Returns:
            torch.tensor: The calculated standard pose of the link.
        """
        if self.cfg.world_frame:
            return torch.cat((link_pos_w, link_orient_w), dim=0)
        return self._calculate_base_frame_pose(link_pos_w, link_orient_w)

    def _calculate_base_frame_pose(self, link_pos_w: torch.Tensor, link_orient_w: torch.Tensor) -> torch.Tensor:
        """Calculate the pose of a link in the base frame.

        Args:
            link_pos_w: Position of the link in the world frame.
            link_orient_w: Orientation of the link in the world frame.

        Returns:
            The calculated pose of the link in the base frame.
        """
        base_pos_w = self.asset.data.root_pos_w[self._env_idx, :]
        base_orient_w = self.asset.data.root_quat_w[self._env_idx, :]
        world_rot_b = math_utils.matrix_from_quat(math_utils.quat_conjugate(base_orient_w))
        link_pos_b = torch.matmul(world_rot_b, link_pos_w - base_pos_w)
        link_orient_b = math_utils.quat_mul(math_utils.quat_conjugate(base_orient_w), link_orient_w)
        return torch.cat((link_pos_b, link_orient_b), dim=0)

    def publish(self, obs: torch.tensor | None):
        """Publish the link pose.

        Args:
            obs: Unused
        """
        link_orient_w = self.asset.data.body_quat_w[self._env_idx, self.link_id, :]
        link_pos_w = self.asset.data.body_pos_w[self._env_idx, self.link_id, :]
        self.link_pose = self._calculate_link_pose(link_pos_w, link_orient_w)
        ros_conversions.torch_to_pose(
            msg=self.msg, obs=self.link_pose, time=self.get_node_clock_msg(), frame_id=self.frame_id
        )
        self.publisher.publish(self.msg)


class BasePosePublisher(PublisherTerm):
    """Publishes the pose of the base link of the articulation in the world frame."""

    def __init__(
        self,
        cfg: publishers_cfg.BasePosePublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the base pose publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)
        self.base_pose = torch.zeros_like(
            torch.cat(
                (self.asset.data.root_pos_w[self._env_idx, :], self.asset.data.root_quat_w[self._env_idx, :]), dim=0
            )
        )
        self.pos_dim = len(self.asset.data.root_pos_w[self._env_idx, :])
        self.quat_dim = len(self.asset.data.root_quat_w[self._env_idx, :])
        self.frame_id = "world"
        dep_msg = "The BasePosePublisher will be removed in a future release. Please use 'LinkPosePublisher' instead."
        warnings.warn(dep_msg, DeprecationWarning)
        carb.log_warn(dep_msg)

    def publish(self, obs: torch.tensor | None):
        # base pose is published as measured from the world frame
        # extract pos and quaternion
        self.base_pose.view(-1)[: self.pos_dim] = self.asset.data.root_pos_w[self._env_idx, :]
        self.base_pose.view(-1)[self.pos_dim :] = self.asset.data.root_quat_w[self._env_idx, :]
        ros_conversions.torch_to_pose(
            msg=self.msg, obs=self.base_pose, time=self.get_node_clock_msg(), frame_id=self.frame_id
        )

        # publish message
        self.publisher.publish(self.msg)


class BaseTwistPublisher(PublisherTerm):
    """Publishes the base twist (linear and angular velocity) of an Articulation
    relative to the world or the base frame."""

    def __init__(
        self,
        cfg: publishers_cfg.BaseTwistPublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the base twist publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)
        self.twist = torch.zeros_like(
            torch.cat(
                (self.asset.data.root_lin_vel_w[self._env_idx, :], self.asset.data.root_ang_vel_w[self._env_idx, :]),
                dim=0,
            )
        )
        self.lin_vel_dim = len(self.asset.data.root_lin_vel_w[self._env_idx, :])
        self.ang_vel_dim = len(self.asset.data.root_ang_vel_w[self._env_idx, :])
        self.root_link_name = self.asset.body_names[0]
        self.frame_id = ""

        # deprecation warning
        dep_msg = (
            "The BaseTwistPublisher will be removed in a future release. Please use a 'TwistObsPublisher' instead."
        )
        warnings.warn(dep_msg, DeprecationWarning)
        carb.log_warn(dep_msg)

    def publish(self, obs: torch.tensor | None):

        # extract pose and quaternion in either base or world frames
        if self.cfg.world_frame:
            lin_vel = self.asset.data.root_lin_vel_w[self._env_idx, :]
            ang_vel = self.asset.data.root_ang_vel_w[self._env_idx, :]
            self.frame_id = "world"
        else:
            lin_vel = self.asset.data.root_lin_vel_b[self._env_idx, :]
            ang_vel = self.asset.data.root_ang_vel_b[self._env_idx, :]
            self.frame_id = self.root_link_name

        self.twist.view(-1)[: self.lin_vel_dim] = lin_vel
        self.twist.view(-1)[self.lin_vel_dim :] = ang_vel

        ros_conversions.torch_to_twist(
            msg=self.msg, obs=self.twist, time=self.get_node_clock_msg(), frame_id=self.frame_id
        )
        # publish message
        self.publisher.publish(self.msg)


class ProjectedGravityPublisher(PublisherTerm):
    """Publishes the projected_gravity property of the ArticulationData associated with the
    described asset."""

    def __init__(
        self,
        cfg: publishers_cfg.ProjectedGravityPublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the projected gravity publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)

    def publish(self, obs: torch.tensor | None):
        # extract projected gravity

        projected_gravity = self.asset.data.projected_gravity_b.view(-1)
        ros_conversions.torch_to_vector3(msg=self.msg, obs=projected_gravity, time=self.get_node_clock_msg())

        # publish message
        self.publisher.publish(self.msg)


class RGBImagePublisher(PublisherTerm):
    """Publishes rgb images generated by a Camera sensor specified in the asset_cfg.

    The Camera must have a valid 'rgb' data stream."""

    def __init__(
        self,
        cfg: publishers_cfg.RGBImagePublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the rgb publisher."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)
        # get camera sensor
        self.camera = self._env.scene[self.cfg.asset_cfg.name]

    def publish(self, obs: torch.tensor | None):
        # get camera data
        rgb_image_tensor = self.camera.data.output["rgb"][self._env_idx]
        # convert camera data
        ros_conversions.torch_to_rgb(
            msg=self.msg, obs=rgb_image_tensor, time=self.get_node_clock_msg(), encoding=self.cfg.encoding
        )
        # publish camera data
        self.publisher.publish(self.msg)

    def close(self):
        """Closes all references made to ManagerBasedEnv / ManagerBasedRLEnv."""
        super().close()
        del self.camera


class DepthImagePublisher(PublisherTerm):
    """Publishes depth images generated by a Camera sensor specified in the asset_cfg.
    The Camera must have a valid 'distance_to_image_plane' data stream."""

    def __init__(
        self,
        cfg: publishers_cfg.DepthImagePublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the depth publisher."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)
        self.camera = self._env.scene[self.cfg.asset_cfg.name]

    def publish(self, obs: torch.tensor | None):
        # get camera data
        depth_image_tensor = self.camera.data.output["distance_to_image_plane"][self._env_idx]
        # convert camera data
        ros_conversions.torch_to_depth(
            msg=self.msg,
            obs=depth_image_tensor,
            time=self.get_node_clock_msg(),
            encoding=self.cfg.encoding,
            threshold=self.cfg.threshold,
            scale=self.cfg.scale,
        )
        # publish camera data
        self.publisher.publish(self.msg)

    def close(self):
        """Closes all references made to ManagerBasedEnv / ManagerBasedRLEnv."""
        super().close()
        del self.camera


class CameraInfoPublisher(PublisherTerm):
    """Publishes the intrinsic properties of the camera specified in the asset_cfg."""

    def __init__(
        self,
        cfg: publishers_cfg.CameraInfoPublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the camera info publisher."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)
        self.prev_pub_time = 0.0
        self.camera = self._env.scene[self.cfg.asset_cfg.name]
        self.extract_camera_info()

    def extract_camera_info(self):
        # Calculate camera intrinsics
        # following conversions: https://docs.omniverse.nvidia.com/isaacsim/latest/features/sensors_simulation/isaac_sim_sensors_camera.html
        prim_path = self.camera.cfg.prim_path
        prim_path = prim_path.replace("env_.*", "env_" + str(self._env_idx))

        focal_length = (
            prims_utils.get_prim_property(prim_path, property_name="focalLength") * 10
        )  # 1/10th of world unit
        horizontal_aperture = (
            prims_utils.get_prim_property(prim_path, property_name="horizontalAperture") * 10
        )  # 1/10th of world unit
        px_size = horizontal_aperture / self.camera.cfg.width

        fx = focal_length / px_size
        fy = fx
        cx = self.camera.cfg.width / 2
        cy = self.camera.cfg.height / 2

        # fill message
        self.msg.height = self.camera.cfg.height
        self.msg.width = self.camera.cfg.width
        # assuming no distortion
        self.msg.distortion_model = "plumb_bob"
        self.msg.d = [0.0, 0.0, 0.0, 0.0, 0.0]  # For "plumb_bob", the 5 parameters are: (k1, k2, t1, t2, k3).
        # Intrinsic camera matrix for the raw (distorted) images.
        self.msg.k = [fx, 0.0, cx, 0.0, fy, cy, 0.0, 0.0, 1.0]
        # Rectification matrix (stereo cameras only) assume monocular identity matrix
        self.msg.r = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 1.0]  # 3x3 row-major matrix
        # Projection matrix assume monocular
        self.msg.p = [fx, 0.0, cx, 0.0, 0.0, fy, cy, 0.0, 0.0, 0.0, 1.0, 0.0]
        # no subsampling
        self.binning_x = 1
        self.binning_y = 1

    def publish(self, obs: torch.tensor | None):
        # publish
        self.publisher.publish(self.msg)


#
# TF broadcasters
#
class BasicTFBroadcaster(PublisherTerm):
    """Broadcasts transforms of articulations and sensors to TF2.

    NOTE: TFBroadcaster transforms all have frame_id="World". The child_frame for each transform is the prim name.

    NOTE: For static frames use :class:`StaticTFBroadcaster`. For increased performance, only publish the frames with dynamic
    parent transform through :class:`TFBroadcaster`, then publish frames with static transforms to parents using the
    StaticTFBroadcaster.

    NOTE: For best performance, users should use a separate robot state publisher that that listens to the joint_state message
    and utilizes a URDF of the robot to calculate link transforms in another state process."""

    def __init__(
        self,
        cfg: publishers_cfg.BasicTFBroadcasterCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the TF broadcaster term. It does not create a publisher to a topic. Instead, it will
        utilize the ROS 2's built-in :class:`TransformBroadcaster`.

        Args:
            cfg: The configuration object.
            node: The ros node instance to tie this publisher to.
            env: The Isaac Lab ManagerBased environment.
            qos_profile: The quality of service profile for ROS communications.
            env_idx: The index of the environment, defaults to 0. Used when multiple environments are managed.
        """
        # store the inputs
        self._cfg: publishers_cfg.BasicTFBroadcasterCfg = cfg
        self._node = node
        self._env = env
        self._qos_profile = qos_profile
        self._env_idx = env_idx

        # create tf broadcaster
        self.tf_broadcaster = TransformBroadcaster(self._node)

        self.parent_frame = cfg.parent_frame
        self.child_frame = cfg.child_frame

    def publish(self, obs: torch.tensor | None) -> None:
        """
        Publish a dynamic transform between two frames.

        Args:
            parent_frame: The frame to transform from.
            child_frame: The frame to transform to.
            translation: A 3-element tensor representing x, y, z translation.
            rotation: A 4-element tensor representing a quaternion (x, y, z, w).
        """
        # Create a TransformStamped message
        tf_msg = TransformStamped()
        
        # Fill the header
        tf_msg.header.stamp = self.get_node_clock_msg()# self._node.get_clock().now().to_msg()
        tf_msg.header.frame_id = self.parent_frame
        tf_msg.child_frame_id = self.child_frame

        # Set translation
        tf_msg.transform.translation.x = float(0)
        tf_msg.transform.translation.y = float(0)
        tf_msg.transform.translation.z = float(0)

        # Set rotation (normalize to ensure valid quaternion)
        #rotation = torch.nn.functional.normalize(rotation, dim=0)
        tf_msg.transform.rotation.x = float(0)
        tf_msg.transform.rotation.y = float(0)
        tf_msg.transform.rotation.z = float(0)
        tf_msg.transform.rotation.w = float(1)

        # Publish the transform
        self.tf_broadcaster.sendTransform(tf_msg)

class TFBroadcaster(PublisherTerm):
    """Broadcasts transforms of articulations and sensors to TF2.

    NOTE: TFBroadcaster transforms all have frame_id="World". The child_frame for each transform is the prim name.

    NOTE: For static frames use :class:`StaticTFBroadcaster`. For increased performance, only publish the frames with dynamic
    parent transform through :class:`TFBroadcaster`, then publish frames with static transforms to parents using the
    StaticTFBroadcaster.

    NOTE: For best performance, users should use a separate robot state publisher that that listens to the joint_state message
    and utilizes a URDF of the robot to calculate link transforms in another state process."""

    def __init__(
        self,
        cfg: publishers_cfg.TFBroadcasterCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the TF broadcaster term. It does not create a publisher to a topic. Instead, it will
        utilize the ROS 2's built-in :class:`TransformBroadcaster`.

        Args:
            cfg: The configuration object.
            node: The ros node instance to tie this publisher to.
            env: The Isaac Lab ManagerBased environment.
            qos_profile: The quality of service profile for ROS communications.
            env_idx: The index of the environment, defaults to 0. Used when multiple environments are managed.
        """
        # store the inputs
        self._cfg: publishers_cfg.TFBroadcasterCfg = cfg
        self._node = node
        self._env = env
        self._qos_profile = qos_profile
        self._env_idx = env_idx

        # create tf broadcaster
        self.tf_broadcaster = TransformBroadcaster(self._node)

        # create list of assets to add to TransformBroadcaster
        self._asset = []
        if not isinstance(self.cfg.asset_cfg, list):
            asset_list = [self.cfg.asset_cfg]
        else:
            asset_list = self.cfg.asset_cfg

        for asset_cfg in asset_list:
            # resolve scene entities
            asset_cfg.resolve(env.scene)

            # store asset(articulation or sensor) handle
            self._asset.append(self._env.scene[asset_cfg.name])

        # add env parent path to additional prims
        self._env_path = self.cfg.env_path.replace("env_.*", f"env_{self._env_idx}")
        self.additional_prim_paths = self._additional_prim_paths()

        # create dictionary of messages
        self.tf_dict = self._initialize_msg()

    def _additional_prim_paths(self) -> list[str]:
        """Gets the list of prim paths with prepended environment path.

        Returns:
            List of prim paths prefixed with base environment path."""

        prim_paths = []
        if self.cfg.additional_prim_paths is not None:
            for prim_path in self.cfg.additional_prim_paths:
                if prim_path[0] == "/":
                    prim_paths.append(f"{self._env_path}{prim_path}")
                else:
                    prim_paths.append(f"{self._env_path}/{prim_path}")

        return prim_paths

    def _get_link_id(self, asset: Articulation, link_name: str) -> int:
        """Retrieve the index of a link name within the asset's body names.

        Args:
            asset: The Articulation object to pull link_name from.
            link_name: The name of the link to retrieve the index for.

        Returns:
            int: The index of the link name.

        Raises:
            ValueError: If the link name is not found in the asset's body names.
        """
        try:
            return asset.body_names.index(link_name)
        except ValueError:
            err_msg = f"Body name: '{link_name}' is not a valid body of asset: '{asset}'"
            carb.log_error(err_msg)
            raise ValueError(err_msg)

    def _get_prim_world_pose_at_path(self, prim_path: str) -> tuple[torch.Tensor, torch.Tensor]:
        """Gets world pose of prim at prim_path.

        Args:
            prim_path: The prim path string.

        Returns:
            A tuple containing the position and orientation of prim w.r.t. World.
            Shape of the tensors are (1, 3) and (1, 4) respectively.

        Returns:
            RuntimeError if provided path is not a valid prim.
        """

        if prims_utils.is_prim_path_valid(prim_path.replace("env_.*", f"env_{self._env_idx}")):
            prim = XFormPrim(prim_path)
            pos, rot = prim.get_world_poses()
            return pos[self._env_idx, ...], rot[self._env_idx, ...]
        else:
            raise RuntimeError(f"Provided prim_path: {prim_path} is not a valid prim.")

    def _get_prim_local_pose_at_path(self, prim_path: str) -> tuple[torch.Tensor, torch.Tensor]:
        """Gets local pose of prim at prim_path.

        Args:
            prim_path: The prim path string.

        Returns:
            A tuple containing the position and orientation of prim w.r.t. parent prim.
            Shape of the tensors are (1, 3) and (1, 4) respectively.

        Returns:
            RuntimeError if provided path is not a valid prim.
        """

        if prims_utils.is_prim_path_valid(prim_path.replace("env_.*", f"env_{self._env_idx}")):
            prim = XFormPrim(prim_path)
            pos, rot = prim.get_local_poses()
            return pos[self._env_idx, ...], rot[self._env_idx, ...]
        else:
            raise RuntimeError(f"Provided prim_path: {prim_path} is not a valid prim.")

    def _tf_prim_at_path_to_world(self, prim_path: str) -> TransformStamped:
        """Gets world pose of prim at prim_path.

        Args:
            prim_path: The prim path string.

        Returns:
            TransformStamped message containing position and orientation relative to World.
        """

        tf_msg = TransformStamped()

        pos_w, quat_w = self._get_prim_world_pose_at_path(prim_path=prim_path)
        transform = torch.cat((pos_w, quat_w), dim=0)
        # convert torch tensor to transform
        ros_conversions.torch_to_transform(
            msg=tf_msg,
            obs=transform,
            time=self.get_node_clock_msg(),
            frame_id=prim_path.split("/")[1],
            child_frame=prim_path.split("/")[-1],
        )
        return tf_msg

    def _initialize_msg(self) -> dict[str, TransformStamped]:
        """Preallocates dictionary of msgs

        Returns:
            A dictionary with order with body name as key and associated message as value.

        Raises:
            TypeError when asset_cfg specifies a type other than Articulation, RigidObject, or SensorBase."""

        # initialize dictionary
        tf_dict: dict[str, TransformStamped] = dict()
        # get clock time
        msg_time = self.get_node_clock_msg()
        # create identity transform
        zero_transform = torch.tensor([0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0])

        # add additional prim_paths to tf dictionary
        if self.additional_prim_paths is not None:
            for path in self.additional_prim_paths:
                tf_dict[path.split("/")[-1]] = self._tf_prim_at_path_to_world(path)

        # world frame id for assets
        frame_id = self._asset[0].cfg.prim_path.split("/")[1]

        for asset in self._asset:
            # if asset is an Articulation or RigidObject, add bodies to list of transforms
            if isinstance(asset, (Articulation, RigidObject)):
                for body in asset.body_names:
                    tf_msg = TransformStamped()
                    ros_conversions.torch_to_transform(
                        msg=tf_msg,
                        obs=zero_transform,
                        time=msg_time,
                        frame_id=frame_id,
                        child_frame=body,
                    )
                    tf_dict[body] = tf_msg

            # if asset is a sensor add sensor pose to list of transforms
            elif isinstance(asset, SensorBase):
                tf_msg = TransformStamped()
                # body = asset.cfg.prim_path.split("/")[-1]
                # Use custom child frame from configuration, or fallback to default
                if self._cfg.child_frame is not None:
                    body = self._cfg.child_frame
                else:
                    body = asset.cfg.prim_path.split("/")[-1]
                # convert torch tensor to transform
                ros_conversions.torch_to_transform(
                    msg=tf_msg,
                    obs=zero_transform,
                    time=msg_time,
                    frame_id=frame_id,
                    child_frame=body,
                )
                tf_dict[body] = tf_msg
            else:
                raise TypeError(
                    f"Asset must be of type Articulation, RigidObject, or SensorBase. Provided: {type(asset)}"
                )
        return tf_dict

    def prepare_tfs(self) -> None:
        """Updates a dictionary of transform messages (self.tf_dict) to be published."""
        # get time
        msg_time = self.get_node_clock_msg()

        # gather TF messages for any additional prim paths
        if self.additional_prim_paths is not None:
            for path in self.additional_prim_paths:
                body = path.split("/")[-1]
                pos_w, quat_w = self._get_prim_world_pose_at_path(prim_path=path)
                transform = torch.cat((pos_w, quat_w), dim=0)
                # convert torch tensor to transform
                ros_conversions.torch_to_transform(
                    msg=self.tf_dict[body],
                    obs=transform,
                    time=self.get_node_clock_msg(),
                    frame_id=path.split("/")[1],
                    child_frame=body,
                )

        # loop through assets and publish relative tfs
        # frame_id = self._asset[0].cfg.prim_path.split("/")[1]
        frame_id = "base_link" # publish from base_link
        for asset in self._asset:

            # if asset is an Articulation or RigidObject, add bodies to list of transforms
            if isinstance(asset, (Articulation, RigidObject)):
                body_states = asset.data.body_state_w
                for i, body in enumerate(asset.body_names):
                    # access link of articulation
                    transform = body_states[self._env_idx, i, 0:7]
                    # convert torch tensor to transform
                    ros_conversions.torch_to_transform(
                        msg=self.tf_dict[body],
                        obs=transform,
                        time=msg_time,
                        frame_id=frame_id,
                        child_frame=body,
                    )

            # if asset is a sensor add sensor pose to list of transforms
            elif isinstance(asset, SensorBase):
                # body = asset.cfg.prim_path.split("/")[-1]
                if self._cfg.child_frame is not None:
                    body = self._cfg.child_frame
                else:
                    body = asset.cfg.prim_path.split("/")[-1]
                # publish camera orientation using ros convention for camera frames
                if isinstance(asset, Camera):
                    sensor_pos_w = asset.data.pos_w.squeeze()
                    sensor_quat_w = asset.data.quat_w_world.squeeze()
                    #
                    w = sensor_quat_w[0]
                    x = sensor_quat_w[1]
                    y = sensor_quat_w[2]
                    z = sensor_quat_w[3]
                    #rotated_quaternion = torch.tensor([w,z,y,-x]) #no working
                    # Apply the rotation:
                    rotated_quaternion = rotate_quaternion_around_y_torch(sensor_quat_w, torch.tensor(torch.pi/2))
                    rotated_quaternion_sec = rotate_quaternion_around_x_torch(rotated_quaternion, torch.tensor(-torch.pi/2))

                    sensor_quat_w = rotated_quaternion_sec # 2 rot: isaac, 1 rot: menzi
                    # Reorder quaternion from [w,x,y,z] to [x,y,z,w]
                    sensor_quat_w = torch.cat((sensor_quat_w[1:], sensor_quat_w[0:1]))
                    
                elif isinstance(asset, RtxLidar):
                    sensor_pos_w = asset.data.pos_w.squeeze()#+torch.tensor([0.9397,0,0.3420,0])
                    sensor_quat_w = asset.data.quat_w_ros.squeeze()
                    rotated_quaternion = rotate_quaternion_around_y_torch(sensor_quat_w, torch.tensor(-torch.pi/2))
                    #rotated_quaternion_sec = rotate_quaternion_around_x_torch(rotated_quaternion, torch.tensor(-torch.pi/4))

                    sensor_quat_w = rotated_quaternion # 2 rot: isaac, 1 rot: menzi
                    # Reorder quaternion from [w,x,y,z] to [x,y,z,w]
                    sensor_quat_w = torch.cat((sensor_quat_w[1:], sensor_quat_w[0:1]))
                else:
                    sensor_pos_w = asset.data.pos_w.squeeze()
                    sensor_quat_w = asset.data.quat_w.squeeze()
                    # Reorder quaternion from [w,x,y,z] to [x,y,z,w]
                    sensor_quat_w = torch.cat((sensor_quat_w[1:], sensor_quat_w[0:1]))
                transform = torch.cat((sensor_pos_w, sensor_quat_w), dim=0)
                # convert torch tensor to transform

                ros_conversions.torch_to_transform(
                    msg=self.tf_dict[body],
                    obs=transform,
                    time=msg_time,
                    frame_id=frame_id,
                    child_frame=body,
                )



    def publish(self, obs: torch.tensor | None) -> None:
        """Prepares and sends transform messages to TF2."""
        self.prepare_tfs()
        self.tf_broadcaster.sendTransform(list(self.tf_dict.values()))

import math

def rotate_quaternion_around_z_torch(quaternion, angle):
    """
    Rotates a quaternion [w, x, y, z] around the z-axis by a given angle.
    
    Args:
        quaternion (torch.Tensor): The original quaternion [w, x, y, z] as a 1D tensor.
        angle (float): The angle in radians to rotate around the z-axis.

    Returns:
        torch.Tensor: The rotated quaternion [w, x, y, z].
    """
    # Ensure quaternion is a 1D tensor
    if quaternion.dim() != 1 or quaternion.size(0) != 4:
        raise ValueError("Input quaternion must be a 1D tensor of size 4.")
    
    # Original quaternion components
    w, x, y, z = quaternion

    # Compute the rotation quaternion for the z-axis
    half_angle = angle / 2
    cos_half_angle = torch.cos(half_angle)
    sin_half_angle = torch.sin(half_angle)
    rotation_quaternion = torch.tensor([cos_half_angle, 0, 0, sin_half_angle], dtype=quaternion.dtype, device=quaternion.device)

    # Perform quaternion multiplication: q' = q_rotation * q_original
    rw, rx, ry, rz = rotation_quaternion

    new_w = rw * w - rx * x - ry * y - rz * z
    new_x = rw * x + rx * w + ry * z - rz * y
    new_y = rw * y - rx * z + ry * w + rz * x
    new_z = rw * z + rx * y - ry * x + rz * w

    return torch.tensor([new_w, new_x, new_y, new_z], dtype=quaternion.dtype, device=quaternion.device)

import torch

def rotate_quaternion_around_x_torch(quaternion, angle):
    """
    Rotates a quaternion [w, x, y, z] around the x-axis by a given angle.
    
    Args:
        quaternion (torch.Tensor): The original quaternion [w, x, y, z] as a 1D tensor.
        angle (float): The angle in radians to rotate around the x-axis.

    Returns:
        torch.Tensor: The rotated quaternion [w, x, y, z].
    """
    # Ensure quaternion is a 1D tensor
    if quaternion.dim() != 1 or quaternion.size(0) != 4:
        raise ValueError("Input quaternion must be a 1D tensor of size 4.")
    
    # Original quaternion components
    w, x, y, z = quaternion

    # Compute the rotation quaternion for the x-axis
    half_angle = angle / 2
    cos_half_angle = torch.cos(half_angle)
    sin_half_angle = torch.sin(half_angle)
    rotation_quaternion = torch.tensor([cos_half_angle, sin_half_angle, 0, 0], dtype=quaternion.dtype, device=quaternion.device)

    # Perform quaternion multiplication: q' = q_rotation * q_original
    rw, rx, ry, rz = rotation_quaternion

    new_w = rw * w - rx * x - ry * y - rz * z
    new_x = rw * x + rx * w + ry * z - rz * y
    new_y = rw * y - rx * z + ry * w + rz * x
    new_z = rw * z + rx * y - ry * x + rz * w

    return torch.tensor([new_w, new_x, new_y, new_z], dtype=quaternion.dtype, device=quaternion.device)


def rotate_quaternion_around_y_torch(quaternion, angle):
    """
    Rotates a quaternion [w, x, y, z] around the y-axis by a given angle.
    
    Args:
        quaternion (torch.Tensor): The original quaternion [w, x, y, z] as a 1D tensor.
        angle (float): The angle in radians to rotate around the y-axis.

    Returns:
        torch.Tensor: The rotated quaternion [w, x, y, z].
    """
    # Ensure quaternion is a 1D tensor
    if quaternion.dim() != 1 or quaternion.size(0) != 4:
        raise ValueError("Input quaternion must be a 1D tensor of size 4.")
    
    # Original quaternion components
    w, x, y, z = quaternion

    # Compute the rotation quaternion for the y-axis
    half_angle = angle / 2
    cos_half_angle = torch.cos(half_angle)
    sin_half_angle = torch.sin(half_angle)
    rotation_quaternion = torch.tensor([cos_half_angle, 0, sin_half_angle, 0], dtype=quaternion.dtype, device=quaternion.device)

    # Perform quaternion multiplication: q' = q_rotation * q_original
    rw, rx, ry, rz = rotation_quaternion

    new_w = rw * w - rx * x - ry * y - rz * z
    new_x = rw * x + rx * w + ry * z - rz * y
    new_y = rw * y - rx * z + ry * w + rz * x
    new_z = rw * z + rx * y - ry * x + rz * w

    return torch.tensor([new_w, new_x, new_y, new_z], dtype=quaternion.dtype, device=quaternion.device)





class StaticTFBroadcaster(TFBroadcaster):
    """Broadcasts static transforms of articulations and sensors to TF2.

    NOTE: StaticTFBroadcaster by default broadcasts the root link frame of the articulation relative to World and
    sensor frames relative to their parent prim. To change the relative source frame, set the frame_id_path parameter of
    :class:`StaticTFBroadcasterCfg`

    Static frames are meant to be published infrequently. Use :class:`StaticTFBroadcaster.substep` to set frequency.
    For dynamic frames use :class:`TFBroadcaster`. For increased performance only publish the Frames with dynamics
    parent transform through :class:`TFBroadcaster`. Then publish frames with static transforms to parents using the
    :class:`StaticTFBroadcaster`.
    """

    def __init__(
        self,
        cfg: publishers_cfg.StaticTFBroadcasterCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ) -> None:
        """Initialize the TF broadcaster term. It does not create a publisher to a topic. Instead it will
        utilize the tf_broadcaster.

        Args:
            cfg: The configuration object of type PublisherTermCfg.
            node: The ros node instance to tie this publisher to.
            env: The Isaac Lab ManagerBased environment.
            qos_profile: The quality of service profile for ROS communications.
            env_idx: The index of the environment, defaults to 0. Used when multiple environments are managed.
        """
        super().__init__(cfg, node, env, qos_profile, env_idx)

        self.tf_broadcaster = StaticTransformBroadcaster(self._node)

    def _initialize_msg(self) -> dict[str, TransformStamped]:
        """Preallocates dictionary of msgs

        Returns:
            A dictionary with order with body name as key and associated message as value.

        Raises:
            TypeError when asset_cfg specifies a type other than Articulation, RigidObject, or SensorBase."""

        # initialize dictionary
        tf_dict: dict[str, TransformStamped] = dict()

        # preemptively add environment -> World transform
        tf_dict[self._env_path.split("/")[-1]] = self._tf_prim_at_path_to_world(self._env_path)

        # get pose of prim at frame_id w.r.t World
        if self.cfg.frame_id_path is not None:
            self.frame_id_path = self._env_path + "/" + self.cfg.frame_id_path
        else:
            self.frame_id_path = None

        # get clock time
        msg_time = self.get_node_clock_msg()

        # create identity transform
        zero_transform = torch.tensor([0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0])

        # add additional prim_paths to tf dictionary
        if self.additional_prim_paths is not None:
            for path in self.additional_prim_paths:
                tf_msg = TransformStamped()
                body = path.split("/")[-1]
                frame_id_ = path.split("/")[-2] if self.frame_id_path is None else self.frame_id_path.split("/")[-1]
                ros_conversions.torch_to_transform(
                    msg=tf_msg,
                    obs=zero_transform,
                    time=msg_time,
                    frame_id=frame_id_,
                    child_frame=body,
                )
                tf_dict[body] = tf_msg

        for asset in self._asset:
            # if asset is an Articulation or RigidObject, add bodies to list of transforms
            if isinstance(asset, (Articulation, RigidObject)):
                tf_msg = TransformStamped()
                body = asset.body_names[0]
                frame_id_ = (
                    asset.cfg.prim_path.split("/")[-3]
                    if self.frame_id_path is None
                    else self.frame_id_path.split("/")[-1]
                )
            # if asset is a sensor add sensor pose to list of transforms
            elif isinstance(asset, SensorBase):
                tf_msg = TransformStamped()
                body = asset.cfg.prim_path.split("/")[-1]
                frame_id_ = (
                    asset.cfg.prim_path.split("/")[-2]
                    if self.frame_id_path is None
                    else self.frame_id_path.split("/")[-1]
                )
            else:
                raise TypeError(
                    f"Asset must be of type Articulation, RigidObject, or SensorBase. Provided: {type(asset)}"
                )

            ros_conversions.torch_to_transform(
                msg=tf_msg,
                obs=zero_transform,
                time=msg_time,
                frame_id=frame_id_,
                child_frame=body,
            )
            tf_dict[body] = tf_msg

        return tf_dict

    def _prepare_asset_tfs(self) -> None:
        """Prepares TransformStamped messages for assets provided in self.cfg.asset_cfg and stores
        them in self.tf_dict."""
        # get time
        msg_time = self.get_node_clock_msg()

        # loop through provided assets and get transform from world to asset T_wa
        for asset in self._asset:
            # if asset is an Articulation, add the root link to the static publisher
            if isinstance(asset, (Articulation, RigidObject)):
                # access root link of articulation
                child_name_ = asset.body_names[0]
                root_quat_w = asset.data.root_quat_w[self._env_idx, :]
                root_pos_w = asset.data.root_pos_w[self._env_idx, :]

                # change relative transform
                if self.frame_id_path is None:
                    transform = torch.cat((root_pos_w, root_quat_w), dim=0)
                    frame_id_ = asset.cfg.prim_path.split("/")[-3]
                else:
                    root_pos_frame_id, root_quat_frame_id = math_utils.subtract_frame_transforms(
                        t01=self.frame_id_pos_w, q01=self.frame_id_quat_w, t02=root_pos_w, q02=root_quat_w
                    )
                    transform = torch.cat((root_pos_frame_id, root_quat_frame_id), dim=0)
                    frame_id_ = self.frame_id_path.split("/")[-1]

            # if asset is a sensor add sensor pose to list of transforms
            elif isinstance(asset, SensorBase):
                s_prim_path = asset.cfg.prim_path
                s_prim_path_list = s_prim_path.split("/")
                p_prim_path = "/".join(s_prim_path_list[:-1])
                child_name_ = s_prim_path_list[-1]

                # publish camera orientation using ros convention for camera frames
                if isinstance(asset, Camera):
                    s_pos_p = torch.tensor(asset.cfg.offset.pos)
                    s_quat_p = torch.tensor(asset.cfg.offset.rot, dtype=torch.float32).unsqueeze(0)
                    s_quat_p = math_utils.convert_orientation_convention(
                        s_quat_p, origin=asset.cfg.offset.convention, target="ros"
                    )
                    s_quat_p = s_quat_p.squeeze()
                else:
                    s_pos_p = torch.tensor(asset.cfg.offset.pos)
                    s_quat_p = torch.tensor(asset.cfg.offset.rot)

                # change relative transform
                if self.frame_id_path is None:
                    transform = torch.cat((s_pos_p, s_quat_p), dim=0)
                    frame_id_ = asset.cfg.prim_path.split("/")[-2]
                else:
                    # get parent pose w.r.t world
                    p_pos_w, p_quat_w = self._get_prim_world_pose_at_path(p_prim_path)
                    # convert to parent pose w.r.t frame_id
                    p_pos_frame_id, p_quat_frame_id = math_utils.subtract_frame_transforms(
                        t01=self.frame_id_pos_w, q01=self.frame_id_quat_w, t02=p_pos_w, q02=p_quat_w
                    )
                    # convert to sensor pose w.r.t frame_id
                    s_pos_frame_id, s_quat_frame_id = math_utils.combine_frame_transforms(
                        t01=p_pos_frame_id, q01=p_quat_frame_id, t12=s_pos_p, q12=s_quat_p
                    )
                    transform = torch.cat((s_pos_frame_id, s_quat_frame_id), dim=0)
                    frame_id_ = self.frame_id_path.split("/")[-1]

            # convert torch tensor to transform message
            ros_conversions.torch_to_transform(
                msg=self.tf_dict[child_name_],
                obs=transform,
                time=msg_time,
                frame_id=frame_id_,
                child_frame=child_name_,
            )

    def _prepare_prim_tfs(self) -> None:
        """Prepares TransformStamped messages for prims provided in self.cfg.additional_prim_paths
        and stores them in self.tf_dict."""

        # look through prim_paths and publish static tfs
        if self.additional_prim_paths is not None:
            for c_prim_path in self.additional_prim_paths:
                c_prim_path_list = c_prim_path.split("/")
                c_prim_name = c_prim_path_list[-1]
                p_prim_path_list = c_prim_path_list[:-1]

                # choose between provided parent prim in prims direct parent or self.frame_id
                if self.frame_id_path is None:
                    frame_id_ = c_prim_path_list[-2]
                    p_prim_path = "/".join(p_prim_path_list)

                    # call to update parent prim before getting child to parent frame
                    # TODO: figure out why prim poses are not updating in the GUI and in
                    # the backend without a direct call.
                    XFormPrim(p_prim_path)
                    # get child pose w.r.t world
                    c_pos_p, c_quat_p = self._get_prim_local_pose_at_path(c_prim_path)
                else:
                    frame_id_ = self.frame_id_path.split("/")[-1]
                    p_prim_path = self.frame_id_path

                    # get parent pose w.r.t world
                    p_pos_w, p_quat_w = self._get_prim_world_pose_at_path(p_prim_path)

                    # get child pose w.r.t world
                    c_pos_w, c_quat_w = self._get_prim_world_pose_at_path(c_prim_path)

                    # convert to child relative to parent frame
                    c_pos_p, c_quat_p = math_utils.subtract_frame_transforms(
                        t01=p_pos_w, q01=p_quat_w, t02=c_pos_w, q02=c_quat_w
                    )

                transform = torch.cat((c_pos_p, c_quat_p), dim=0)

                # convert torch tensor to transform message
                ros_conversions.torch_to_transform(
                    msg=self.tf_dict[c_prim_name],
                    obs=transform,
                    time=self.get_node_clock_msg(),
                    frame_id=frame_id_,
                    child_frame=c_prim_name,
                )

    def prepare_tfs(self) -> None:
        """Updates a dictionary of transform messages (self.tf_dict) to be published."""
        # calculate frame_id transform
        if self.frame_id_path is not None:
            self.frame_id_pos_w, self.frame_id_quat_w = self._get_prim_world_pose_at_path(self.frame_id_path)
        # prepare tf messages for assets from self.asset_cfg
        self._prepare_asset_tfs()
        # parepare tf messages for prims in self.prim_paths
        self._prepare_prim_tfs()


class ContactForcePublisher(PublisherTerm):
    """
    A class to publish contact force messages in a ROS environment.

    This class initializes and publishes contact force messages based on the configuration
    provided. It validates the configuration against the contact sensor's body names and
    publishes the net contact forces.

    Attributes:
        cfg: Configuration for the contact force publisher.
        node: ROS node to which the publisher is attached.
        env: Environment containing the scene and sensors.
        qos_profile: Quality of Service profile for the ROS publisher.
        env_idx: Index of the environment instance. Defaults to 0.
    """

    def __init__(
        self,
        cfg: publishers_cfg.ContactForcePublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """
        Initialize the contact force message info publisher.

        Args:
            cfg: Configuration for the contact force publisher.
            node: ROS node to which the publisher is attached.
            env: Environment containing the scene and sensors.
            qos_profile: Quality of Service profile for the ROS publisher.
            env_idx: Index of the environment instance. Defaults to 0.

        Raises:
            ValueError: If the requested link's contact sensor doesn't exist.
        """
        super().__init__(cfg, node, env, qos_profile, env_idx)

        self._contact_sensor: ContactSensor = self._env.scene.sensors[self.cfg.sensor_cfg.name]

        # Get the list of body names from the sensor configuration
        body_names_cfg = self._env.scene[self.cfg.sensor_cfg.name].body_names

        # Get the list of body names from the contact sensor
        body_names_sensor = self._contact_sensor.body_names
        #body_names_sensor = self._env.scene[self.cfg.sensor_cfg.name].body_names

        # Check if all body names in the config are present in the contact sensor body names
        missing_names = [entry for entry in body_names_cfg if entry not in body_names_sensor]

        # Raise an error if there are any missing names
        if len(missing_names) > 0:
            raise ValueError(
                f"Cannot initialize ContactForcePublisher - the following body names: {', '.join(missing_names)} are"
                " missing from `ContactSensor.body_names`"
            )

        # If no names are missing, create the list of body IDs
        self.cfg.sensor_cfg.body_ids = [
            i for entry in body_names_cfg for i, e in enumerate(body_names_sensor) if e == entry
        ]

    def _initialize_msg(self):
        """
        Initialize ROS message with zeros of type Float32MultiArray.

        Returns:
            Float32MultiArray: Initialized ROS message with layout and zero data, the label includes in the name of the links.
        """
        msg = Float32MultiArray()
        dim1 = MultiArrayDimension()

        body_names = self._env.scene[self.cfg.sensor_cfg.name].body_names
        combined_string = " ".join(body_names)
        dim1.label = "Link names: " + combined_string

        dim1.size = len(body_names)
        dim1.stride = len(body_names) * 3

        dim2 = MultiArrayDimension()
        dim2.label = "Forces"
        dim2.size = 3
        dim2.stride = 3

        layout = MultiArrayLayout()
        layout.dim = [dim1, dim2]
        layout.data_offset = 0

        msg.layout = layout
        return msg

    def publish(self, obs: torch.tensor | None):
        """
        Publish contact force data.

        Args:
            obs: Observation data. Can be None.
        """

        net_contact_forces = self._contact_sensor.data.net_forces_w[:, self.cfg.sensor_cfg.body_ids, :]
        self.msg.data = net_contact_forces.flatten().tolist()
        self.publisher.publish(self.msg)


##
# Observation Based Publishers
##


class ObservationPublisher(PublisherTerm):
    """Base class for defining publishers based upon IsaacLab observation terms.

    Utilizes a dict[str, dict[str, torch.Tensor]] format for the observation output. This allows users to pass
    in the observation group and observation terms to the publisher configs to facilitate extraction of
    desired observation data. The user is expected to create accompanying observations for this publisher to access.

    NOTE: In your ManagerBasedEnvCfg, be sure to add the following in the __post_init__ method.

    # disable observation concatenation - REQUIRED for observation based publishers
    self.observations.policy.concatenate_terms = False
    """

    def __init__(
        self,
        cfg: publishers_cfg.ObservationPublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the observation publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)

        # set up conversion function
        self.to_ros_msg = ros_conversions.TO_ROS_MSG[cfg.msg_type]

    def _initialize_msg(self):
        """Initialize ros message with zeros."""
        if self.cfg.msg_type == Float32MultiArray:
            dim = (MultiArrayDimension(size=self._env.observation_manager.group_obs_dim[self.cfg.obs_group][0]),)
            msg = Float32MultiArray(layout=MultiArrayLayout(dim=dim))
        else:
            msg = self.cfg.msg_type()
        return msg

    def prepare_msg(self, obs: dict[str, dict[str, torch.Tensor]]):
        try:
            self.to_ros_msg(self.msg, obs[self.cfg.obs_group][0].to("cpu"))
        except KeyError as e:
            warnings.warn(f"KeyError: {e}. The observation group '{self.cfg.obs_group}' is missing.", UserWarning)

    def publish(self, obs: dict[str, dict[str, torch.Tensor]]):
        """Publish the observation message

        Args:
            obs: Complete observation output dictionary(dict[str, dict[str, torch.Tensor]]).
        """
        # prepare message
        self.prepare_msg(obs)
        # publish message
        self.publisher.publish(self.msg)


class JointStateObsPublisher(ObservationPublisher):
    """Publishes observation data that is in the form of joint position, velocity, and effort.

    The user is expected to properly define observations associated with joint position, velocity, and effort
    separately in the same observation group. The names of the observation group and the names of the observation
    terms are used to pass information to the JointStateObsPublisher allowing the publisher to access the correct
    observation. By utilizing the JointStateObsPublisher instead of the JointStatePublisher, users can take advantage
    of the data corruption/realism features of observations (e.g. noise, bias, etc) as well as customize the
    observation calculations (i.e. relative or absolute).

    NOTE: In your ManagerBasedEnvCfg, be sure to add the following in the __post_init__ method.

    # disable observation concatenation - REQUIRED for observation based publishers
    self.observations.policy.concatenate_terms = False

    Example:
    In your ObservationGroup define the joint state observations

    from isaaclab.envs.mdp import observations as obs
    from rai.eval_sim.envs.mdp import observations as evalsim_obs

    @configclass
    class ObservationsCfg:

        @configclass
        class PolicyCfg(ObsGroup):

            joint_pos = ObsTerm(func=obs.joint_pos)
            joint_vel = ObsTerm(func=obs.joint_vel)
            joint_effort = ObsTerm(func=evalsim_obs.joint_effort)

        # observation groups
        policy: PolicyCfg = PolicyCfg()

    In your RosManagerCfg define the joint state publisher
    class ExampleManagerCfg(RosManagerCfg):
        joint_state = JointStateObsPublisherCfg(
            topic="/joint_state",
            asset_cfg=SceneEntityCfg("robot"),
            obs_group="policy",
            position_obs="joint_pos",
            velocity_obs="joint_vel",
            effort_obs="joint_effort",
        )
    """

    def __init__(
        self,
        cfg: publishers_cfg.JointStateObsPublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the joint state publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)

        # initialize size of joint_state
        self.joint_state = torch.zeros_like(
            torch.vstack((
                self.asset.data.joint_pos[self._env_idx, self.cfg.asset_cfg.joint_ids],
                self.asset.data.joint_vel[self._env_idx, self.cfg.asset_cfg.joint_ids],
                self.asset.data.applied_torque[self._env_idx, self.cfg.asset_cfg.joint_ids],
            ))
        )

    def prepare_msg(self, obs: dict[str, dict[str, torch.Tensor]]):
        """Helper function to extract the observation data and format into joint_state
            tensor filling self.joint_state.

        Args:
            obs: Complete observation output dictionary (dict[str, dict[str, torch.Tensor]]).
        """
        # populate joint name information
        self.msg.name = (
            self.cfg.asset_cfg.joint_names if self.cfg.asset_cfg.joint_names else self.asset.data.joint_names
        )

        # extract and populate the joint position, velocity, and effort
        if self.cfg.position_obs is not None:
            self.joint_state[0] = obs[self.cfg.obs_group][self.cfg.position_obs]
        if self.cfg.velocity_obs is not None:
            self.joint_state[1] = obs[self.cfg.obs_group][self.cfg.velocity_obs]
        if self.cfg.effort_obs is not None:
            self.joint_state[2] = obs[self.cfg.obs_group][self.cfg.effort_obs]

        # convert tensor to ros message
        ros_conversions.torch_to_joint_state(msg=self.msg, obs=self.joint_state, time=self.get_node_clock_msg())


class LinkPoseObsPublisher(ObservationPublisher):
    """Publishes observation data that is in the form of link position and orientation (quaternion) [x y z qw qx qy qz].

    The user is expected to properly define observations associated with pose. The names of the observation group and the
    observation terms are used to pass information to the LinkPoseObsPublisher allowing the publisher to access the correct
    observation. By utilizing the LinkPoseObsPublisher instead of the LinkPosePublisher, users can take advantage of the
    data corruption/realism features of observations (e.g. noise, bias, etc) as well as customize the observation calculations.

    NOTE: In your ManagerBasedEnvCfg, be sure to add the following in the __post_init__ method.

    # disable observation concatenation - REQUIRED for observation based publishers
    self.observations.policy.concatenate_terms = False

    Example:
    In your ObservationGroup define the link pose observations

    from rai.eval_sim.envs.mdp import observations as evalsim_obs

    @configclass
    class ObservationsCfg:

        @configclass
        class PolicyCfg(ObsGroup):

            base_link_pose = ObsTerm(func=evalsim_obs.link_pose, params={"link_name": "base"})

        # observation groups
        policy: PolicyCfg = PolicyCfg()

    In your RosManagerCfg define the link pose publisher

    class ExampleManagerCfg(RosManagerCfg):
        base_pose = LinkPoseObsPublisherCfg(
            topic="/base_pose",
            obs_group="policy",
            asset_cfg=ENTITY,
            link_pose_obs="base_link_pose"
        )
    """

    def __init__(
        self,
        cfg: publishers_cfg.LinkPoseObsPublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the link pose publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)

        self.link_pose = torch.zeros(7)
        self.frame_id = "world"

    def prepare_msg(self, obs: dict[str, dict[str, torch.Tensor]]):
        """Helper function to prepare link pose message, extracting from observation dictionary
        and filling publisher message.

        Args:
            obs: Complete obsercation output dictionary (dict[str, dict[str, torch.Tensor]]).
        """

        # extract link pose from observations
        self.link_pose = torch.squeeze(obs[self.cfg.obs_group][self.cfg.link_pose_obs])
        # convert and apply to message
        ros_conversions.torch_to_pose(
            msg=self.msg, obs=self.link_pose, time=self.get_node_clock_msg(), frame_id=self.frame_id
        )


class TwistObsPublisher(ObservationPublisher):
    """Publishes observation data that is in the form of linear velocity and angular velocity [x y z qw qx qy qz].

    The user is expected to properly define observations associated with each velocity. The names of the
    observation group and the observation terms are used to pass information to the TwistObsPublisher
    allowing the publisher to access the correct observation. By utilizing the TwistObsPublisher
    instead of the TwistPublisher, users can take advantage of the data corruption/realism features
    of observations (e.g. noise, bias, etc) as well as customize the observation calculations.

    NOTE: In your ManagerBasedEnvCfg, be sure to add the following in the __post_init__ method.

    # disable observation concatenation - REQUIRED for observation based publishers
    self.observations.policy.concatenate_terms = False

    Example:
    In your ObservationGroup define the linear and angular velocity observations.

    from rai.eval_sim.envs.mdp import observations as evalsim_obs

    @configclass
    class ObservationsCfg:

        @configclass
        class PolicyCfg(ObsGroup):

            base_lin_vel = ObsTerm(func=mdp.base_lin_vel)
            base_ang_vel = ObsTerm(func=mdp.base_ang_vel)

        # observation groups
        policy: PolicyCfg = PolicyCfg()

    In your RosManagerCfg define the twist publisher.

    class ExampleManagerCfg(RosManagerCfg):
        base_twist = TwistObsPublisherCfg(
            topic="base_twist",
            asset_cfg=ENTITY,
            obs_group="policy",
            frame_id="world",
            lin_vel_obs="base_lin_vel",
            ang_vel_obs="base_ang_vel",
        )

    """

    def __init__(
        self,
        cfg: publishers_cfg.TwistObsPublisherCfg,
        node: Node,
        env: ManagerBasedEnv,
        qos_profile: QoSProfile,
        env_idx: int = 0,
    ):
        """Initialize the base twist publisher term."""
        # call the base class constructor
        super().__init__(cfg, node, env, qos_profile, env_idx)
        self.twist = torch.zeros_like(
            torch.cat(
                (self.asset.data.root_lin_vel_w[self._env_idx, :], self.asset.data.root_ang_vel_w[self._env_idx, :]),
                dim=0,
            )
        )
        self.frame_id = self.cfg.frame_id

    def prepare_msg(self, obs: dict[str, dict[str, torch.Tensor]]):
        """Helper function to prepare Twist message, extracting from observation dictionary
        and filling publisher message.

        Args:
            obs: Complete observation output dictionary (dict[str, dict[str, torch.Tensor]]).
        """

        # extract pose and quaternion from observation
        self.twist.view(-1)[:3] = torch.squeeze(obs[self.cfg.obs_group][self.cfg.lin_vel_obs])
        self.twist.view(-1)[3:] = torch.squeeze(obs[self.cfg.obs_group][self.cfg.ang_vel_obs])
        # convert and apply to message
        ros_conversions.torch_to_twist(
            msg=self.msg, obs=self.twist, time=self.get_node_clock_msg(), frame_id=self.frame_id
        )
