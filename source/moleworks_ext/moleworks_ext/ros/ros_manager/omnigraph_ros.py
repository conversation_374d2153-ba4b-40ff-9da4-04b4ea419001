# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

import omni.graph.core as og
import usdrt.Sdf
from isaaclab.envs import ManagerBasedEnv

if TYPE_CHECKING:
    from . import omnigraph_ros_cfg


class OmniGraphTerm(ABC):
    def __init__(self, cfg: omnigraph_ros_cfg.OmniGraphTermCfg, env: ManagerBasedEnv, env_idx: int = 0) -> None:
        """Initializes the Omnigraph term.

        Args:
            cfg: The configuration object
            env: The orbit environment
            env_indx: The index of the environment, used when multiple environments are managed. Defaults to 0.
        """
        # store inputs
        self._cfg = cfg
        self._env = env
        self._env_idx = env_idx

        # resolve scene entity
        self.cfg.asset_cfg.resolve(env.scene)

        self._create_omnigraph()

    def close(self):
        del self._env

    @property
    def cfg(self) -> omnigraph_ros_cfg.OmniGraphTermCfg:
        return self._cfg

    @abstractmethod
    def _create_omnigraph(self):
        """Create Omnigraph."""
        raise NotImplementedError


class OmniGraphCameraTerm(OmniGraphTerm):
    def __init__(self, cfg: omnigraph_ros_cfg.OmniGraphCameraTermCfg, env: ManagerBasedEnv, env_idx: int = 0):
        super().__init__(cfg, env, env_idx)
        """Creates an Omnigraph graph to publish camera data.

        It does this by reading in a OmniGraphCameraTermCfg that contains enables and
        parameters for different camera data modes. Currently supported outputs are RBG,
        Pseudo-depth, and camera info. When creating the OmniGraphCameraTermCfg take care to
        set the asset_cfg to a Camera created using a CameraCfg. When creating the CameraCfg
        be sure to set data_types=[]. This prevents duplicate RenderProducts used in the
        Synthetic Data Pipeline. No error will occur if data_types is non-empty, but
        performance will be slower.

        The Camera publish rate is controlled by the rendering rate of the simulation.
        """

    def _create_omnigraph(self):
        """Creates the configurable Camera Publisher Omnigraph"""

        prim_path = self._env.scene[self.cfg.asset_cfg.name].cfg.prim_path
        camera_prim = prim_path.replace("env_.*", "env_" + str(self._env_idx))
        print("camera prim_path", prim_path)
        print("camera camera_prim", camera_prim)
        camera_name = camera_prim.split("/")[-1]
        height = self._env.scene[self.cfg.asset_cfg.name].cfg.height
        width = self._env.scene[self.cfg.asset_cfg.name].cfg.width

        # define graph prim name
        if self.cfg.graph_name is None:
            graph_name = "/ActionGraph_" + camera_name
        else:
            graph_name = self.cfg.graph_name

        # initialize nodes, connections, and values lists
        nodes = list()
        connect = list()
        values = list()

        # setup execution via Physics step NOTE: this does not control rate of execution for rendering products
        # setup Rendering Product basics
        nodes.append(("OnPhysicsStep", "isaacsim.core.nodes.OnPhysicsStep"))
        nodes.append(("RenderProduct", "isaacsim.core.nodes.IsaacCreateRenderProduct"))

        connect.append(("OnPhysicsStep.outputs:step", "RenderProduct.inputs:execIn"))

        values.append(("RenderProduct.inputs:cameraPrim", [usdrt.Sdf.Path(camera_prim)]))
        values.append(("RenderProduct.inputs:height", height))
        values.append(("RenderProduct.inputs:width", width))

        # RGB publishing
        if self.cfg.enable_rgb:
            nodes.append(("cameraHelperRgb", "isaacsim.ros2.bridge.ROS2CameraHelper"))
            connect.append(("RenderProduct.outputs:renderProductPath", "cameraHelperRgb.inputs:renderProductPath"))
            connect.append(("RenderProduct.outputs:execOut", "cameraHelperRgb.inputs:execIn"))
            values.append(("cameraHelperRgb.inputs:frameId", "camMainView")) #TODO: do with config
            values.append(("cameraHelperRgb.inputs:topicName", self.cfg.rgb_topic))
            values.append(("cameraHelperRgb.inputs:type", "rgb"))
            values.append(("cameraHelperRgb.inputs:nodeNamespace", self.cfg.namespace))
            values.append(("cameraHelperRgb.inputs:useSystemTime", False)) # SImulation time
        # Pseudo-Depth publishing
        if self.cfg.enable_depth:
            nodes.append(("cameraHelperDepth", "isaacsim.ros2.bridge.ROS2CameraHelper"))
            connect.append(("RenderProduct.outputs:execOut", "cameraHelperDepth.inputs:execIn"))
            connect.append(("RenderProduct.outputs:renderProductPath", "cameraHelperDepth.inputs:renderProductPath"))
            values.append(("cameraHelperDepth.inputs:frameId", "camMainView"))
            values.append(("cameraHelperDepth.inputs:topicName", self.cfg.depth_topic))
            values.append(("cameraHelperDepth.inputs:type", "depth"))
            values.append(("cameraHelperDepth.inputs:nodeNamespace", self.cfg.namespace))
            values.append(("cameraHelperDepth.inputs:useSystemTime", False))
        # Camera Info Publishing
        if self.cfg.enable_info:
            nodes.append(("cameraHelperInfo", "isaacsim.ros2.bridge.ROS2CameraHelper"))
            connect.append(("RenderProduct.outputs:execOut", "cameraHelperInfo.inputs:execIn"))
            connect.append(("RenderProduct.outputs:renderProductPath", "cameraHelperInfo.inputs:renderProductPath"))
            values.append(("cameraHelperInfo.inputs:frameId", "camMainView"))
            values.append(("cameraHelperInfo.inputs:topicName", self.cfg.info_topic))
            values.append(("cameraHelperInfo.inputs:type", "camera_info"))
            values.append(("cameraHelperInfo.inputs:nodeNamespace", self.cfg.namespace))
            values.append(("cameraHelperInfo.inputs:useSystemTime", False))

        # create omnigraph
        keys = og.Controller.Keys
        _ = og.Controller.edit(
            {
                "graph_path": graph_name,
                "evaluator_name": "push",
                "pipeline_stage": og.GraphPipelineStage.GRAPH_PIPELINE_STAGE_ONDEMAND,
            },
            {
                keys.CREATE_NODES: nodes,
                keys.CONNECT: connect,
                keys.SET_VALUES: values,
            },
        )

class OmniGraphLidarTerm(OmniGraphTerm):
    """based on https://docs.omniverse.nvidia.com/py/isaacsim/source/extensions/isaacsim.ros2.bridge/docs/index.html"""
    def __init__(self, cfg: omnigraph_ros_cfg.OmniGraphLidarTermCfg, env: ManagerBasedEnv, env_idx: int = 0):
        super().__init__(cfg, env, env_idx)
        """Creates an Omnigraph graph to publish lidar data.

        It does this by reading in a OmniGraphCameraTermCfg that contains enables and
        parameters for different camera data modes. Currently supported outputs are RBG,
        Pseudo-depth, and camera info. When creating the OmniGraphCameraTermCfg take care to
        set the asset_cfg to a Camera created using a CameraCfg. When creating the CameraCfg
        be sure to set data_types=[]. This prevents duplicate RenderProducts used in the
        Synthetic Data Pipeline. No error will occur if data_types is non-empty, but
        performance will be slower.

        The Camera publish rate is controlled by the rendering rate of the simulation.
        """

    def _create_omnigraph(self):
        """Creates the configurable Camera Publisher Omnigraph"""

        prim_path = self._env.scene[self.cfg.asset_cfg.name].cfg.prim_path
        lidar_prim = prim_path.replace("env_.*", "env_" + str(self._env_idx))
        print("lidar prim_path", prim_path)
        print("lidar lidar_prim", lidar_prim)
        lidar_name = lidar_prim.split("/")[-1]
        print("lidar name", lidar_name)

        # define graph prim name
        if self.cfg.graph_name is None:
            graph_name = "/ActionGraph_" + lidar_name
        else:
            graph_name = self.cfg.graph_name

        # initialize nodes, connections, and values lists
        nodes = list()
        connect = list()
        values = list()

        # RTX Lidar Sensor

        # setup execution via Physics step NOTE: this does not control rate of execution for rendering products
        # setup Rendering Product basics
        nodes.append(("OnPhysicsStep", "isaacsim.core.nodes.OnPhysicsStep"))
        nodes.append(("RenderProduct", "isaacsim.core.nodes.IsaacCreateRenderProduct"))

        connect.append(("OnPhysicsStep.outputs:step", "RenderProduct.inputs:execIn"))

        values.append(("RenderProduct.inputs:cameraPrim", [usdrt.Sdf.Path(lidar_prim)]))

        # Point Cloud publishing
        if self.cfg.enable_point_cloud:
            nodes.append(("lidarhelperpointcloud", "isaacsim.ros2.bridge.ROS2RtxLidarHelper"))#ROS2CameraHelper
            
            connect.append(("RenderProduct.outputs:renderProductPath", "lidarhelperpointcloud.inputs:renderProductPath"))
            connect.append(("RenderProduct.outputs:execOut", "lidarhelperpointcloud.inputs:execIn"))
            
            values.append(("lidarhelperpointcloud.inputs:frameId", "os1_lidar")) #TODO: do with config
            values.append(("lidarhelperpointcloud.inputs:topicName", self.cfg.point_cloud_topic))
            values.append(("lidarhelperpointcloud.inputs:type", "point_cloud"))
            values.append(("lidarhelperpointcloud.inputs:nodeNamespace", self.cfg.namespace))
            values.append(("lidarhelperpointcloud.inputs:useSystemTime", False)) # SImulation time
            values.append(("lidarhelperpointcloud.inputs:fullScan", True))


        if self.cfg.enable_laser_scan:
            nodes.append(("lidarhelperlaserscan", "isaacsim.ros2.bridge.ROS2RtxLidarHelper"))#ROS2CameraHelper
            connect.append(("RenderProduct.outputs:renderProductPath", "lidarhelperlaserscan.inputs:renderProductPath"))
            connect.append(("RenderProduct.outputs:execOut", "lidarhelperlaserscan.inputs:execIn"))
            
            values.append(("lidarhelperlaserscan.inputs:frameId", "os1_lidar"))
            values.append(("lidarhelperlaserscan.inputs:topicName", self.cfg.laser_scan_topic))
            values.append(("lidarhelperlaserscan.inputs:type", "laser_scan"))
            values.append(("lidarhelperlaserscan.inputs:nodeNamespace", self.cfg.namespace))
            values.append(("lidarhelperlaserscan.inputs:useSystemTime", False))

        # PHYSX Lidar

        '''nodes.append(("OnPhysicsStep", "omni.isaac.core_nodes.OnPhysicsStep"))
        nodes.append(("readpointcloudnode", "omni.isaac.range_sensor.IsaacReadLidarPointCloud"))

        connect.append(("OnPhysicsStep.outputs:step", "readpointcloudnode.inputs:execIn"))
        values.append(("readpointcloudnode.inputs:lidarPrim", [usdrt.Sdf.Path(lidar_prim)]))# -> throws error Prim is not a Lidar Prim

        nodes.append(("publishpointcloud", "isaacsim.ros2.bridge.ROS2PublishPointCloud"))

        connect.append(("readpointcloudnode.outputs:execOut", "publishpointcloud.inputs:execIn"))
        connect.append(("readpointcloudnode.outputs:data", "publishpointcloud.inputs:data"))
        #
        values.append(("publishpointcloud.inputs:frameId", lidar_name))
        values.append(("publishpointcloud.inputs:topicName", lidar_name))
        values.append(("publishpointcloud.inputs:nodeNamespace", self.cfg.namespace))'''

        # create omnigraph
        keys = og.Controller.Keys
        _ = og.Controller.edit(
            {
                "graph_path": graph_name,
                "evaluator_name": "push",
                "pipeline_stage": og.GraphPipelineStage.GRAPH_PIPELINE_STAGE_ONDEMAND,
            },
            {
                keys.CREATE_NODES: nodes,
                keys.CONNECT: connect,
                keys.SET_VALUES: values,
            },
        )





    '''def _create_omnigraph(self):
        """Creates the configurable Lidar Publisher Omnigraph"""
        import omni.replicator.core as rep

        # Ensure the lidar prim path is valid
        lidar_path = self._env.scene[self.cfg.asset_cfg.name].cfg.prim_path

        # Create lidar post-process graph (RTX Lidar)
        #print(lidar_path)  # Check the camera/sensor's USD path
        hydra_texture = rep.create.render_product(lidar_path, [1, 1], name="Isaac")

        # Create Point cloud publisher pipeline in the post process graph
        writer = rep.writers.get("RtxLidar" + "ROS2PublishPointCloud")
        writer.initialize(topicName="point_cloud", frameId="sim_lidar")
        writer.attach([hydra_texture])
        # Create the debug draw pipeline in the post process graph
        writer = rep.writers.get("RtxLidar" + "DebugDrawPointCloud")
        writer.attach([hydra_texture])
        # Create LaserScan publisher pipeline in the post process graph
        writer = rep.writers.get("RtxLidar" + "ROS2PublishLaserScan")
        writer.initialize(topicName="scan", frameId="sim_lidar")
        writer.attach([hydra_texture])'''