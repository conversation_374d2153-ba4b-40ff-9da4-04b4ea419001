# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from isaaclab.utils import configclass

from moleworks_ext.ros.ros_manager.publishers_cfg import ClockPublisherCfg
from moleworks_ext.ros.ros_manager.subscribers_cfg import ResetSubscriberCfg


@configclass
class RosManagerCfg:
    clock_term: ClockPublisherCfg = ClockPublisherCfg()
    """Default ClockPublisher term."""
    reset_term: ResetSubscriberCfg = ResetSubscriberCfg()
    """Default ResetSubscriber term."""
    lockstep_timeout: float | None = None
    """Seconds to wait. Block forever if None or negative. Don’t wait if 0."""
