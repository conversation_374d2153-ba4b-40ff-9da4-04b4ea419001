# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import copy
import threading

import carb
import rclpy
import torch
from isaaclab.envs import ManagerBasedEnv
from moleworks_ext.ros.utils.environment import zero_actions
from rclpy.node import Node
from rclpy.parameter import Parameter
from std_srvs.srv import SetBool
from rclpy.qos import (
    Duration,
    QoSDurabilityPolicy,
    QoSHistoryPolicy,
    QoSProfile,
    QoSReliabilityPolicy,
    qos_profile_system_default
)

from .omnigraph_ros import OmniGraphTerm
from .omnigraph_ros_cfg import OmniGraphTermCfg
from .publishers import PublisherTerm
from .publishers_cfg import PublisherTermCfg
from .subscribers import SubscriberTerm
from .subscribers_cfg import SubscriberTermCfg

# Create a custom QoS profile better suited for real-time control
# Using RELIABLE reliability, KEEP_LAST history policy with depth 1 (we only care about the most recent message)
# and low latency settings (small deadlines)
QOS_PROFILE = QoSProfile(
    reliability=QoSReliabilityPolicy.BEST_EFFORT,
    durability=QoSDurabilityPolicy.VOLATILE,
    history=QoSHistoryPolicy.KEEP_LAST,
    depth=1,
    deadline=Duration(seconds=0.100)  # 100ms deadline
)

class RosManager:
    """Manager responsible for interfacing with external control inputs / output over ROS."""

    def __init__(self, cfg: object, env: ManagerBasedEnv, node_name: str = "ros_manager_node"):
        """Initialize the ros manager.

        Args:
            cfg: The configuration object.
            env: The ManagerBasedEnv environment.
            node_name: Name of the ROS node.
        """
        # Initialize rclpy if not already initialized
        if not rclpy.ok():
            rclpy.init()

        # Create shared ROS node with use_sim_time parameter set to True
        self._node = Node(
            node_name,
            parameter_overrides=[Parameter('use_sim_time', Parameter.Type.BOOL, True)]
        )

        # Store the inputs
        self.cfg = copy.deepcopy(cfg)
        self._env = env

        # Parse config to create terms information
        self._prepare_terms()
        
        # Set up asynchronous spinning
        self._spin_thread = None
        self._spin_thread_exit = False
        self._start_spin_thread()

    def __str__(self) -> str:
        """Returns a string representation for the ros manager."""
        return NotImplementedError

    def _spin_thread_fn(self):
        """Thread function that continuously spins the ROS node to process messages."""
        carb.log_info("ROS spin thread started")
        while not self._spin_thread_exit:
            # Process all available callbacks with a short timeout to avoid blocking
            rclpy.spin_once(self._node, timeout_sec=0.001)
        carb.log_info("ROS spin thread exited")

    def _start_spin_thread(self):
        """Start the ROS spinning thread."""
        self._spin_thread_exit = False
        self._spin_thread = threading.Thread(target=self._spin_thread_fn, daemon=True)
        self._spin_thread.start()
        carb.log_info("Started ROS spinning thread")

    def _stop_spin_thread(self):
        """Stop the ROS spinning thread."""
        if self._spin_thread is not None and self._spin_thread.is_alive():
            self._spin_thread_exit = True
            self._spin_thread.join(timeout=1.0)
            if self._spin_thread.is_alive():
                carb.log_warn("ROS spin thread did not exit cleanly, forcing termination")
            self._spin_thread = None

    def close(self):
        """Close the ros manager."""
        # Stop the ROS spinning thread
        self._stop_spin_thread()
        
        # Close publisher and subscriber terms
        for term in self.active_terms:
            term.close()

        # Delete all references to the environment
        del self._env

        # Shutdown ROS if not already shutdown
        rclpy.utilities.try_shutdown()

    @property
    def active_terms(self) -> list[PublisherTerm | SubscriberTerm | OmniGraphTerm]:
        """List of active ROS terms."""
        return self._sub_terms + self._pub_terms + self._og_terms

    def get_latest_messages(self, num_envs: int = 1):
        """Get the latest messages from subscribers.
        Note: With the continuous spinning thread, we no longer need to call
        rclpy.spin_once() here as messages are being processed in the background.
        """
        # No need to spin here anymore as the thread handles it
        # Simply get the latest message data from subscribers
        output = torch.concat([sub.msg_converted for sub in self._sub_terms])
        output = output.expand(num_envs, -1).to(self._env.device)
        return output

    def publish_static(self, obs: dict | None = None):
        """Publish static observations through ROS, (i.e., publishers with substep=None)"""
        for term in self._pub_terms:
            if term.cfg.substep is None:
                term.publish(obs)

    def publish(self, obs: dict):
        """Publish observations through ROS."""
        for term in self._pub_terms:
            if term.cfg.substep is not None:
                if term.cfg.substep < 1:
                    raise ValueError(
                        f"Continuous publisher substep must be greater than 1, received: {term.cfg.substep}"
                    )

                if self._env.sim.current_time_step_index % term.cfg.substep == 0:
                    # Publish each substep
                    term.publish(obs)

    def _prepare_terms(self):
        """Prepares a list of ROS terms."""
        # Initialize terms
        self._sub_terms_name: list[str] = list()
        self._sub_terms: list[SubscriberTerm] = list()
        self._pub_terms_names: list[str] = list()
        self._pub_terms: list[PublisherTerm] = list()
        self._og_terms_names: list[str] = list()
        self._og_terms: list[OmniGraphTerm] = list()
        self._option_terms_names: list[str] = list()
        self._option_terms: list[float | int | bool | str] = list()

        # Check if config is dict already
        if isinstance(self.cfg, dict):
            cfg_items = self.cfg.items()
        else:
            cfg_items = self.cfg.__dict__.items()

        for term_name, term_cfg in cfg_items:
            if term_cfg is None:
                continue
            
            try:
                if isinstance(term_cfg, SubscriberTermCfg):
                    term = term_cfg.class_type(term_cfg, self._node, self._env, qos_profile_system_default)
                    self._sub_terms.append(term)
                    self._sub_terms_name.append(term_name)
                    
                elif isinstance(term_cfg, PublisherTermCfg):
                    term = term_cfg.class_type(term_cfg, self._node, self._env, qos_profile_system_default)
                    self._pub_terms_names.append(term_name)
                    self._pub_terms.append(term)
                    
                elif isinstance(term_cfg, OmniGraphTermCfg):
                    term = term_cfg.class_type(term_cfg, self._env)
                    self._og_terms_names.append(term_name)
                    self._og_terms.append(term)
                    
                elif isinstance(term_cfg, (bool, float, int, torch.Tensor)):
                    self._option_terms_names.append(term_name)
                    self._option_terms.append(term_cfg)
                    
                else:
                    raise TypeError(
                        f"Invalid configuration type for term '{term_name}'. "
                        f"Expected SubscriberTermCfg, PublisherTermCfg, OmniGraphTermCfg, or primitive type. "
                        f"Received '{type(term_cfg).__name__}'"
                    )
                    
            except Exception as e:
                # Get the full traceback
                import traceback
                trace = ''.join(traceback.format_tb(e.__traceback__))
                
                # Create detailed error message based on term type
                if isinstance(term_cfg, SubscriberTermCfg):
                    term_type = "SubscriberTermCfg"
                elif isinstance(term_cfg, PublisherTermCfg):
                    term_type = "PublisherTermCfg"
                elif isinstance(term_cfg, OmniGraphTermCfg):
                    term_type = "OmniGraphTermCfg"
                else:
                    term_type = type(term_cfg).__name__

                err_msg = (
                    f"Failed to initialize {term_type} term '{term_name}':\n"
                    f"Error: {str(e)}\n"
                    f"Traceback:\n{trace}"
                )
                carb.log_error(err_msg)
                raise RuntimeError(err_msg) from e

    def reset_controller(self):
        """Call the high-level controller reset service via ROS."""
        self._node.get_logger().info("Requesting high-level controller reset...")
        client = self._node.create_client(SetBool, 'reset_controller')

        # Wait for the service to become available
        if not client.wait_for_service(timeout_sec=5.0):
            self._node.get_logger().error("Reset service 'reset_controller' not available!")
            return

        # Create the request message; 'data' set to True signals a reset request
        req = SetBool.Request()
        req.data = True

        # Call the service and wait for a response
        future = client.call_async(req)
        rclpy.spin_until_future_complete(self._node, future)

        if future.result() is not None:
            result = future.result()
            if result.success:
                self._node.get_logger().info(
                    f"High-level controller reset successful: {result.message}"
                )
            else:
                self._node.get_logger().warn(
                    f"High-level controller reset failed: {result.message}"
                )
        else:
            self._node.get_logger().error("Exception occurred during reset service call.")
