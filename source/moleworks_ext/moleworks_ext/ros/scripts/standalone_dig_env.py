# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

"""Launch Isaac Sim Simulator first."""

import argparse
import traceback

# update and parse AppLauncher arguments
from isaaclab.app import AppLauncher

parser = argparse.ArgumentParser(description="EvalSim Standalone")  # add argparse arguments
parser.add_argument(
    "--env",
    type=str,
    default="m545",
    choices=[
        "m545",
    ],
    help="Number of steps to take when profiling.",
)
parser.add_argument(
    "--sync_to_real_time",
    action="store_true",
    default=True,
    help="Limit sim execution to be no faster than real time.",
)
parser.add_argument(
    "--enable_sim_cameras",
    action="store_true",
    default=False,
    help="Enable cameras.",
)
AppLauncher.add_app_launcher_args(parser)  # append AppLauncher cli args
args_cli = parser.parse_args()  # parse the arguments
args_cli.enable_sim_cameras = True
args_cli.enable_cameras = args_cli.enable_sim_cameras
# args_cli.headless = True
#  launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import carb
from isaacsim.core.utils.extensions import enable_extension
enable_extension("isaacsim.ros2.bridge")
from moleworks_ext.ros.eval_sim.direct_eval_sim import ExcavationEvalSim
from moleworks_ext.ros.eval_sim.eval_sim_cfg import BaseEvalSimCfg
from moleworks_ext.ros.utils.env_cfg import update_env_cfg
from moleworks_ext.ros.ros_manager import RosManager
# menzi specific imports
from moleworks_ext.tasks.excavation.env_cfg.m545_env_deploy_cfg import M545EnvCfg
from moleworks_ext.tasks.excavation.excavation_env import ExcavationEnv
from moleworks_ext.ros.ros_manager.menzi.menzi_ros_manager_cfg import ExcavationRosManagerCfg


def main():
    """
    Main function that sets up and runs the excavation simulation with ROS integration.
    
    The ROS communication has been improved to:
    1. Process ROS messages continuously in a background thread
    2. Use a custom QoS profile optimized for real-time control 
    3. Avoid blocking the simulation loop while waiting for messages
    """
    # Create base eval sim config
    eval_sim_cfg = BaseEvalSimCfg(
        sync_to_real_time=args_cli.sync_to_real_time,
    )

    # Load and update environment config
    env_cfg = M545EnvCfg(enable_lidar=True)
    env_cfg.soil_height.z_min = -0.7049
    env_cfg.soil_height.z_max = -0.7050
    env_cfg.soil_height.theta = 0.0
    update_env_cfg(env_cfg)
    
    # Create environment
    env = ExcavationEnv(cfg=env_cfg)
    
    # Create ROS Manager with improved async message handling
    ros_cfg = ExcavationRosManagerCfg()
    ros_manager = RosManager(ros_cfg, env)
    
    # Create DirectEvalSim instance
    eval_sim = ExcavationEvalSim(eval_sim_cfg, env, ros_manager)

    # Run simulation loop - ROS messages are now processed continuously in the background
    while simulation_app.is_running() and not eval_sim.exit_requested:
        eval_sim.step()

    # Close the environment and ROS manager
    eval_sim.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
