# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause


from __future__ import annotations

"""Launch Isaac Sim Simulator first."""

import argparse
import os
from importlib import resources

# update and parse AppLauncher arguments
from isaaclab.app import AppLauncher
import sys



parser = argparse.ArgumentParser(description="EvalSim Standalone")  # add argparse arguments
parser.add_argument(
    "--env",
    type=str,
    default="m545",
    choices=[
        "m545",
    ],
    help="Number of steps to take when profiling.",
)
parser.add_argument(
    "--sync_to_real_time",
    action="store_true",
    default=True,
    help="Limit sim execution to be no faster than real time.",
)
parser.add_argument(
    "--enable_sim_cameras",
    action="store_true",
    default=False,
    help="Enable cameras.",
)
AppLauncher.add_app_launcher_args(parser)  # append AppLauncher cli args
args_cli = parser.parse_args()  # parse the arguments
args_cli.enable_sim_cameras = True
args_cli.enable_cameras = args_cli.enable_sim_cameras
args_cli.headless = False
#  launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import traceback

import carb
from isaaclab.managers import SceneEntityCfg
from isaacsim.core.utils.extensions import enable_extension
enable_extension("isaacsim.ros2.bridge")
from moleworks_ext.ros.eval_sim.direct_eval_sim import ExcavationEvalSim
from moleworks_ext.ros.eval_sim.eval_sim_cfg import BaseEvalSimCfg
from moleworks_ext.ros.utils.env_cfg import update_env_cfg
from moleworks_ext.ros.ros_manager import RosManager
# menzi specific imports
from moleworks_ext.tasks.single_boulder_excavation.env_cfg.deploy_boulder_cfg import M545EnvCfg


from moleworks_ext.tasks.excavation.excavation_env import ExcavationEnv
from moleworks_ext.tasks.single_boulder_excavation.excavation_env_boulder import ExcavationEnvBoulder
from moleworks_ext.ros.ros_manager.menzi.menzi_ros_manager_cfg import ExcavationBoulderRosManagerCfg
import torch


def main():
    # Create base eval sim config
    eval_sim_cfg = BaseEvalSimCfg(
        sync_to_real_time=args_cli.sync_to_real_time,
    )

    # Load and update environment config
    env_cfg = M545EnvCfg(enable_lidar=True)
    #env_cfg.soil_height.z_min = -0.7049
    #env_cfg.soil_height.z_max = -0.7050
    env_cfg.soil_parameters.type = "s_3_1"
    
    update_env_cfg(env_cfg)
    # Create environment
    env = ExcavationEnvBoulder(cfg=env_cfg)
    ros_cfg = ExcavationBoulderRosManagerCfg()
    ros_manager = RosManager(ros_cfg, env)
    # Create DirectEvalSim instance
    eval_sim = ExcavationEvalSim(eval_sim_cfg, env, ros_manager)
    # run simulation
    while simulation_app.is_running() and not eval_sim.exit_requested:
        eval_sim.step()
   
    # close the environment
    eval_sim.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        simulation_app.close()
