#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from rclpy.qos import (
    QoSProfile,
    QoSReliabilityPolicy,
    QoSHistoryPolicy,
    QoSDurabilityPolicy,
    Duration,
)
import numpy as np
from m545_highlevel_interfaces.msg import M545Measurements, M545ActuatorCommands
from m545_lowlevel_interfaces.msg import M545ActuatorCommand

# Define the same QOS profile as in ros_manager.py
QOS_PROFILE = QoSProfile(
    history=QoSHistoryPolicy.KEEP_LAST,
    depth=1,
    reliability=QoSReliabilityPolicy.RELIABLE,
    durability=QoSDurabilityPolicy.TRANSIENT_LOCAL,
    lifespan=Duration(seconds=1000),
)


class TestController(Node):
    def __init__(self):
        super().__init__('test_controller')
        
        # Define arm joints based on sim_cfg.py
        self.joints = ["J_<PERSON>UR<PERSON>", "J_<PERSON><PERSON><PERSON>", "<PERSON>_<PERSON><PERSON><PERSON>", "<PERSON>_<PERSON><PERSON><PERSON>", "<PERSON>_<PERSON><PERSON>_PITCH"]
        
        # Create publisher for commands
        self.command_publisher = self.create_publisher(
            M545ActuatorCommands,
            'm545_actuator_commands',
            QOS_PROFILE
        )

        # Create subscriber for measurements
        self.measurement_subscriber = self.create_subscription(
            M545Measurements,
            'm545_measurements',
            self.measurement_callback,
            QOS_PROFILE
        )

        # Create timer for publishing commands
        self.timer = self.create_timer(0.01, self.publish_commands)  # 100Hz
        
        self.get_logger().info("Test controller node initialized")

    def measurement_callback(self, msg: M545Measurements):
        """Callback for receiving measurements"""
        self.get_logger().info(
            f"Received measurements:\n"
            f"  Joint positions: {[cmd.position for cmd in msg.actuators]}\n"
            f"  Joint velocities: {[cmd.velocity for cmd in msg.actuators]}\n"
            f"  Joint torques: {[cmd.effort for cmd in msg.actuators]}"
        )

    def publish_commands(self):
        """Publish velocity commands limited to the arm joints."""
        msg = M545ActuatorCommands()
        
        # Create a list of actuator commands
        msg.actuators = []
        
        # Generate and assign velocity commands for arm joints
        for joint_name in self.joints:
            # Create individual actuator command
            actuator_cmd = M545ActuatorCommand()
            actuator_cmd.joint_name = joint_name
            actuator_cmd.mode = M545ActuatorCommand.MODE_JOINTVELOCITY
            
            # Generate random velocity between -1 and 1
            velocity = np.random.uniform(-1.0, 1.0)
            actuator_cmd.velocity = velocity
            
            # Add to the list of commands
            msg.actuators.append(actuator_cmd)
        
        self.command_publisher.publish(msg)
        self.get_logger().debug("Published joint velocity commands for arm")


def main(args=None):
    rclpy.init(args=args)
    test_controller = TestController()
    
    try:
        rclpy.spin(test_controller)
    except KeyboardInterrupt:
        pass
    finally:
        test_controller.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main() 