# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# Modified for Navigation Task

from __future__ import annotations

"""Launch Isaac Sim Simulator first."""

import argparse
import traceback
import sys
import os

# Ensure navigation environment components are importable
# Adjust this path if necessary based on your project structure
# sys.path.append(os.path.join(os.path.dirname(__file__), "..", "mole_environments", "nav")) # Example path addition

# update and parse AppLauncher arguments
from isaaclab.app import AppLauncher

parser = argparse.ArgumentParser(description="Navigation Standalone ROS Environment") # Updated description
# Add navigation specific arguments if needed, or adjust existing ones
parser.add_argument(
    "--num_envs",
    type=int,
    default=1, # Defaulting to 1 environment for standalone ROS control
    help="Number of environments to simulate."
)
parser.add_argument(
    "--sync_to_real_time",
    action="store_true",
    default=True,
    help="Limit sim execution to be no faster than real time.",
)
parser.add_argument(
    "--enable_sim_cameras",
    action="store_true",
    default=True, # Often needed for navigation (visual feedback, perception)
    help="Enable cameras.",
)
AppLauncher.add_app_launcher_args(parser)  # append AppLauncher cli args
args_cli = parser.parse_args()  # parse the arguments

# Ensure camera flags are consistent
args_cli.enable_cameras = args_cli.enable_sim_cameras
# args_cli.headless = True # Uncomment if running headless

# launch omniverse app
app_launcher = AppLauncher(args_cli)
simulation_app = app_launcher.app

"""Rest everything follows."""

import carb
from isaacsim.core.utils.extensions import enable_extension
enable_extension("isaacsim.ros2.bridge") # Ensure ROS bridge is enabled

# Core simulation and ROS components (assuming similar structure to digging)
from moleworks_ext.ros.eval_sim.direct_eval_sim import DirectEvalSim # CHANGED: Assumed class name
from moleworks_ext.ros.eval_sim.eval_sim_cfg import BaseEvalSimCfg
from moleworks_ext.ros.utils.env_cfg import update_env_cfg # Keep if needed for NavEnvCfg
from moleworks_ext.ros.ros_manager import RosManager

# Navigation specific imports (replace digging ones)
from moleworks_ext.tasks.driving.env_cfg.mole_nav_env_cfg import NavEnvCfg # CHANGED: Env Cfg
from moleworks_ext.tasks.sim.sim_env import MoleSimEnv # CHANGED: Env Class
from moleworks_ext.ros.ros_manager.menzi.menzi_ros_manager_cfg import M545RosManagerCfg # CHANGED: Assumed ROS Cfg class


def main():
    """
    Main function that sets up and runs the navigation simulation with ROS integration.
    Relies on NavigationEvalSim and RosManager to handle ROS communication and environment stepping.
    """
    # Create base eval sim config
    eval_sim_cfg = BaseEvalSimCfg(
        sync_to_real_time=args_cli.sync_to_real_time,
    )

    # Load and update environment config for navigation
    env_cfg = NavEnvCfg(enable_lidar=True)
    env_cfg.num_envs = args_cli.num_envs # Set number of environments from CLI args
    # Add any specific navigation env config modifications here if needed
    # e.g., env_cfg.terrain.difficulty = 0.5
    update_env_cfg(env_cfg) # Keep if your update_env_cfg handles NavEnvCfg

    # Create navigation environment
    # NOTE: Do not reset the env here, NavigationEvalim should handle lifecycleS
    env = MoleSimEnv(cfg=env_cfg) # CHANGED: Use Navigation Env Class

    # Create ROS Manager with navigation-specific configuration
    # This assumes NavigationRosManagerCfg defines topics like /cmd_vel, /odom, /scan etc.
    ros_cfg = M545RosManagerCfg() # CHANGED: Use Navigation ROS Cfg
    ros_manager = RosManager(ros_cfg, env) # Assumes RosManager is generic

    # Create NavigationEvalSim instance
    # This class is responsible for the main loop logic:
    # - Getting commands from ros_manager
    # - Calling env.step() with appropriate actions
    # - Handling resets, observations etc.
    eval_sim = DirectEvalSim(eval_sim_cfg, env, ros_manager) # CHANGED: Use Navigation EvalSim Class

    # Run simulation loop - ROS messages processed via RosManager/NavigationEvalSim
    while simulation_app.is_running() and not eval_sim.exit_requested:
        eval_sim.step() # NavigationEvalSim handles getting ROS commands and stepping env

    # Close the environment and ROS manager via EvalSim
    eval_sim.close()


if __name__ == "__main__":
    try:
        # run the main execution
        main()
    except Exception as err:
        carb.log_error(err)
        carb.log_error(traceback.format_exc())
        raise
    finally:
        # close sim app
        if simulation_app is not None:
            simulation_app.close()