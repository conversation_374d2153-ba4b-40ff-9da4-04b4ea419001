# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import torch
from isaaclab.envs import ManagerBasedEnv

from moleworks_ext.ros.eval_sim.eval_sim import Eval<PERSON><PERSON>, EvalSimCfg


class EvalSimStandalone(EvalSim):
    def __init__(self, cfg: EvalSimCfg) -> None:
        super().__init__(cfg)
        self.obs = {}

    def step_deployment(self):

        # run step in inference mode
        with torch.inference_mode():

            # publish observations through ROS
            self.ros_manager.publish(self.obs)

            # subscribe to action input through ROS
            action = self.ros_manager.subscribe()

            # rendering substep
            if self.cfg.render_substeps:
                render = self.step_counter % self.cfg.render_substeps == 0
            else:
                render = False

            # run ManagerBasedEnv step 
            self.obs, *_ = ManagerBasedEnv.step(self.env, action=action, render=render)
