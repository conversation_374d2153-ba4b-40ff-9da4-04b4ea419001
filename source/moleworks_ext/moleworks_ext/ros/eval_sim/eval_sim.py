# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import signal
from abc import abstractmethod
from collections import deque
from time import perf_counter, sleep

import torch
from isaaclab.envs import (
    ManagerBasedEnv,
    ManagerBasedEnvCfg,
    ManagerBasedRLEnv,
    ManagerBasedRLEnvCfg,
)

from moleworks_ext.ros.eval_sim.eval_sim_cfg import EvalSimCfg, CustomEnvEvalSimCfg
from moleworks_ext.ros.ros_manager import RosManager
from moleworks_ext.ros.utils import (
    find_env_cfgs_in_file,
    find_ros_manager_cfgs_in_file,
    import_class_dynamically,
    log_error,
    log_info,
    log_warn,
    zero_actions,
)


class EvalSim:
    def __init__(self, cfg: EvalSimCfg) -> None:
        # load inputs
        self.cfg = cfg

        # initialize environment and ros manager
        self.env_cfg = None
        self.env = None
        self.ros_manager_cfg = None
        self.ros_manager = None

        # load environment if configuration is given
        if cfg.env_cfg_path:
            self.set_env_cfg(cfg.env_cfg_path)
            self.load_env()

        # load ros manager if configuration is given
        if cfg.ros_manager_cfg_path:
            self.set_ros_manager_cfg(cfg.ros_manager_cfg_path)
            self.load_ros_manager()

        # initialize runtime variables
        self.step_counter = 0
        self.obs = {}
        self.wallclock_dt_buffer = deque(maxlen=cfg.wallclock_dt_buffer_size)
        self.t_prev = perf_counter()

        # action delay queue
        self.action_queue: list[torch.Tensor] = []

        # Register signal handler for SIGINT (Ctrl+C)
        signal.signal(signal.SIGINT, self.sigint_handler)

    """
    Sigint Handler
    """

    def sigint_handler(self, signum, frame):
        """Custom SigInt handling callback"""
        log_info("Ctrl+C received, running EvalSim cleanup code")
        self.close()

    """
    Core Functions
    """

    def step(self):
        # deployment specific step
        self.step_deployment()

        # profiling
        self.log_wallclock_time()

        # get the wallclock time of the last sim step
        sim_wallclock_dt = self.wallclock_dt_buffer[-1]
        if self.cfg.sync_to_real_time and sim_wallclock_dt < self.physics_dt:
            # sleep simulation loop if running faster than real time
            sleep(self.physics_dt - sim_wallclock_dt)

        # update counter
        self.step_counter += 1

    @abstractmethod
    def step_deployment(self):
        pass

    def reset(self):
        # reset environment
        if self.env is not None:
            self.env.reset()

        # reset timeline variables
        self.reset_timeline_variables()

    def close(self):
        """Close active EvalSim components (environment and ros manager)."""

        # close environment and simulation context
        if self.env is not None:
            try:
                self.env.close()
                self.env = None
            except Exception as e:
                log_error(f"Environment couldn't be closed. Exception: {e}")

        # close ros manager
        if self.ros_manager is not None:
            try:
                self.ros_manager.close()
                self.ros_manager = None
            except Exception as e:
                log_error(f"RosManager couldn't be shutdown. Exception: {e}")

    """
    Configuration and Loading
    """

    def load(self):
        self.load_env()
        self.load_ros_manager()

    def load_env(self):
        # load environment
        if isinstance(self.env_cfg, ManagerBasedRLEnvCfg):
            self.env = ManagerBasedRLEnv(self.env_cfg)
        elif isinstance(self.env_cfg, ManagerBasedEnvCfg):
            self.env = ManagerBasedEnv(self.env_cfg)
        else:
            raise ValueError(
                "Current set environment configuration is not of type ManagerBasedRLEnvCfg or ManagerBasedEnvCfg."
            )

        # prefill action buffer with zero_actions
        self.action_queue = [zero_actions(self.env) for _ in range(self.cfg.control_delay)]

    def load_ros_manager(self):
        if self.env is None:
            raise ValueError("Environment must exist to load a ros manager.")

        # load ros manager
        if self.ros_manager_cfg is not None:
            self.ros_manager = RosManager(self.ros_manager_cfg, self.env)
        else:
            log_warn("RosManager is not loaded, due to non-existent configuration.")

    def set_env_cfg(self, env_cfg_ref: str | type = ""):

        if env_cfg_ref == "":
            # empty path gets set to None
            self.env_cfg = None
        else:
            if isinstance(env_cfg_ref, type):
                self.env_cfg: ManagerBasedEnvCfg | ManagerBasedRLEnvCfg = env_cfg_ref()
                env_name = env_cfg_ref.__name__
                env_cfg_ref = str(env_cfg_ref)
            elif isinstance(env_cfg_ref, str):
                env_name = find_env_cfgs_in_file(env_cfg_ref)
                self.env_cfg: ManagerBasedEnvCfg | ManagerBasedRLEnvCfg = import_class_dynamically(
                    env_cfg_ref, env_name
                )()

            # update configuration for EvalSim
            self.update_env_cfg(self.env_cfg)

            # log loaded environment
            log_info(f"EnvCfg set ({env_name})")

        # save path
        self.cfg.env_cfg_path = env_cfg_ref

    def set_ros_manager_cfg(self, ros_manager_cfg_ref: str | type = ""):

        if ros_manager_cfg_ref == "":
            # empty path gets set to None
            self.ros_manager_cfg = None
        else:
            if isinstance(ros_manager_cfg_ref, type):
                self.ros_manager_cfg = ros_manager_cfg_ref()
                ros_manager_name = ros_manager_cfg_ref.__name__
                ros_manager_cfg_ref = str(ros_manager_cfg_ref)
            elif isinstance(ros_manager_cfg_ref, str):
                ros_manager_name = find_ros_manager_cfgs_in_file(ros_manager_cfg_ref)
                self.ros_manager_cfg: ManagerBasedEnvCfg | ManagerBasedRLEnvCfg = import_class_dynamically(
                    ros_manager_cfg_ref, ros_manager_name
                )()

            # log loaded environment
            log_info(f"RosManagerCfg set ({ros_manager_name})")

        # save path
        self.cfg.ros_manager_cfg_path = ros_manager_cfg_ref

    """
    Profiling
    """

    def log_wallclock_time(self):
        # append dt for computing simulation control speeds
        t_curr = perf_counter()
        dt = t_curr - self.t_prev
        self.wallclock_dt_buffer.append(dt)
        self.dt_avg_ms = 1000 * sum(self.wallclock_dt_buffer) / len(self.wallclock_dt_buffer)
        self.t_prev = t_curr

    def get_simulation_time_profile(self):
        # update simulation parameters if buffer is full
        if len(self.wallclock_dt_buffer) == self.wallclock_dt_buffer.maxlen:
            time_per_step = f"{self.dt_avg_ms:.3f} ms"
            time_per_step_hz = f"({1000.0 / self.dt_avg_ms:.0f} Hz)"
            sim_speed = f"{100 * (1000 * self.physics_dt) / self.dt_avg_ms:.2f}%" if self.dt_avg_ms > 0 else "N/A"
        else:
            time_per_step = "N/A"
            time_per_step_hz = "N/A"
            sim_speed = "N/A"
        return time_per_step, time_per_step_hz, sim_speed

    """
    Other
    """

    def reset_timeline_variables(self):
        # reset sim time and steps
        if self.env is not None:
            self.env.sim._timeline_timer_callback_fn(None)

        # reset wallclock
        self.wallclock_dt_buffer.clear()

    @property
    def physics_dt(self):
        """Ensures always using the environments physics dt."""
        if self.env is None:
            raise ValueError("Environment must exist for accessing physics_dt.")

        return self.env.physics_dt

    def set_physics_dt(self, dt: float):
        if self.env is None:
            raise ValueError("Environment must exist for setting physics_dt.")

        # update environment variable
        self.env.cfg.sim.dt = dt

        # update simulation
        self.env.sim.set_simulation_dt(physics_dt=dt)

        # inform user and reset clock
        log_info(f"Set physics_dt to {self.physics_dt:.4f}s")
        self.wallclock_dt_buffer.clear()

    def apply_delay_to_action_queue(self):
        if self.cfg.control_delay < 0:
            raise ValueError("Delay must be positive")

        diff_delay = self.cfg.control_delay - len(self.action_queue)
        if diff_delay > 0:
            for _ in range(diff_delay):
                if not self.action_queue:
                    self.action_queue.append(zero_actions(self.env))
                else:
                    self.action_queue.append(self.action_queue[-1])
        elif diff_delay < 0:
            for _ in range(-diff_delay):
                if self.action_queue:
                    self.action_queue.pop()
        else:
            pass

    def update_env_cfg(self, cfg: ManagerBasedEnvCfg):
        """Update an environment config to ensure it works and performs well with EvalSim.

        These modifications are necessary so that raw ManagerBasedEnvcfg and ManagerBasedRLEnvCfgs can be used seamlessly with eval sim.
        Most settings ensure higher performance over default settings, which is crucial for a good user experience.
        NOTE: The input config is modified in place.

        Args:
            cfg: The config to update.
        """

        # general adjustments
        cfg.scene.num_envs = 1
        cfg.ui_window_class_type = None

        # cpu deployment ensures higher performance over gpu when parallelization is not required
        cfg.sim.device = "cpu"
        cfg.sim.use_gpu_pipeline = False
        cfg.sim.physx.use_gpu = False

        # enable fabric
        cfg.sim.use_fabric = True

        # ensures enhanced determinism without much loss in performance (only available for cpu)
        cfg.sim.physx.enable_enhanced_determinism = True


import importlib.util


def import_class_from_file(file_path, class_name):
    """Utility function to import a class from a file dynamically."""
    spec = importlib.util.spec_from_file_location("module.name", file_path)
    module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module)
    cls = getattr(module, class_name)
    return cls


class CustomEnvEvalSim(EvalSim):
    def __init__(self, cfg: CustomEnvEvalSimCfg) -> None:
        super().__init__(cfg)
        self.env_path = cfg.env_path  # Store the environment path
        self.env_class = None  # Will hold the environment class
        self.obs = {}  # Initialize observations

    def set_env_cfg(self, env_cfg_ref: str | type = ""):
        """Set the environment configuration."""
        if env_cfg_ref == "":
            self.env_cfg = None
        else:
            if isinstance(env_cfg_ref, type):
                self.env_cfg = env_cfg_ref()
                env_cfg_name = env_cfg_ref.__name__
                env_cfg_ref = str(env_cfg_ref)
            elif isinstance(env_cfg_ref, str):
                # Assuming the class name in sim_cfg.py is MoleSimEnvCfg
                env_cfg_name = "MoleSimEnvCfg"
                print(env_cfg_ref)
                self.env_cfg = import_class_from_file(env_cfg_ref, env_cfg_name)()

            # Update configuration for EvalSim
            self.update_env_cfg(self.env_cfg)

            # Log loaded environment configuration
            log_info(f"EnvCfg set ({env_cfg_name})")

        # Save path
        self.cfg.env_cfg_path = env_cfg_ref

    def load_env(self):
        """Load the custom environment."""
        if self.env_path:
            # Assuming the class name in sim_env.py is MoleSimEnv
            self.env_class = import_class_from_file(self.env_path, self.cfg.env_class_name)
            # Instantiate the environment with the configuration
            self.env = self.env_class(cfg=self.env_cfg)
            log_info(f"Environment loaded: {self.cfg.env_class_name}")
        else:
            raise ValueError("Environment path is not provided.")

        # Prefill action buffer with zero actions
        self.action_queue = [zero_actions(self.env) for _ in range(self.cfg.control_delay)]

    def step_deployment(self):
        """Perform a simulation step."""
        with torch.inference_mode():
            # Publish observations through ROS
            self.ros_manager.publish(self.obs)

            # Subscribe to action input through ROS
            action = self.ros_manager.subscribe()

            # Handle rendering
            if self.cfg.render_substeps:
                render = self.step_counter % self.cfg.render_substeps == 0
            else:
                render = False

            # Run environment step
            self.obs, *_ = self.env.step(action=action, render=render)