# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

import gc
import json
import os

import omni
import isaacsim.core.utils.stage as stage_utils
import omni.physx
from omni import usd
from isaaclab.envs import ManagerBasedRLEnv
from isaaclab.envs.ui import ViewportCameraController
from isaaclab.sim import SimulationContext
from omni.timeline import get_timeline_interface
from pxr import UsdUtils

from moleworks_ext.ros.eval_sim import EvalSim, EvalSimCfg
from moleworks_ext.ros.utils import (
    USER_SETTINGS_PATH,
    log_info,
    log_warn,
    random_actions,
    zero_actions,
)
from moleworks_ext.ros.utils.video_recorder import VideoRecorder, VideoRecorderCfg


class EvalSimGUI(EvalSim):
    def __init__(self, cfg: EvalSimCfg) -> None:
        super().__init__(cfg)

        # runtime variables
        self.video_recorder = None
        self._ros_enabled = False
        self._prev_ros_enable = False
        self._prev_control_delay = cfg.control_delay
        self._random_actions = False  # use zeros by default
        self.timeline = get_timeline_interface()

    """
    Core Functions
    """

    def step_deployment(self) -> None:
        """Updates the simulation for each step."""
        # do not step if environment does not exist or the sim is stopped
        if self.env is None or self.env.sim.is_stopped():
            return

        # update buffers
        self.env.scene.update(dt=self.physics_dt)

        # compute observations
        obs = self.env.observation_manager.compute()

        # update actions for every control substep
        if (
            self.env.sim.current_time_step_index % self.env_cfg.decimation == 0
        ):  # NOTE: control substeps is called decimation in Isaac Lab
            # get actions
            if self.ros_manager and self._ros_enabled:
                # no delay
                if self.cfg.control_delay == 0:
                    self.ros_manager.publish(obs)
                    self.action_queue.append(self.ros_manager.subscribe())
                # time delay
                elif self.cfg.control_delay > 0:
                    self.time_delay_actions()
                    self.ros_manager.publish(obs)
            else:
                actions = random_actions(self.env) if self._random_actions else zero_actions(self.env)
                self.action_queue.append(actions)

            # pop first action in queue
            actions = self.action_queue.pop(0)
            self.env.action_manager.process_action(actions)

            # keep track of previous ros_enable state
            self._prev_ros_enable = self._ros_enabled
            self._prev_control_delay = self.cfg.control_delay

        # apply actions to scene
        self.env.action_manager.apply_action()
        self.env.scene.write_data_to_sim()

        # step video recorder
        self.video_recorder.step()

    async def clear_async(self):
        """Closes the environment, creates a new scene and resets the simulator"""
        # close EvalSim (environment, ros manager and simulation context)
        self.close()

        # create a new stage to clear the scene
        await stage_utils.create_new_stage_async()

        # reset timeline variables
        self.reset_timeline_variables()

    async def pause(self):
        if self.env is not None:
            await self.env.sim.pause_async()

    """
    Configuration and Loading
    """

    async def load_env_async(self):
        """Loads the configuration and initializes the simulation context."""
        sim_context = SimulationContext(self.env_cfg.sim)

        # this method is intended to be used in the Isaac Sim's Extensions workflow where the Kit application has the
        # control over timing of physics and rendering steps
        await sim_context.initialize_simulation_context_async()

        # load the environment
        self.load_env()

        # update the current USD stage asynchronously
        await stage_utils.update_stage_async()
        # reset the simulator
        # for the first time, we need to reset the simulator to initialize the scene and the agent.
        # after that, the buttons need to be used.
        # note: this plays the simulator which allows setting up all the physics handles.
        # NOTE: This line is the source of regex matching errors that occur when a different EnvCfg gets loaded for the
        # second time. There seems to be something fishy with clearing environments, somehow the previous
        # ArticulationCfg gets persisted to the second environment.
        # NOTE: The problem seems to be coming from an ISubscription function being set in for self._initialize_handle
        # in isaaclab.assets.asset_base.AssetBase. There is a weakref.proxy on this function and it seems
        # to be called even after ManagerBasedEnv.close(), which should take care of unsubscribing to this function.
        # When running in debug mode, this error does not appear
        await self.env.sim.reset_async(soft=False)

        # pause the simulator and wait for user to click play

        await self.env.sim.pause_async()

        # Initialize the camera viewport
        if self.env.sim.render_mode >= self.env.sim.RenderMode.PARTIAL_RENDERING:
            self.env.viewport_camera_controller = ViewportCameraController(self.env, self.env.cfg.viewer)
        else:
            self.env.viewport_camera_controller = None

        # load managers
        self.env.load_managers()

        # only for RL tasks
        if isinstance(self.env, ManagerBasedRLEnv):
            self.env._configure_gym_env_spaces()
            self.env.event_manager.apply(mode="startup")

        # load the ros manager
        self.load_ros_manager()

        # initialize video recorder
        self.video_recorder = VideoRecorder(self.env.sim, VideoRecorderCfg())

        # reset timeline variables
        self.reset_timeline_variables()

        # step environment for visualizing initial position, rather than raw usd state
        await self.step_async()

    """
    Timeline
    """

    def stop(self) -> None:
        """Stops the simulation.

        This function handles the cleanup of the simulation context and the stage.
        """
        # check if simulation context exists
        if self.env.sim is not None:
            # stop the simulation
            if not self.env.sim.is_stopped():
                self.env.sim.stop()
            # clear TODO: check if this is necessary here
            self.env.sim.clear()
            # clear all callbacks
            self.env.sim.clear_all_callbacks()
            # clear simulation
            self.env.sim.clear_instance()
            # collect garbage
            gc.collect()

        # close environment
        self.env.close()

        # close ros manager
        self.ros_manager.close()

    async def step_async(self, nr_steps: int = 1):

        # Get simulation / fabric interfaces and add the stage_id
        stage_id = UsdUtils.StageCache.Get().Insert(usd.get_context().get_stage()).ToLongInt()
        iphysx_sim = omni.physx.get_physx_simulation_interface()
        iphysx_fc = omni.physxfabric.get_physx_fabric_interface()
        iphysx_fc.attach_stage(stage_id)

        # step environment
        if self.env is not None:
            for _ in range(nr_steps):
                # pause simulation
                if self.timeline.is_playing():
                    self.timeline.pause()

                # Asynchronous step function from omni.physxui.physxDebugView (stripped to include relevant parts).
                self.timeline.set_current_time(self.timeline.get_current_time() + self.env.physics_dt)

                # Update fabric
                iphysx_sim.simulate(self.env.physics_dt, 0.0)
                iphysx_sim.fetch_results()
                iphysx_fc.update(self.env.physics_dt, 0.0)

                # render simulation (required for visualizing multiple steps)
                await self.env.sim.render_async()

        # reset wallclock
        self.wallclock_dt_buffer.clear()

    async def reset_async(self):
        # reset environment
        self.reset()

        # pause
        await self.pause()

        # take one sim step to visualize reset
        await self.step_async()

    def time_delay_actions(self):
        # transition: skip first subscription after ros enabling
        if not self._prev_ros_enable:  # noqa
            self.action_queue.append(self.action_queue[-1])
        # transition: skip first subscription after increasing delay skip subscription
        elif self._prev_control_delay < self.cfg.control_delay:
            self.action_queue.append(self.action_queue[-1])
        # transition: decreasing delay
        elif self._prev_control_delay > self.cfg.control_delay:
            self.action_queue.append(self.ros_manager.subscribe())
        # non zero delay constant value
        else:
            self.action_queue.append(self.ros_manager.subscribe())

    """
    Video Recording
    """

    def start_recording(self):
        if self.video_recorder is not None:
            self.video_recorder.start_recording()
        else:
            log_warn("Video recorder is not loaded. Can not start / stop recording.")

    def stop_recording(self):
        if self.video_recorder is not None:
            self.video_recorder.stop_recording()
        else:
            log_warn("Video recorder is not loaded. Can not start / stop recording.")

    """
    User Settings
    """

    def load_user_settings(self) -> dict:
        """Loads the user settings from json.

        See :func:`save_user_settings` for the structure of the user settings.

        Returns:
            The user settings as a dictionary.
        """
        # set default user settings
        user_settings = {"env_cfg_path": "", "ros_manager_cfg_path": "", "enable_ros": False, "auto_load": False}

        # open the JSON file and load its contents into a Python dictionary
        if os.path.exists(str(USER_SETTINGS_PATH)):
            with open(str(USER_SETTINGS_PATH)) as file:
                user_settings_from_file = json.load(file)

            # form user settings from file
            for k, v in user_settings_from_file.items():
                print(f"Loading {k}: {v} from user settings.")
                if k in ["enable_ros", "auto_load"]:
                    user_settings[k] = v
                else:
                    if v.startswith("<class"):
                        user_settings[k] = v
                    else:
                        # only need to check if file exists for paths
                        user_settings[k] = "" if not os.path.exists(v) else v
        else:
            log_warn("User settings file does not exist. Using default settings.")

        print(f"Loaded user settings: {user_settings}")

        return user_settings

    def save_user_settings(self, auto_load: False) -> None:
        """Saves the user settings as json.

        Args:
            auto_load: Whether to update user_settings to automatically load the last saved scene.

        """
        # convert user settings to dictionary
        user_settings = {
            "env_cfg_path": str(self.cfg.env_cfg_path),
            "ros_manager_cfg_path": str(self.cfg.ros_manager_cfg_path),
            "enable_ros": self._ros_enabled,
            "auto_load": auto_load,
        }

        print(f"Saving user settings: {user_settings}")

        # writing the user settings to a JSON file
        with open(str(USER_SETTINGS_PATH), "w") as file:
            json.dump(user_settings, file, indent=4)

    """
    Properties
    """

    @property
    def ros_enabled(self) -> bool:
        return self._ros_enabled

    """
    Other
    """

    def set_debug_vis(self, debug_vis: bool) -> None:
        """This function is used to toggle the debug visualization of the simulator."""
        if self.env:
            # visualize sensors
            for sensor_name, sensor in self.env.scene.sensors.items():
                log_warn(f"Setting {sensor_name} to {debug_vis}")
                sensor.set_debug_vis(debug_vis)

            # visualize commands
            command_manager = getattr(self.env, "command_manager", None)
            if command_manager is not None:
                command_manager.set_debug_vis(debug_vis)
        else:
            log_warn("Toggling debug visualization ineffective. Environment has not been loaded.")

    def enable_ros(self) -> None:
        """Enables the ROS manager."""
        self._ros_enabled = True
        log_info("ROS is enabled.")

    def disable_ros(self) -> None:
        """Disables the ROS manager."""
        self._ros_enabled = False
        log_info("ROS is disabled.")

    def set_random_actions(self, case: bool) -> None:
        self._random_actions = bool(case)
