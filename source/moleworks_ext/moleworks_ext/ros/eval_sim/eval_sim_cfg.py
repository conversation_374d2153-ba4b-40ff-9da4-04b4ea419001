# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import enum

from isaaclab.utils import configclass


class ExecutionMode(enum.IntEnum):
    """Whether to run EvalSim in a lockstep fashion, or asynchronously."""

    BLOCKING: int = 0
    NON_BLOCKING: int = 1


@configclass
class BaseEvalSimCfg:
    execution_mode: ExecutionMode = ExecutionMode.BLOCKING
    render_substeps: int = 4  # no rendering if set to zero
    control_substeps: int = 1  # same parameter as decimation in Isaac Lab
    control_delay: int = 0
    sync_to_real_time: bool = False  # limit sim execution to be no faster than real time


@configclass
class EvalSimCfg(BaseEvalSimCfg):
    # environment / ros manager settings
    env_cfg_path: str = ""
    ros_manager_cfg_path: str = ""

    # profiling
    wallclock_dt_buffer_size: int = 200

    # importing environment / ros manager configurations
    search_pkgs_for_cfgs: list = [
        # add packages from which you would like to import environment or ros_manager cfg's
        # NOTE: these packages need to be pip installed to be used.
        "rai.eval_sim",
        # "isaaclab_tasks",  # NOTE: contains a lot of envs, you might not want them all to be loaded.
    ]


@configclass
class CustomEnvEvalSimCfg(EvalSimCfg):
    """
    Extended configuration for EvalSim with an additional environment path.

    Attributes:
        env_path (str): Path to the environment script.
    """
    env_path: str = ""
    env_class_name: str = ""

