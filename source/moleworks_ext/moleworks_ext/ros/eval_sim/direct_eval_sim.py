# Copyright (c) 2022-2024, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import signal
from collections import deque
from time import perf_counter, sleep

import rclpy
import torch
from isaaclab.envs import ManagerBasedEnv
import carb

from moleworks_ext.ros.eval_sim.eval_sim_cfg import BaseEvalSimCfg
from moleworks_ext.ros.ros_manager import RosManager
from moleworks_ext.ros.utils import log_error, log_info


class DirectEvalSim:
    """A simplified version of EvalSim that takes pre-initialized environment and ROS manager."""
    
    def __init__(self, cfg: BaseEvalSimCfg, env: ManagerBasedEnv, ros_manager: RosManager) -> None:
        # store inputs
        self.cfg = cfg
        self.env = env
        self.ros_manager = ros_manager
        
        # initialize runtime variables
        self.step_counter = 0
        self.obs = {}
        self.wallclock_dt_buffer = deque(maxlen=200)  # Default buffer size
        self.t_prev = perf_counter()

        # Register signal handler for SIGINT (Ctrl+C)
        self.exit_requested = False
        signal.signal(signal.SIGINT, self.sigint_handler)
        print("DirectEnvSim initialized")

    def sigint_handler(self, signum, frame):
        """Custom SigInt handling callback"""
        log_info("Ctrl+C received, running DirectEnvSim cleanup code")
        rclpy.shutdown()  # Add this line to shutdown ROS
        self.exit_requested = True  # Set the exit flag

    def step(self):
        """Perform a simulation step."""
        with torch.inference_mode():
            # Start timing for this step
            step_start_time = perf_counter()
            
            # Publish observations through ROS first (observations from previous step)
            self.ros_manager.publish(self.obs)
            
            # Subscribe to action input through ROS - this will process all pending messages
            # to ensure we get the most recent command
            action = self.ros_manager.get_latest_messages()
            
            # Extract arm action
            # arm_action = action[:, self.env.m545_measurements.arm_joint_ids]
            
            # Handle rendering if configured
            if self.cfg.render_substeps:
                # Render every N steps based on configuration
                if self.step_counter % self.cfg.render_substeps == 0:
                    # If we had rendering code, it would go here
                    pass

            # Run environment step
            print("action", action)
            self.obs, extras = self.env.step(action=action)
            
            # If the environment has signalled termination (should only be triggered for one environment)
            # then call reset. If reset_terminated comes as a tensor, use .item() to get the Python bool.
            # if isinstance(reset_terminated, torch.Tensor):
            #     if reset_terminated.item():
            #         self.reset()
            # elif reset_terminated:
            #     self.reset()
                
            # End timing for this step
            step_end_time = perf_counter()
            step_duration = step_end_time - step_start_time
            
            carb.log_info(f"Completed step {self.step_counter} in {step_duration:.4f}s, "
                         f"sim_time={self.step_counter * self.physics_dt * self.cfg.control_substeps:.4f}")
            
        # Profiling: log wallclock time of the sim step
        self.log_wallclock_time()

        # Get the wallclock time of the last simulation step
        sim_wallclock_dt = self.wallclock_dt_buffer[-1]
        
        # Calculate target step time based on physics timestep
        target_step_time = self.physics_dt
        
        # If we're syncing to real time and we're running faster than desired rate, sleep to maintain timing
        if self.cfg.sync_to_real_time and sim_wallclock_dt < target_step_time:
            sleep_time = target_step_time - sim_wallclock_dt
            carb.log_info(f"Step completed faster than real-time, sleeping for {sleep_time:.4f}s")
            sleep(sleep_time)

        # Update simulation step counter
        self.step_counter += 1

    def reset(self):
        """Reset the environment and timeline variables."""
        # reset environment
        if self.env is not None:
            self.env.reset()

        # reset timeline variables
        # self.reset_timeline_variables()
        
        # Also call the high-level controller reset service via the ROS manager
        if self.ros_manager is not None:
            # print("resetting controller")
            self.ros_manager.reset_controller()

    def close(self):
        """Close active DirectEnvSim components."""
        # close environment and simulation context
        if self.env is not None:
            try:
                self.env.close()
                self.env = None
            except Exception as e:
                log_error(f"Environment couldn't be closed. Exception: {e}")

        # close ros manager
        if self.ros_manager is not None:
            try:
                self.ros_manager.close()
                self.ros_manager = None
            except Exception as e:
                log_error(f"RosManager couldn't be shutdown. Exception: {e}")

    def log_wallclock_time(self):
        """Log wallclock time for profiling."""
        t_curr = perf_counter()
        dt = t_curr - self.t_prev
        self.wallclock_dt_buffer.append(dt)
        self.dt_avg_ms = 1000 * sum(self.wallclock_dt_buffer) / len(self.wallclock_dt_buffer)
        self.t_prev = t_curr

    def reset_timeline_variables(self):
        """Reset timeline variables."""
        # reset sim time and steps
        if self.env is not None:
            self.env.sim._timeline_timer_callback_fn(None)

        # reset wallclock
        self.wallclock_dt_buffer.clear()

    @property
    def physics_dt(self):
        """Get the environment's physics timestep."""
        if self.env is None:
            raise ValueError("Environment must exist for accessing physics_dt.")
        return self.env.physics_dt 


class ExcavationEvalSim(DirectEvalSim):
    def __init__(self, cfg: BaseEvalSimCfg, env: ManagerBasedEnv, ros_manager: RosManager) -> None:
        super().__init__(cfg, env, ros_manager)

    def step(self):
        """Perform a simulation step."""
        with torch.inference_mode():
            # Start timing for this step
            step_start_time = perf_counter()
            
            # Publish observations through ROS first (observations from previous step)
            self.ros_manager.publish(self.obs)
            
            # Subscribe to action input through ROS - this will process all pending messages
            # to ensure we get the most recent command
            action = self.ros_manager.get_latest_messages()
            
            # Extract arm action
            arm_action = action[:, self.env.m545_measurements.arm_joint_ids]
            
            # Handle rendering if configured
            if self.cfg.render_substeps:
                # Render every N steps based on configuration
                if self.step_counter % self.cfg.render_substeps == 0:
                    # If we had rendering code, it would go here
                    pass

            # Run environment step
            self.obs, rew, reset_terminated, reset_timeout, extras = self.env.step(action=arm_action)
            
            # If the environment has signalled termination (should only be triggered for one environment)
            # then call reset. If reset_terminated comes as a tensor, use .item() to get the Python bool.
            if isinstance(reset_terminated, torch.Tensor):
                if reset_terminated.item():
                    self.reset()
            elif reset_terminated:
                self.reset()
                
            # End timing for this step
            step_end_time = perf_counter()
            step_duration = step_end_time - step_start_time
            
            # print(f"Completed step {self.step_counter} in {step_duration:.4f}s, "
            #       f"sim_time={self.step_counter * self.physics_dt * self.cfg.control_substeps:.4f}")
            
        # Profiling: log wallclock time of the sim step
        self.log_wallclock_time()

        # Get the wallclock time of the last simulation step
        sim_wallclock_dt = self.wallclock_dt_buffer[-1]
        
        # Calculate target step time based on physics timestep
        target_step_time = self.physics_dt
        
        # If we're syncing to real time and we're running faster than desired rate, sleep to maintain timing
        if self.cfg.sync_to_real_time and sim_wallclock_dt < target_step_time:
            sleep_time = target_step_time - sim_wallclock_dt
            carb.log_info(f"Step completed faster than real-time, sleeping for {sleep_time:.4f}s")
            sleep(sleep_time)

        # Update simulation step counter
        self.step_counter += 1