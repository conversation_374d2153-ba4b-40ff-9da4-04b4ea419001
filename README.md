# Moleworks Extension for IsaacLab
[![<PERSON><PERSON><PERSON>](https://img.shields.io/badge/IsaacSim-4.2.0-silver.svg)](https://docs.omniverse.nvidia.com/isaacsim/latest/overview.html)
[![Isaac Lab](https://img.shields.io/badge/IsaacLab-1.2.0-silver)](https://isaac-sim.github.io/IsaacLab)
[![Python](https://img.shields.io/badge/python-3.10-blue.svg)](https://docs.python.org/3/whatsnew/3.10.html)
[![Linux platform](https://img.shields.io/badge/platform-linux--64-orange.svg)](https://releases.ubuntu.com/20.04/)
[![Windows platform](https://img.shields.io/badge/platform-windows--64-orange.svg)](https://www.microsoft.com/en-us/)
[![pre-commit](https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit&logoColor=white)](https://pre-commit.com/)
[![License](https://img.shields.io/badge/license-MIT-yellow.svg)](https://opensource.org/license/mit)

## Overview

This repository serves as a template for building projects or extensions based on Isaac Lab. It allows you to develop in an isolated environment, outside of the core Isaac Lab repository.

**Key Features:**

- `Isolation` Work outside the core Isaac Lab repository, ensuring that your development efforts remain self-contained.
- `Flexibility` This template is set up to allow your code to be run as an extension in Omniverse.

**Keywords:** extension, template, isaaclab

## Installation
We reccomend to use our docker containers. Check in the docker folder for further instructions then you can proceede to test the installation. 

- Verify that the extension is correctly installed by running the following command:

```bash
python scripts/rsl_rl/train.py --task=Isaac-m545-digging
```

### Set up IDE (Optional)

To setup the IDE, please follow these instructions:

- Run VSCode Tasks, by pressing `Ctrl+Shift+P`, selecting `Tasks: Run Task` and running the `setup_python_env` in the drop down menu. When running this task, you will be prompted to add the absolute path to your Isaac Sim installation.

If everything executes correctly, it should create a file .python.env in the `.vscode` directory. The file contains the python paths to all the extensions provided by Isaac Sim and Omniverse. This helps in indexing all the python modules for intelligent suggestions while writing code.

## Usage

This guide outlines how to set up tasks, configure agents, and create specialized environments for a robotics or reinforcement learning system. The key points are as follows:

### Tasks and Agent Configuration
- **Task Creation:**  
  - Tasks are defined per requirement.
  - A new task can be created in `source/moleworks_ext/moleworks_ext/tasks` to suit your needs.
  
- **Agent Definition:**  
  - Within each task, an agent (in this case, a PPO agent) is defined through its configuration in `source/moleworks_ext/moleworks_ext/tasks/'task_name'/agents`.

### Main Environment Configuration
- **Core Settings:**  
  - The main environment configuration in `source/moleworks_ext/moleworks_ext/tasks/'task_name'/env_cfg` handles most settings.
  - You define crucial parameters such as:
    - The robot’s properties.
    - Which sensors are used, whether raycasting is used.
    - Reward configurations.
    - Observation configurations.
    - These link to the functions in the MDP (Markov Decision Process) for observations, rewards, and terminations.

- **Environment Specifics:**  
  - Environment defined in a python file, for example `source/moleworks_ext/moleworks_ext/tasks/single_boulder_excavation/excavation_env_boulder.py`
  - Each environment is derived from a base (e.g., `ManagerBaseRLEnv`),
  - Measurements and physics updates are initialized here.
  - A minimal working version is `source/moleworks_ext/moleworks_ext/tasks/sim/sim_env.py` simply spawns a robot and updates its measurements.

### Folder Structure and Common Components
- **Directory Organization:**  
  - All environment-related files are kept in the dedicated folder (`source/moleworks_ext/moleworks_ext/tasks/'task_name'/`).
  
- **Action Controllers and Sensors:**  
  - Common utils in `source/moleworks_ext/moleworks_ext/common`
  - Common action controllers (like velocity controllers) are defined in configuration files.
  - A separate sensor configuration file handles sensor setups (e.g., for raycasting in the boulder environment) and defines custom sensor behaviors.

### Scripts and Testing Methods
- **Environment Scripts:**  
  - Scripts exist to ensure the environment is registered correctly `scripts/list_envs.py`.
  - In `scripts/rl/rsl_rl`, scripts support training a policy and playing with the environment, including running a “zero agent” mode (an agent that simply tests environment functionality without active behavioral input).
  - This mode serves as an initial check where the environment stands alone without active intervention. It helps verify basic functionality before moving into more complex setups.
  
- **Gamepad Integration:**
  - `scripts/mole_environments/excavation/gamepad.py`.
  - While there is support for gamepad inputs (Xbox, Logitech), some features (e.g., cabin controls) may need further testing or adaptations.

### Benchmarks and Specialized Testing
- **Benchmark Scripts:**  
  - Specialized benchmarks (e.g., for the excavation environment `scripts/mole_environments/excavation/benchmark_excavation.py`).
  - These tests ensure:
    - The environment resets correctly without errors like the robot initializing under the soil (`scripts/mole_environments/excavation/test_reset.py`).
    - Controllers (such as the PID) are properly tuned.
  
- **Trajectory Tracking and Command Validation:**  
  - `scripts/standalone`.
  - There are scripts designed to track trajectories and assess whether the robot’s velocity commands are executed as intended.
  - These tests also help in confirming that tracking (for example, of a cabin) remains robust even if delays occur.

### Raycasting and Deployment Considerations
- **Raycaster Configuration:**  
  - The raycaster is a custom sensor that returns messages (with its data used during real deployments).
  - Even though some data isn’t essential for every deployment, it is integrated for accurate real-simulation scenarios.
  
- **Real vs. Simulation Modes:**  
  - Certain configurations (like student policies or real simulation parameters) can be toggled depending on whether you’re deploying with full simulation or a simplified mode.

### Additional Insights

This system is designed to be modular and flexible:
- **Modularity:**  
  Developers can customize almost every aspect—from how the robot is spawned to how sensors and controllers interact with the environment.
  
- **Testing Focus:**  
  Extensive scripts are provided to check for errors (like improper resets or control delays), ensuring robustness in both simulation and real-world deployments.
  
- **Adapting to New Requirements:**  
  The structure facilitates the addition of custom sensors, alternative measurement files for different robots, and support for different control methods including game controllers.


## Troubleshooting

### Pylance Missing Indexing of Extensions

In some VsCode versions, the indexing of part of the extensions is missing. In this case, add the path to your extension in `.vscode/settings.json` under the key `"python.analysis.extraPaths"`.

```json
{
    "python.analysis.extraPaths": [
        "<path-to-ext-repo>/source/moleworks_ext"
    ]
}
```
### Pylance Crash

If you encounter a crash in `pylance`, it is probable that too many files are indexed and you run out of memory.
A possible solution is to exclude some of omniverse packages that are not used in your project.
To do so, modify `.vscode/settings.json` and comment out packages under the key `"python.analysis.extraPaths"`
Some examples of packages that can likely be excluded are:

```json
"<path-to-isaac-sim>/extscache/omni.anim.*"         // Animation packages
"<path-to-isaac-sim>/extscache/omni.kit.*"          // Kit UI tools
"<path-to-isaac-sim>/extscache/omni.graph.*"        // Graph UI tools
"<path-to-isaac-sim>/extscache/omni.services.*"     // Services tools
...
```
